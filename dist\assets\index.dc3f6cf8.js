import {
  r as u,
  C as A,
  ax as B,
  v as G,
  j as C,
  e as J,
  a as p,
  f as ee,
  O as M,
  F as re,
  w as te,
} from './index.7dafa16d.js';
import { T as ae } from './b-tween.es.d368a2a1.js';
import { p as ne, g as L, a as W } from './pad.af73d6a9.js';
import { S as ie } from './index.43d26da3.js';
var le =
    (globalThis && globalThis.__read) ||
    function (e, i) {
      var a = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!a) return e;
      var t = a.call(e),
        n,
        l = [],
        o;
      try {
        for (; (i === void 0 || i-- > 0) && !(n = t.next()).done; )
          l.push(n.value);
      } catch (r) {
        o = { error: r };
      } finally {
        try {
          n && !n.done && (a = t.return) && a.call(t);
        } finally {
          if (o) throw o.error;
        }
      }
      return l;
    },
  oe = [
    ['Y', 1e3 * 60 * 60 * 24 * 365],
    ['M', 1e3 * 60 * 60 * 24 * 30],
    ['D', 1e3 * 60 * 60 * 24],
    ['H', 1e3 * 60 * 60],
    ['m', 1e3 * 60],
    ['s', 1e3],
    ['S', 1],
  ];
function Y(e, i) {
  var a = e;
  return oe.reduce(function (t, n) {
    var l = le(n, 2),
      o = l[0],
      r = l[1];
    if (t.indexOf(o) !== -1) {
      var y = Math.floor(a / r);
      return (
        (a -= y * r),
        t.replace(new RegExp(o + '+', 'g'), function (w) {
          var x = w.length;
          return ne(y.toString(), x, '0');
        })
      );
    }
    return t;
  }, i);
}
var z =
  (globalThis && globalThis.__read) ||
  function (e, i) {
    var a = typeof Symbol == 'function' && e[Symbol.iterator];
    if (!a) return e;
    var t = a.call(e),
      n,
      l = [],
      o;
    try {
      for (; (i === void 0 || i-- > 0) && !(n = t.next()).done; )
        l.push(n.value);
    } catch (r) {
      o = { error: r };
    } finally {
      try {
        n && !n.done && (a = t.return) && a.call(t);
      } finally {
        if (o) throw o.error;
      }
    }
    return l;
  };
function ue(e, i) {
  var a = u.exports.useContext(A).getPrefixCls,
    t = e.className,
    n = e.style,
    l = e.title,
    o = e.styleValue,
    r = e.value,
    y = e.onFinish,
    w = e.renderFormat,
    x = e.format,
    g = x === void 0 ? 'HH:mm:ss' : x,
    F = e.start,
    b = F === void 0 ? !0 : F,
    h = L(r, g) || B(),
    _ = L(e.now, g),
    d = a('statistic'),
    O = z(u.exports.useState(h.diff(_, 'millisecond')), 2),
    j = O[0],
    U = O[1],
    V = z(u.exports.useState(Y(Math.max(j, 0), g)), 2),
    P = V[0],
    m = V[1],
    S = u.exports.useRef(null),
    D = function () {
      clearInterval(S.current), (S.current = null);
    },
    s = function () {
      S.current = setInterval(function () {
        var c = h.diff(W()),
          N = h.diff(W(), 'millisecond');
        N <= 0 && (D(), y == null || y());
        var R = Y(Math.max(N, 0), g);
        m(R), U(c);
      }, 1e3 / 30);
    };
  u.exports.useEffect(
    function () {
      return (
        !S.current && b && h.valueOf() >= Date.now() && s(),
        function () {
          D();
        }
      );
    },
    [e.start]
  );
  var T = G(w) ? w(j, P) : P;
  return C('div', {
    ref: i,
    className: J('' + d, d + '-countdown', t),
    style: n,
    children: [
      l && p('div', { className: d + '-title', children: l }),
      p('div', {
        className: d + '-content',
        children: p('div', { className: d + '-value', style: o, children: T }),
      }),
    ],
  });
}
var K = u.exports.forwardRef(ue);
K.displayName = 'StatisticCountdown';
var se = K,
  I =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (I =
          Object.assign ||
          function (e) {
            for (var i, a = 1, t = arguments.length; a < t; a++) {
              i = arguments[a];
              for (var n in i)
                Object.prototype.hasOwnProperty.call(i, n) && (e[n] = i[n]);
            }
            return e;
          }),
        I.apply(this, arguments)
      );
    },
  ce =
    (globalThis && globalThis.__rest) ||
    function (e, i) {
      var a = {};
      for (var t in e)
        Object.prototype.hasOwnProperty.call(e, t) &&
          i.indexOf(t) < 0 &&
          (a[t] = e[t]);
      if (e != null && typeof Object.getOwnPropertySymbols == 'function')
        for (var n = 0, t = Object.getOwnPropertySymbols(e); n < t.length; n++)
          i.indexOf(t[n]) < 0 &&
            Object.prototype.propertyIsEnumerable.call(e, t[n]) &&
            (a[t[n]] = e[t[n]]);
      return a;
    },
  fe =
    (globalThis && globalThis.__read) ||
    function (e, i) {
      var a = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!a) return e;
      var t = a.call(e),
        n,
        l = [],
        o;
      try {
        for (; (i === void 0 || i-- > 0) && !(n = t.next()).done; )
          l.push(n.value);
      } catch (r) {
        o = { error: r };
      } finally {
        try {
          n && !n.done && (a = t.return) && a.call(t);
        } finally {
          if (o) throw o.error;
        }
      }
      return l;
    },
  ve = { countFrom: 0, countDuration: 2e3 };
function de(e, i) {
  var a,
    t = u.exports.useContext(A),
    n = t.getPrefixCls,
    l = t.componentConfig,
    o = t.rtl,
    r = ee(e, ve, l == null ? void 0 : l.Statistic),
    y = r.className,
    w = r.style,
    x = r.title,
    g = r.extra,
    F = r.groupSeparator,
    b = r.precision,
    h = r.prefix,
    _ = r.suffix,
    d = r.format,
    O = r.renderFormat,
    j = r.styleValue,
    U = r.styleDecimal,
    V = r.loading,
    P = ce(r, [
      'className',
      'style',
      'title',
      'extra',
      'groupSeparator',
      'precision',
      'prefix',
      'suffix',
      'format',
      'renderFormat',
      'styleValue',
      'styleDecimal',
      'loading',
    ]),
    m = u.exports.useRef(),
    S = u.exports.useRef(),
    D = fe(u.exports.useState('value' in r ? r.value : void 0), 2),
    s = D[0],
    T = D[1],
    c = n('statistic'),
    N = function (v, f) {
      v === void 0 && (v = r.countFrom), f === void 0 && (f = r.value);
      var H = r.countDuration;
      v !== f &&
        ((m.current = new ae({
          from: { value: v },
          to: { value: f },
          duration: H,
          easing: 'quartOut',
          onUpdate: function (Z) {
            T(Z.value.toFixed(b));
          },
          onFinish: function () {
            T(f);
          },
        })),
        m.current.start());
    };
  u.exports.useEffect(
    function () {
      return (
        r.countUp
          ? (m.current && m.current.stop(),
            s !== r.value ? N(Number(s), r.value) : N())
          : T(r.value),
        function () {
          m.current && m.current.stop(), (m.current = null);
        }
      );
    },
    [r.value]
  ),
    u.exports.useImperativeHandle(i, function () {
      return {
        countUp: N,
        getRootDOMNode: function () {
          return S.current;
        },
      };
    });
  var R = u.exports.useMemo(
      function () {
        var v = s;
        d && (v = B(s).format(d)), M(b) && b >= 0 && (v = Number(s).toFixed(b));
        var f = String(v).split('.')[0],
          H = String(v).split('.')[1];
        return (
          F && M(Number(s)) && (f = Number(f).toLocaleString('en-US')),
          { int: f, decimal: H }
        );
      },
      [d, F, b, s]
    ),
    Q = R.int,
    E = R.decimal,
    X = G(O)
      ? O
      : function (v, f) {
          return f;
        },
    k = M(Number(s)),
    q = C(re, {
      children: [
        h != null
          ? p('span', { className: c + '-value-prefix', children: h })
          : null,
        X(s, k ? Q : s),
      ],
    });
  return C('div', {
    ...I(
      { className: J('' + c, ((a = {}), (a[c + '-rtl'] = o), a), y), style: w },
      te(P, ['value', 'countUp', 'countFrom', 'countDuration']),
      { ref: S }
    ),
    children: [
      x && p('div', { className: c + '-title', children: x }),
      C('div', {
        className: c + '-content',
        children: [
          p(ie, {
            animation: !0,
            loading: !!V,
            text: { rows: 1, width: '100%' },
            children: C('div', {
              className: c + '-value',
              style: j,
              children: [
                k ? p('span', { className: c + '-value-int', children: q }) : q,
                E !== void 0 || _
                  ? C('span', {
                      className: c + '-value-decimal',
                      style: U,
                      children: [
                        M(Number(s)) && E !== void 0 && '.' + E,
                        _ != null
                          ? p('span', {
                              className: c + '-value-suffix',
                              children: _,
                            })
                          : null,
                      ],
                    })
                  : null,
              ],
            }),
          }),
          g && p('div', { className: c + '-extra', children: g }),
        ],
      }),
    ],
  });
}
var me = u.exports.forwardRef(de),
  $ = me;
$.displayName = 'Statistic';
$.Countdown = se;
var ge = $;
export { ge as S };
