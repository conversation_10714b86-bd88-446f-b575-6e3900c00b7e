import { http, HttpResponse } from 'msw';
import { generatePermission } from '@/routes';

const userHandlers = [
  http.get('/fe/api/user/userInfo', () => {
    console.log('UserInfo mock called via MSW');
    const userRole = localStorage.getItem('userRole') || 'admin';

    return HttpResponse.json({
      name: 'admin',
      avatar:
        'https://lf1-xgcdn-tos.pstatp.com/obj/vcloud/vadmin/start.8e0e4855ee346a46ccff8ff3e24db27b.png',
      email: '<EMAIL>',
      job: 'frontend',
      jobName: '前端开发工程师',
      organization: 'Frontend',
      organizationName: '前端',
      location: 'beijing',
      locationName: '北京',
      introduction: '王力群并非是一个真实存在的人。',
      personalWebsite: 'https://www.arco.design',
      verified: true,
      phoneNumber: '177******02',
      accountId: 'abcd-********',
      registrationTime: new Date().toISOString(),
      permissions: generatePermission(userRole),
    });
  }),

  http.post('/fe/api/user/login', async ({ request }) => {
    console.log('Login mock intercepted via MSW');
    const body = (await request.json()) as {
      userName?: string;
      password?: string;
    };
    const { userName, password } = body;

    if (!userName) {
      return HttpResponse.json({
        status: 'error',
        msg: '用户名不能为空',
      });
    }

    if (!password) {
      return HttpResponse.json({
        status: 'error',
        msg: '密码不能为空',
      });
    }

    if (userName === 'admin' && password === 'admin') {
      return HttpResponse.json({
        status: 'ok',
      });
    }

    return HttpResponse.json({
      status: 'error',
      msg: '账号或者密码错误',
    });
  }),
];

export default userHandlers;
