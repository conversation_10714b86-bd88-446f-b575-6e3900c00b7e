import {
  u as c,
  b as n,
  a as s,
  j as a,
  n as o,
  B as p,
} from './index.7dafa16d.js';
import { l as d } from './index.69444b8c.js';
import { s as r } from './index.module.5c220b22.js';
function S() {
  const e = c(d),
    i = n((t) => t.userInfo || {}),
    l = [
      {
        title: e['userSetting.security.password'],
        value: e['userSetting.security.password.tips'],
      },
      {
        title: e['userSetting.security.question'],
        value: '',
        placeholder: e['userSetting.security.question.placeholder'],
      },
      {
        title: e['userSetting.security.phone'],
        value: i.phoneNumber
          ? `${e['userSetting.security.phone.tips']} ${i.phoneNumber}`
          : '',
      },
      {
        title: e['userSetting.security.email'],
        value: '',
        placeholder: e['userSetting.security.email.placeholder'],
      },
    ];
  return s('div', {
    className: r.security,
    children: l.map((t, u) =>
      a(
        'div',
        {
          className: r['security-item'],
          children: [
            s('span', {
              className: r['security-item-title'],
              children: t.title,
            }),
            a('div', {
              className: r['security-item-content'],
              children: [
                s('span', {
                  className: o({
                    [`${r['security-item-placeholder']}`]: !t.value,
                  }),
                  children: t.value || t.placeholder,
                }),
                s('span', {
                  children: s(p, {
                    type: 'text',
                    children: t.value
                      ? e['userSetting.btn.edit']
                      : e['userSetting.btn.set'],
                  }),
                }),
              ],
            }),
          ],
        },
        u
      )
    ),
  });
}
export { S as default };
