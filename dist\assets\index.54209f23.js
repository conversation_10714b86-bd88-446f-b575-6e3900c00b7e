var dt = Object.defineProperty;
var ft = (e, t, r) =>
  t in e
    ? dt(e, t, { enumerable: !0, configurable: !0, writable: !0, value: r })
    : (e[t] = r);
var f = (e, t, r) => (ft(e, typeof t != 'symbol' ? t + '' : t, r), r),
  ie = (e, t, r) => {
    if (!t.has(e)) throw TypeError('Cannot ' + r);
  };
var w = (e, t, r) => (
    ie(e, t, 'read from private field'), r ? r.call(e) : t.get(e)
  ),
  j = (e, t, r) => {
    if (t.has(e))
      throw TypeError('Cannot add the same private member more than once');
    t instanceof WeakSet ? t.add(e) : t.set(e, r);
  },
  ae = (e, t, r, s) => (
    ie(e, t, 'write to private field'), s ? s.call(e, r) : t.set(e, r), r
  );
var T = (e, t, r) => (ie(e, t, 'access private method'), r);
import {
  bf as R,
  bg as h,
  bh as pe,
  bi as te,
  bj as Te,
  bk as pt,
  bl as gt,
  bm as mt,
  bn as q,
  bo as yt,
} from './index.7dafa16d.js';
function bt(e, t) {
  const r = e.emit;
  if (r._isPiped) return;
  const s = function (n, ...i) {
    return t.emit(n, ...i), r.call(this, n, ...i);
  };
  (s._isPiped = !0), (e.emit = s);
}
function vt(e) {
  const t = [...e];
  return Object.freeze(t), t;
}
class wt {
  constructor() {
    f(this, 'subscriptions', []);
  }
  dispose() {
    let t;
    for (; (t = this.subscriptions.shift()); ) t();
  }
}
class Et {
  constructor(t) {
    f(this, 'handlers');
    (this.initialHandlers = t), (this.handlers = [...t]);
  }
  prepend(t) {
    this.handlers.unshift(...t);
  }
  reset(t) {
    this.handlers = t.length > 0 ? [...t] : [...this.initialHandlers];
  }
  currentHandlers() {
    return this.handlers;
  }
}
class kt extends wt {
  constructor(...r) {
    super();
    f(this, 'handlersController');
    f(this, 'emitter');
    f(this, 'publicEmitter');
    f(this, 'events');
    R(
      this.validateHandlers(r),
      h.formatMessage(
        'Failed to apply given request handlers: invalid input. Did you forget to spread the request handlers Array?'
      )
    ),
      (this.handlersController = new Et(r)),
      (this.emitter = new pe()),
      (this.publicEmitter = new pe()),
      bt(this.emitter, this.publicEmitter),
      (this.events = this.createLifeCycleEvents()),
      this.subscriptions.push(() => {
        this.emitter.removeAllListeners(),
          this.publicEmitter.removeAllListeners();
      });
  }
  validateHandlers(r) {
    return r.every((s) => !Array.isArray(s));
  }
  use(...r) {
    R(
      this.validateHandlers(r),
      h.formatMessage(
        'Failed to call "use()" with the given request handlers: invalid input. Did you forget to spread the array of request handlers?'
      )
    ),
      this.handlersController.prepend(r);
  }
  restoreHandlers() {
    this.handlersController.currentHandlers().forEach((r) => {
      'isUsed' in r && (r.isUsed = !1);
    });
  }
  resetHandlers(...r) {
    this.handlersController.reset(r);
  }
  listHandlers() {
    return vt(this.handlersController.currentHandlers());
  }
  createLifeCycleEvents() {
    return {
      on: (...r) => this.publicEmitter.on(...r),
      removeListener: (...r) => this.publicEmitter.removeListener(...r),
      removeAllListeners: (...r) => this.publicEmitter.removeAllListeners(...r),
    };
  }
}
function St() {
  const e = (t, r) => {
    (e.state = 'pending'),
      (e.resolve = (s) => {
        if (e.state !== 'pending') return;
        e.result = s;
        const o = (n) => ((e.state = 'fulfilled'), n);
        return t(s instanceof Promise ? s : Promise.resolve(s).then(o));
      }),
      (e.reject = (s) => {
        if (e.state === 'pending')
          return (
            queueMicrotask(() => {
              e.state = 'rejected';
            }),
            r((e.rejectionReason = s))
          );
      });
  };
  return e;
}
var S,
  _,
  K,
  Be,
  Lt =
    ((Be = class extends Promise {
      constructor(t = null) {
        const r = St();
        super((s, o) => {
          r(s, o), t == null || t(r.resolve, r.reject);
        });
        j(this, _);
        j(this, S, void 0);
        f(this, 'resolve');
        f(this, 'reject');
        ae(this, S, r),
          (this.resolve = w(this, S).resolve),
          (this.reject = w(this, S).reject);
      }
      get state() {
        return w(this, S).state;
      }
      get rejectionReason() {
        return w(this, S).rejectionReason;
      }
      then(t, r) {
        return T(this, _, K).call(this, super.then(t, r));
      }
      catch(t) {
        return T(this, _, K).call(this, super.catch(t));
      }
      finally(t) {
        return T(this, _, K).call(this, super.finally(t));
      }
    }),
    (S = new WeakMap()),
    (_ = new WeakSet()),
    (K = function (t) {
      return Object.defineProperties(t, {
        resolve: { configurable: !0, value: this.resolve },
        reject: { configurable: !0, value: this.reject },
      });
    }),
    Be),
  Rt = async (e) => {
    try {
      const t = await e().catch((r) => {
        throw r;
      });
      return { error: null, data: t };
    } catch (t) {
      return { error: t, data: null };
    }
  };
const Ct = async ({
  request: e,
  requestId: t,
  handlers: r,
  resolutionContext: s,
}) => {
  let o = null,
    n = null;
  for (const i of r)
    if (
      ((n = await i.run({ request: e, requestId: t, resolutionContext: s })),
      n !== null && (o = i),
      n != null && n.response)
    )
      break;
  return o
    ? {
        handler: o,
        parsedResult: n == null ? void 0 : n.parsedResult,
        response: n == null ? void 0 : n.response,
      }
    : null;
};
function Tt(e) {
  const t = new URL(e.url);
  return t.protocol === 'file:' ||
    /(fonts\.googleapis\.com)/.test(t.hostname) ||
    /node_modules/.test(t.pathname) ||
    t.pathname.includes('@vite')
    ? !0
    : /\.(s?css|less|m?jsx?|m?tsx?|html|ttf|otf|woff|woff2|eot|gif|jpe?g|png|avif|webp|svg|mp4|webm|ogg|mov|mp3|wav|ogg|flac|aac|pdf|txt|csv|json|xml|md|zip|tar|gz|rar|7z)$/i.test(
        t.pathname
      );
}
async function Ue(e, t = 'warn') {
  const r = new URL(e.url),
    s = te(r) + r.search,
    o =
      e.method === 'HEAD' || e.method === 'GET' ? null : await e.clone().text(),
    i = `intercepted a request without a matching request handler:${`

  \u2022 ${e.method} ${s}

${
  o
    ? `  \u2022 Request body: ${o}

`
    : ''
}`}If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/http/intercepting-requests`;
  function a(l) {
    switch (l) {
      case 'error':
        throw (
          (h.error('Error: %s', i),
          new Te(
            h.formatMessage(
              'Cannot bypass a request when using the "error" strategy for the "onUnhandledRequest" option.'
            )
          ))
        );
      case 'warn': {
        h.warn('Warning: %s', i);
        break;
      }
      case 'bypass':
        break;
      default:
        throw new Te(
          h.formatMessage(
            'Failed to react to an unhandled request: unknown strategy "%s". Please provide one of the supported strategies ("bypass", "warn", "error") or a custom callback function as the value of the "onUnhandledRequest" option.',
            l
          )
        );
    }
  }
  if (typeof t == 'function') {
    t(e, { warning: a.bind(null, 'warn'), error: a.bind(null, 'error') });
    return;
  }
  Tt(e) || a(t);
}
function Pt(e, t) {
  const r = Reflect.get(t, pt);
  r && gt.setCookie(r, e.url);
}
async function Ne(e, t, r, s, o, n) {
  var u, c, d, p, m, X;
  if (
    (o.emit('request:start', { request: e, requestId: t }),
    (u = e.headers.get('accept')) != null && u.includes('msw/passthrough'))
  ) {
    o.emit('request:end', { request: e, requestId: t }),
      (c = n == null ? void 0 : n.onPassthroughResponse) == null ||
        c.call(n, e);
    return;
  }
  const i = await Rt(() =>
    Ct({
      request: e,
      requestId: t,
      handlers: r,
      resolutionContext: n == null ? void 0 : n.resolutionContext,
    })
  );
  if (i.error)
    throw (
      (o.emit('unhandledException', {
        error: i.error,
        request: e,
        requestId: t,
      }),
      i.error)
    );
  if (!i.data) {
    await Ue(e, s.onUnhandledRequest),
      o.emit('request:unhandled', { request: e, requestId: t }),
      o.emit('request:end', { request: e, requestId: t }),
      (d = n == null ? void 0 : n.onPassthroughResponse) == null ||
        d.call(n, e);
    return;
  }
  const { response: a } = i.data;
  if (!a) {
    o.emit('request:end', { request: e, requestId: t }),
      (p = n == null ? void 0 : n.onPassthroughResponse) == null ||
        p.call(n, e);
    return;
  }
  if (a.status === 302 && a.headers.get('x-msw-intention') === 'passthrough') {
    o.emit('request:end', { request: e, requestId: t }),
      (m = n == null ? void 0 : n.onPassthroughResponse) == null ||
        m.call(n, e);
    return;
  }
  Pt(e, a), o.emit('request:match', { request: e, requestId: t });
  const l = i.data;
  return (
    (X = n == null ? void 0 : n.onMockedResponse) == null || X.call(n, a, l),
    o.emit('request:end', { request: e, requestId: t }),
    a
  );
}
function qt(e) {
  return {
    status: e.status,
    statusText: e.statusText,
    headers: Object.fromEntries(e.headers.entries()),
  };
}
function we(e) {
  return (t) =>
    t != null && typeof t == 'object' && '__kind' in t && t.__kind === e;
}
function Pe(e) {
  return e != null && typeof e == 'object' && !Array.isArray(e);
}
function Ge(e, t) {
  return Object.entries(t).reduce((r, [s, o]) => {
    const n = r[s];
    return Array.isArray(n) && Array.isArray(o)
      ? ((r[s] = n.concat(o)), r)
      : Pe(n) && Pe(o)
      ? ((r[s] = Ge(n, o)), r)
      : ((r[s] = o), r);
  }, Object.assign({}, e));
}
function xt(e) {
  const t = Object.getOwnPropertyDescriptor(globalThis, e);
  return typeof t == 'undefined' ||
    (typeof t.get == 'function' && typeof t.get() == 'undefined') ||
    (typeof t.get == 'undefined' && t.value == null)
    ? !1
    : typeof t.set == 'undefined' && !t.configurable
    ? (console.error(
        `[MSW] Failed to apply interceptor: the global \`${e}\` property is non-configurable. This is likely an issue with your environment. If you are using a framework, please open an issue about this in their repository.`
      ),
      !1)
    : !0;
}
function qe(e) {
  return globalThis[e] || void 0;
}
function It(e, t) {
  globalThis[e] = t;
}
function Mt(e) {
  delete globalThis[e];
}
var At = class {
  constructor(e) {
    (this.symbol = e),
      (this.readyState = 'INACTIVE'),
      (this.emitter = new pe()),
      (this.subscriptions = []),
      (this.logger = new mt(e.description)),
      this.emitter.setMaxListeners(0),
      this.logger.info('constructing the interceptor...');
  }
  checkEnvironment() {
    return !0;
  }
  apply() {
    const e = this.logger.extend('apply');
    if (
      (e.info('applying the interceptor...'), this.readyState === 'APPLIED')
    ) {
      e.info('intercepted already applied!');
      return;
    }
    if (!this.checkEnvironment()) {
      e.info('the interceptor cannot be applied in this environment!');
      return;
    }
    this.readyState = 'APPLYING';
    const r = this.getInstance();
    if (r) {
      e.info('found a running instance, reusing...'),
        (this.on = (s, o) => (
          e.info('proxying the "%s" listener', s),
          r.emitter.addListener(s, o),
          this.subscriptions.push(() => {
            r.emitter.removeListener(s, o),
              e.info('removed proxied "%s" listener!', s);
          }),
          this
        )),
        (this.readyState = 'APPLIED');
      return;
    }
    e.info('no running instance found, setting up a new instance...'),
      this.setup(),
      this.setInstance(),
      (this.readyState = 'APPLIED');
  }
  setup() {}
  on(e, t) {
    const r = this.logger.extend('on');
    return this.readyState === 'DISPOSING' || this.readyState === 'DISPOSED'
      ? (r.info('cannot listen to events, already disposed!'), this)
      : (r.info('adding "%s" event listener:', e, t),
        this.emitter.on(e, t),
        this);
  }
  once(e, t) {
    return this.emitter.once(e, t), this;
  }
  off(e, t) {
    return this.emitter.off(e, t), this;
  }
  removeAllListeners(e) {
    return this.emitter.removeAllListeners(e), this;
  }
  dispose() {
    const e = this.logger.extend('dispose');
    if (this.readyState === 'DISPOSED') {
      e.info('cannot dispose, already disposed!');
      return;
    }
    if (
      (e.info('disposing the interceptor...'),
      (this.readyState = 'DISPOSING'),
      !this.getInstance())
    ) {
      e.info('no interceptors running, skipping dispose...');
      return;
    }
    if (
      (this.clearInstance(),
      e.info('global symbol deleted:', qe(this.symbol)),
      this.subscriptions.length > 0)
    ) {
      e.info('disposing of %d subscriptions...', this.subscriptions.length);
      for (const t of this.subscriptions) t();
      (this.subscriptions = []),
        e.info('disposed of all subscriptions!', this.subscriptions.length);
    }
    this.emitter.removeAllListeners(),
      e.info('destroyed the listener!'),
      (this.readyState = 'DISPOSED');
  }
  getInstance() {
    var e;
    const t = qe(this.symbol);
    return (
      this.logger.info(
        'retrieved global instance:',
        (e = t == null ? void 0 : t.constructor) == null ? void 0 : e.name
      ),
      t
    );
  }
  setInstance() {
    It(this.symbol, this),
      this.logger.info('set global instance!', this.symbol.description);
  }
  clearInstance() {
    Mt(this.symbol),
      this.logger.info('cleared global instance!', this.symbol.description);
  }
};
function Wt() {
  return Math.random().toString(16).slice(2);
}
function g(e, t) {
  return (
    Object.defineProperties(t, {
      target: { value: e, enumerable: !0, writable: !0 },
      currentTarget: { value: e, enumerable: !0, writable: !0 },
    }),
    t
  );
}
var A = Symbol('kCancelable'),
  v = Symbol('kDefaultPrevented'),
  Ee = class extends MessageEvent {
    constructor(e, t) {
      super(e, t), (this[A] = !!t.cancelable), (this[v] = !1);
    }
    get cancelable() {
      return this[A];
    }
    set cancelable(e) {
      this[A] = e;
    }
    get defaultPrevented() {
      return this[v];
    }
    set defaultPrevented(e) {
      this[v] = e;
    }
    preventDefault() {
      this.cancelable && !this[v] && (this[v] = !0);
    }
  },
  re = class extends Event {
    constructor(e, t = {}) {
      super(e, t),
        (this.code = t.code === void 0 ? 0 : t.code),
        (this.reason = t.reason === void 0 ? '' : t.reason),
        (this.wasClean = t.wasClean === void 0 ? !1 : t.wasClean);
    }
  },
  xe = class extends re {
    constructor(e, t = {}) {
      super(e, t), (this[A] = !!t.cancelable), (this[v] = !1);
    }
    get cancelable() {
      return this[A];
    }
    set cancelable(e) {
      this[A] = e;
    }
    get defaultPrevented() {
      return this[v];
    }
    set defaultPrevented(e) {
      this[v] = e;
    }
    preventDefault() {
      this.cancelable && !this[v] && (this[v] = !0);
    }
  },
  H = Symbol('kEmitter'),
  z = Symbol('kBoundListener'),
  _t = class {
    constructor(e, t) {
      (this.socket = e),
        (this.transport = t),
        (this.id = Wt()),
        (this.url = new URL(e.url)),
        (this[H] = new EventTarget()),
        this.transport.addEventListener('outgoing', (r) => {
          const s = g(
            this.socket,
            new Ee('message', {
              data: r.data,
              origin: r.origin,
              cancelable: !0,
            })
          );
          this[H].dispatchEvent(s), s.defaultPrevented && r.preventDefault();
        }),
        this.transport.addEventListener('close', (r) => {
          this[H].dispatchEvent(g(this.socket, new re('close', r)));
        });
    }
    addEventListener(e, t, r) {
      if (!Reflect.has(t, z)) {
        const s = t.bind(this.socket);
        Object.defineProperty(t, z, {
          value: s,
          enumerable: !1,
          configurable: !1,
        });
      }
      this[H].addEventListener(e, Reflect.get(t, z), r);
    }
    removeEventListener(e, t, r) {
      this[H].removeEventListener(e, Reflect.get(t, z), r);
    }
    send(e) {
      this.transport.send(e);
    }
    close(e, t) {
      this.transport.close(e, t);
    }
  },
  Ie = 'InvalidAccessError: close code out of user configurable range',
  Q = Symbol('kPassthroughPromise'),
  Fe = Symbol('kOnSend'),
  N = Symbol('kClose'),
  G = class extends EventTarget {
    constructor(e, t) {
      super(),
        (this.CONNECTING = 0),
        (this.OPEN = 1),
        (this.CLOSING = 2),
        (this.CLOSED = 3),
        (this._onopen = null),
        (this._onmessage = null),
        (this._onerror = null),
        (this._onclose = null),
        (this.url = e.toString()),
        (this.protocol = ''),
        (this.extensions = ''),
        (this.binaryType = 'blob'),
        (this.readyState = this.CONNECTING),
        (this.bufferedAmount = 0),
        (this[Q] = new Lt()),
        queueMicrotask(async () => {
          (await this[Q]) ||
            ((this.protocol =
              typeof t == 'string'
                ? t
                : Array.isArray(t) && t.length > 0
                ? t[0]
                : ''),
            this.readyState === this.CONNECTING &&
              ((this.readyState = this.OPEN),
              this.dispatchEvent(g(this, new Event('open')))));
        });
    }
    set onopen(e) {
      this.removeEventListener('open', this._onopen),
        (this._onopen = e),
        e !== null && this.addEventListener('open', e);
    }
    get onopen() {
      return this._onopen;
    }
    set onmessage(e) {
      this.removeEventListener('message', this._onmessage),
        (this._onmessage = e),
        e !== null && this.addEventListener('message', e);
    }
    get onmessage() {
      return this._onmessage;
    }
    set onerror(e) {
      this.removeEventListener('error', this._onerror),
        (this._onerror = e),
        e !== null && this.addEventListener('error', e);
    }
    get onerror() {
      return this._onerror;
    }
    set onclose(e) {
      this.removeEventListener('close', this._onclose),
        (this._onclose = e),
        e !== null && this.addEventListener('close', e);
    }
    get onclose() {
      return this._onclose;
    }
    send(e) {
      if (this.readyState === this.CONNECTING)
        throw (this.close(), new DOMException('InvalidStateError'));
      this.readyState === this.CLOSING ||
        this.readyState === this.CLOSED ||
        ((this.bufferedAmount += Dt(e)),
        queueMicrotask(() => {
          var t;
          (this.bufferedAmount = 0), (t = this[Fe]) == null || t.call(this, e);
        }));
    }
    close(e = 1e3, t) {
      R(e, Ie), R(e === 1e3 || (e >= 3e3 && e <= 4999), Ie), this[N](e, t);
    }
    [N](e = 1e3, t, r = !0) {
      this.readyState === this.CLOSING ||
        this.readyState === this.CLOSED ||
        ((this.readyState = this.CLOSING),
        queueMicrotask(() => {
          (this.readyState = this.CLOSED),
            this.dispatchEvent(
              g(this, new re('close', { code: e, reason: t, wasClean: r }))
            ),
            (this._onopen = null),
            (this._onmessage = null),
            (this._onerror = null),
            (this._onclose = null);
        }));
    }
    addEventListener(e, t, r) {
      return super.addEventListener(e, t, r);
    }
    removeEventListener(e, t, r) {
      return super.removeEventListener(e, t, r);
    }
  };
G.CONNECTING = 0;
G.OPEN = 1;
G.CLOSING = 2;
G.CLOSED = 3;
function Dt(e) {
  return typeof e == 'string'
    ? e.length
    : e instanceof Blob
    ? e.size
    : e.byteLength;
}
var k = Symbol('kEmitter'),
  V = Symbol('kBoundListener'),
  le = Symbol('kSend'),
  Ot = class {
    constructor(e, t, r) {
      (this.client = e),
        (this.transport = t),
        (this.createConnection = r),
        (this[k] = new EventTarget()),
        (this.mockCloseController = new AbortController()),
        (this.realCloseController = new AbortController()),
        this.transport.addEventListener('outgoing', (s) => {
          typeof this.realWebSocket != 'undefined' &&
            queueMicrotask(() => {
              s.defaultPrevented || this[le](s.data);
            });
        }),
        this.transport.addEventListener(
          'incoming',
          this.handleIncomingMessage.bind(this)
        );
    }
    get socket() {
      return (
        R(
          this.realWebSocket,
          'Cannot access "socket" on the original WebSocket server object: the connection is not open. Did you forget to call `server.connect()`?'
        ),
        this.realWebSocket
      );
    }
    connect() {
      R(
        !this.realWebSocket || this.realWebSocket.readyState !== WebSocket.OPEN,
        'Failed to call "connect()" on the original WebSocket instance: the connection already open'
      );
      const e = this.createConnection();
      (e.binaryType = this.client.binaryType),
        e.addEventListener(
          'open',
          (t) => {
            this[k].dispatchEvent(g(this.realWebSocket, new Event('open', t)));
          },
          { once: !0 }
        ),
        e.addEventListener('message', (t) => {
          this.transport.dispatchEvent(
            g(
              this.realWebSocket,
              new MessageEvent('incoming', { data: t.data, origin: t.origin })
            )
          );
        }),
        this.client.addEventListener(
          'close',
          (t) => {
            this.handleMockClose(t);
          },
          { signal: this.mockCloseController.signal }
        ),
        e.addEventListener(
          'close',
          (t) => {
            this.handleRealClose(t);
          },
          { signal: this.realCloseController.signal }
        ),
        e.addEventListener('error', () => {
          const t = g(e, new Event('error', { cancelable: !0 }));
          this[k].dispatchEvent(t),
            t.defaultPrevented ||
              this.client.dispatchEvent(g(this.client, new Event('error')));
        }),
        (this.realWebSocket = e);
    }
    addEventListener(e, t, r) {
      if (!Reflect.has(t, V)) {
        const s = t.bind(this.client);
        Object.defineProperty(t, V, { value: s, enumerable: !1 });
      }
      this[k].addEventListener(e, Reflect.get(t, V), r);
    }
    removeEventListener(e, t, r) {
      this[k].removeEventListener(e, Reflect.get(t, V), r);
    }
    send(e) {
      this[le](e);
    }
    [le](e) {
      const { realWebSocket: t } = this;
      if (
        (R(
          t,
          'Failed to call "server.send()" for "%s": the connection is not open. Did you forget to call "server.connect()"?',
          this.client.url
        ),
        !(
          t.readyState === WebSocket.CLOSING ||
          t.readyState === WebSocket.CLOSED
        ))
      ) {
        if (t.readyState === WebSocket.CONNECTING) {
          t.addEventListener(
            'open',
            () => {
              t.send(e);
            },
            { once: !0 }
          );
          return;
        }
        t.send(e);
      }
    }
    close() {
      const { realWebSocket: e } = this;
      R(
        e,
        'Failed to close server connection for "%s": the connection is not open. Did you forget to call "server.connect()"?',
        this.client.url
      ),
        this.realCloseController.abort(),
        !(
          e.readyState === WebSocket.CLOSING ||
          e.readyState === WebSocket.CLOSED
        ) &&
          (e.close(),
          queueMicrotask(() => {
            this[k].dispatchEvent(
              g(
                this.realWebSocket,
                new xe('close', { code: 1e3, cancelable: !0 })
              )
            );
          }));
    }
    handleIncomingMessage(e) {
      const t = g(
        e.target,
        new Ee('message', { data: e.data, origin: e.origin, cancelable: !0 })
      );
      this[k].dispatchEvent(t),
        t.defaultPrevented ||
          this.client.dispatchEvent(
            g(
              this.client,
              new MessageEvent('message', { data: e.data, origin: e.origin })
            )
          );
    }
    handleMockClose(e) {
      this.realWebSocket && this.realWebSocket.close();
    }
    handleRealClose(e) {
      this.mockCloseController.abort();
      const t = g(
        this.realWebSocket,
        new xe('close', {
          code: e.code,
          reason: e.reason,
          wasClean: e.wasClean,
          cancelable: !0,
        })
      );
      this[k].dispatchEvent(t),
        t.defaultPrevented || this.client[N](e.code, e.reason);
    }
  },
  jt = class extends EventTarget {
    constructor(e) {
      super(),
        (this.socket = e),
        this.socket.addEventListener('close', (t) => {
          this.dispatchEvent(g(this.socket, new re('close', t)));
        }),
        (this.socket[Fe] = (t) => {
          this.dispatchEvent(
            g(
              this.socket,
              new Ee('outgoing', {
                data: t,
                origin: this.socket.url,
                cancelable: !0,
              })
            )
          );
        });
    }
    addEventListener(e, t, r) {
      return super.addEventListener(e, t, r);
    }
    dispatchEvent(e) {
      return super.dispatchEvent(e);
    }
    send(e) {
      queueMicrotask(() => {
        if (
          this.socket.readyState === this.socket.CLOSING ||
          this.socket.readyState === this.socket.CLOSED
        )
          return;
        const t = () => {
          this.socket.dispatchEvent(
            g(
              this.socket,
              new MessageEvent('message', { data: e, origin: this.socket.url })
            )
          );
        };
        this.socket.readyState === this.socket.CONNECTING
          ? this.socket.addEventListener(
              'open',
              () => {
                t();
              },
              { once: !0 }
            )
          : t();
      });
    }
    close(e, t) {
      this.socket[N](e, t);
    }
  },
  Xe = class extends At {
    constructor() {
      super(Xe.symbol);
    }
    checkEnvironment() {
      return xt('WebSocket');
    }
    setup() {
      const e = Object.getOwnPropertyDescriptor(globalThis, 'WebSocket'),
        t = new Proxy(globalThis.WebSocket, {
          construct: (r, s, o) => {
            const [n, i] = s,
              a = () => Reflect.construct(r, s, o),
              l = new G(n, i),
              u = new jt(l);
            return (
              queueMicrotask(() => {
                try {
                  const c = new Ot(l, u, a);
                  this.emitter.emit('connection', {
                    client: new _t(l, u),
                    server: c,
                    info: { protocols: i },
                  })
                    ? l[Q].resolve(!1)
                    : (l[Q].resolve(!0),
                      c.connect(),
                      c.addEventListener('open', () => {
                        l.dispatchEvent(g(l, new Event('open'))),
                          c.realWebSocket &&
                            (l.protocol = c.realWebSocket.protocol);
                      }));
                } catch (c) {
                  c instanceof Error &&
                    (l.dispatchEvent(new Event('error')),
                    l.readyState !== WebSocket.CLOSING &&
                      l.readyState !== WebSocket.CLOSED &&
                      l[N](1011, c.message, !1),
                    console.error(c));
                }
              }),
              l
            );
          },
        });
      Object.defineProperty(globalThis, 'WebSocket', {
        value: t,
        configurable: !0,
      }),
        this.subscriptions.push(() => {
          Object.defineProperty(globalThis, 'WebSocket', e);
        });
    }
  },
  ze = Xe;
ze.symbol = Symbol('websocket');
const ge = new ze();
function Ht(e) {
  ge.on('connection', async (t) => {
    const r = e.getHandlers().filter(we('EventHandler'));
    if (r.length > 0) {
      e == null || e.onMockedConnection(t),
        await Promise.all(r.map((o) => o.run(t)));
      return;
    }
    const s = new Request(t.client.url, {
      headers: { upgrade: 'websocket', connection: 'upgrade' },
    });
    await Ue(s, e.getUnhandledRequestStrategy()).catch((o) => {
      const n = new Event('error');
      Object.defineProperty(n, 'cause', {
        enumerable: !0,
        configurable: !1,
        value: o,
      }),
        t.client.socket.dispatchEvent(n);
    }),
      e == null || e.onPassthroughConnection(t),
      t.server.connect();
  });
}
function se(e) {
  return e instanceof Blob
    ? e.size
    : e instanceof ArrayBuffer
    ? e.byteLength
    : new Blob([e]).size;
}
const Me = 24;
function ce(e) {
  return e.length <= Me ? e : `${e.slice(0, Me)}\u2026`;
}
async function ne(e) {
  if (e instanceof Blob) {
    const t = await e.text();
    return `Blob(${ce(t)})`;
  }
  if (typeof e == 'object' && 'byteLength' in e) {
    const t = new TextDecoder().decode(e);
    return `ArrayBuffer(${ce(t)})`;
  }
  return ce(e);
}
const x = {
  system: '#3b82f6',
  outgoing: '#22c55e',
  incoming: '#ef4444',
  mocked: '#ff6a33',
};
function Bt(e) {
  const { client: t, server: r } = e;
  $t(t),
    t.addEventListener('message', (s) => {
      Gt(s);
    }),
    t.addEventListener('close', (s) => {
      Ut(s);
    }),
    t.socket.addEventListener('error', (s) => {
      Nt(s);
    }),
    (t.send = new Proxy(t.send, {
      apply(s, o, n) {
        const [i] = n,
          a = new MessageEvent('message', { data: i });
        return (
          Object.defineProperties(a, {
            currentTarget: { enumerable: !0, writable: !1, value: t.socket },
            target: { enumerable: !0, writable: !1, value: t.socket },
          }),
          queueMicrotask(() => {
            Xt(a);
          }),
          Reflect.apply(s, o, n)
        );
      },
    })),
    r.addEventListener(
      'open',
      () => {
        r.addEventListener('message', (s) => {
          zt(s);
        });
      },
      { once: !0 }
    ),
    (r.send = new Proxy(r.send, {
      apply(s, o, n) {
        const [i] = n,
          a = new MessageEvent('message', { data: i });
        return (
          Object.defineProperties(a, {
            currentTarget: { enumerable: !0, writable: !1, value: r.socket },
            target: { enumerable: !0, writable: !1, value: r.socket },
          }),
          Ft(a),
          Reflect.apply(s, o, n)
        );
      },
    }));
}
function $t(e) {
  const t = te(e.url);
  console.groupCollapsed(
    h.formatMessage(`${q()} %c\u25B6%c ${t}`),
    `color:${x.system}`,
    'color:inherit'
  ),
    console.log('Client:', e.socket),
    console.groupEnd();
}
function Ut(e) {
  const t = e.target,
    r = te(t.url);
  console.groupCollapsed(
    h.formatMessage(`${q({ milliseconds: !0 })} %c\u25A0%c ${r}`),
    `color:${x.system}`,
    'color:inherit'
  ),
    console.log(e),
    console.groupEnd();
}
function Nt(e) {
  const t = e.target,
    r = te(t.url);
  console.groupCollapsed(
    h.formatMessage(`${q({ milliseconds: !0 })} %c\xD7%c ${r}`),
    `color:${x.system}`,
    'color:inherit'
  ),
    console.log(e),
    console.groupEnd();
}
async function Gt(e) {
  const t = se(e.data),
    r = await ne(e.data),
    s = e.defaultPrevented ? '\u21E1' : '\u2B06';
  console.groupCollapsed(
    h.formatMessage(`${q({ milliseconds: !0 })} %c${s}%c ${r} %c${t}%c`),
    `color:${x.outgoing}`,
    'color:inherit',
    'color:gray;font-weight:normal',
    'color:inherit;font-weight:inherit'
  ),
    console.log(e),
    console.groupEnd();
}
async function Ft(e) {
  const t = se(e.data),
    r = await ne(e.data);
  console.groupCollapsed(
    h.formatMessage(`${q({ milliseconds: !0 })} %c\u2B06%c ${r} %c${t}%c`),
    `color:${x.mocked}`,
    'color:inherit',
    'color:gray;font-weight:normal',
    'color:inherit;font-weight:inherit'
  ),
    console.log(e),
    console.groupEnd();
}
async function Xt(e) {
  const t = se(e.data),
    r = await ne(e.data);
  console.groupCollapsed(
    h.formatMessage(`${q({ milliseconds: !0 })} %c\u2B07%c ${r} %c${t}%c`),
    `color:${x.mocked}`,
    'color:inherit',
    'color:gray;font-weight:normal',
    'color:inherit;font-weight:inherit'
  ),
    console.log(e),
    console.groupEnd();
}
async function zt(e) {
  const t = se(e.data),
    r = await ne(e.data),
    s = e.defaultPrevented ? '\u21E3' : '\u2B07';
  console.groupCollapsed(
    h.formatMessage(`${q({ milliseconds: !0 })} %c${s}%c ${r} %c${t}%c`),
    `color:${x.incoming}`,
    'color:inherit',
    'color:gray;font-weight:normal',
    'color:inherit;font-weight:inherit'
  ),
    console.log(e),
    console.groupEnd();
}
var Vt = /(%?)(%([sdijo]))/g;
function Kt(e, t) {
  switch (t) {
    case 's':
      return e;
    case 'd':
    case 'i':
      return Number(e);
    case 'j':
      return JSON.stringify(e);
    case 'o': {
      if (typeof e == 'string') return e;
      const r = JSON.stringify(e);
      return r === '{}' || r === '[]' || /^\[object .+?\]$/.test(r) ? e : r;
    }
  }
}
function F(e, ...t) {
  if (t.length === 0) return e;
  let r = 0,
    s = e.replace(Vt, (o, n, i, a) => {
      const l = t[r],
        u = Kt(l, a);
      return n ? o : (r++, u);
    });
  return (
    r < t.length && (s += ` ${t.slice(r).join(' ')}`),
    (s = s.replace(/%{2,2}/g, '%')),
    s
  );
}
var Jt = 2;
function Yt(e) {
  if (!e.stack) return;
  const t = e.stack.split(`
`);
  t.splice(1, Jt),
    (e.stack = t.join(`
`));
}
var Qt = class extends Error {
    constructor(e, ...t) {
      super(e),
        (this.message = e),
        (this.name = 'Invariant Violation'),
        (this.message = F(e, ...t)),
        Yt(this);
    }
  },
  C = (e, t, ...r) => {
    if (!e) throw new Qt(t, ...r);
  };
C.as = (e, t, r, ...s) => {
  if (!t) {
    const o = s.length === 0 ? r : F(r, ...s);
    let n;
    try {
      n = Reflect.construct(e, [o]);
    } catch {
      n = e(o);
    }
    throw n;
  }
};
function ke() {
  if (typeof navigator != 'undefined' && navigator.product === 'ReactNative')
    return !0;
  if (typeof process != 'undefined') {
    const e = process.type;
    return e === 'renderer' || e === 'worker'
      ? !1
      : !!(process.versions && process.versions.node);
  }
  return !1;
}
var me = async (e) => {
  try {
    const t = await e().catch((r) => {
      throw r;
    });
    return { error: null, data: t };
  } catch (t) {
    return { error: t, data: null };
  }
};
function Zt(e) {
  return new URL(e, location.href).href;
}
function ue(e, t, r) {
  return (
    [e.active, e.installing, e.waiting]
      .filter((i) => i != null)
      .find((i) => r(i.scriptURL, t)) || null
  );
}
var er = async (e, t = {}, r) => {
  const s = Zt(e),
    o = await navigator.serviceWorker
      .getRegistrations()
      .then((a) => a.filter((l) => ue(l, s, r)));
  !navigator.serviceWorker.controller && o.length > 0 && location.reload();
  const [n] = o;
  if (n) return n.update(), [ue(n, s, r), n];
  const i = await me(async () => {
    const a = await navigator.serviceWorker.register(e, t);
    return [ue(a, s, r), a];
  });
  if (i.error) {
    if (i.error.message.includes('(404)')) {
      const l = new URL((t == null ? void 0 : t.scope) || '/', location.href);
      throw new Error(
        h.formatMessage(`Failed to register a Service Worker for scope ('${l.href}') with script ('${s}'): Service Worker script does not exist at the given path.

Did you forget to run "npx msw init <PUBLIC_DIR>"?

Learn more about creating the Service Worker script: https://mswjs.io/docs/cli/init`)
      );
    }
    throw new Error(
      h.formatMessage(
        `Failed to register the Service Worker:

%s`,
        i.error.message
      )
    );
  }
  return i.data;
};
function Ve(e = {}) {
  if (e.quiet) return;
  const t = e.message || 'Mocking enabled.';
  console.groupCollapsed(
    `%c${h.formatMessage(t)}`,
    'color:orangered;font-weight:bold;'
  ),
    console.log(
      '%cDocumentation: %chttps://mswjs.io/docs',
      'font-weight:bold',
      'font-weight:normal'
    ),
    console.log('Found an issue? https://github.com/mswjs/msw/issues'),
    e.workerUrl && console.log('Worker script URL:', e.workerUrl),
    e.workerScope && console.log('Worker scope:', e.workerScope),
    e.client &&
      console.log('Client ID: %s (%s)', e.client.id, e.client.frameType),
    console.groupEnd();
}
async function tr(e, t) {
  var s, o;
  e.workerChannel.send('MOCK_ACTIVATE');
  const { payload: r } = await e.events.once('MOCKING_ENABLED');
  if (e.isMockingEnabled) {
    h.warn(
      'Found a redundant "worker.start()" call. Note that starting the worker while mocking is already enabled will have no effect. Consider removing this "worker.start()" call.'
    );
    return;
  }
  (e.isMockingEnabled = !0),
    Ve({
      quiet: t.quiet,
      workerScope: (s = e.registration) == null ? void 0 : s.scope,
      workerUrl: (o = e.worker) == null ? void 0 : o.scriptURL,
      client: r.client,
    });
}
var rr = class {
  constructor(e) {
    this.port = e;
  }
  postMessage(e, ...t) {
    const [r, s] = t;
    this.port.postMessage({ type: e, data: r }, { transfer: s });
  }
};
function sr(e) {
  if (!['HEAD', 'GET'].includes(e.method)) return e.body;
}
function Ke(e) {
  return new Request(e.url, { ...e, body: sr(e) });
}
var nr = (e, t) => async (r, s) => {
  var u;
  const o = new rr(r.ports[0]),
    n = s.payload.id,
    i = Ke(s.payload),
    a = i.clone(),
    l = i.clone();
  yt.cache.set(i, l);
  try {
    await Ne(
      i,
      n,
      e.getRequestHandlers().filter(we('RequestHandler')),
      t,
      e.emitter,
      {
        onPassthroughResponse() {
          o.postMessage('PASSTHROUGH');
        },
        async onMockedResponse(c, { handler: d, parsedResult: p }) {
          const m = c.clone(),
            X = c.clone(),
            Ce = qt(c);
          if (e.supports.readableStreamTransfer) {
            const O = c.body;
            o.postMessage(
              'MOCK_RESPONSE',
              { ...Ce, body: O },
              O ? [O] : void 0
            );
          } else {
            const O = c.body === null ? null : await m.arrayBuffer();
            o.postMessage('MOCK_RESPONSE', { ...Ce, body: O });
          }
          t.quiet ||
            e.emitter.once('response:mocked', () => {
              d.log({ request: a, response: X, parsedResult: p });
            });
        },
      }
    );
  } catch (c) {
    c instanceof Error &&
      (h.error(
        `Uncaught exception in the request handler for "%s %s":

%s

This exception has been gracefully handled as a 500 response, however, it's strongly recommended to resolve this error, as it indicates a mistake in your code. If you wish to mock an error response, please see this guide: https://mswjs.io/docs/http/mocking-responses/error-responses`,
        i.method,
        i.url,
        (u = c.stack) != null ? u : c
      ),
      o.postMessage('MOCK_RESPONSE', {
        status: 500,
        statusText: 'Request Handler Error',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: c.name,
          message: c.message,
          stack: c.stack,
        }),
      }));
  }
};
async function or(e) {
  e.workerChannel.send('INTEGRITY_CHECK_REQUEST');
  const { payload: t } = await e.events.once('INTEGRITY_CHECK_RESPONSE');
  t.checksum !== 'f5825c521429caf22a4dd13b66e243af' &&
    h.warn(`The currently registered Service Worker has been generated by a different version of MSW (${t.packageVersion}) and may not be fully compatible with the installed version.

It's recommended you update your worker script by running this command:

  \u2022 npx msw init <PUBLIC_DIR>

You can also automate this process and make the worker script update automatically upon the library installations. Read more: https://mswjs.io/docs/cli/init.`);
}
var ir = new TextEncoder();
function ar(e) {
  return ir.encode(e);
}
function lr(e, t) {
  return new TextDecoder(t).decode(e);
}
function cr(e) {
  return e.buffer.slice(e.byteOffset, e.byteOffset + e.byteLength);
}
var W = Symbol('isPatchedModule');
function Je(e) {
  try {
    return new URL(e), !0;
  } catch {
    return !1;
  }
}
function Ae(e, t) {
  const s = Object.getOwnPropertySymbols(t).find((o) => o.description === e);
  if (s) return Reflect.get(t, s);
}
var M = class extends Response {
    static isConfigurableStatusCode(e) {
      return e >= 200 && e <= 599;
    }
    static isRedirectResponse(e) {
      return M.STATUS_CODES_WITH_REDIRECT.includes(e);
    }
    static isResponseWithBody(e) {
      return !M.STATUS_CODES_WITHOUT_BODY.includes(e);
    }
    static setUrl(e, t) {
      if (!e || e === 'about:' || !Je(e)) return;
      const r = Ae('state', t);
      r
        ? r.urlList.push(new URL(e))
        : Object.defineProperty(t, 'url', {
            value: e,
            enumerable: !0,
            configurable: !0,
            writable: !1,
          });
    }
    static parseRawHeaders(e) {
      const t = new Headers();
      for (let r = 0; r < e.length; r += 2) t.append(e[r], e[r + 1]);
      return t;
    }
    constructor(e, t = {}) {
      var r;
      const s = (r = t.status) != null ? r : 200,
        o = M.isConfigurableStatusCode(s) ? s : 200,
        n = M.isResponseWithBody(s) ? e : null;
      if (
        (super(n, { status: o, statusText: t.statusText, headers: t.headers }),
        s !== o)
      ) {
        const i = Ae('state', this);
        i
          ? (i.status = s)
          : Object.defineProperty(this, 'status', {
              value: s,
              enumerable: !0,
              configurable: !0,
              writable: !1,
            });
      }
      M.setUrl(t.url, this);
    }
  },
  E = M;
E.STATUS_CODES_WITHOUT_BODY = [101, 103, 204, 205, 304];
E.STATUS_CODES_WITH_REDIRECT = [301, 302, 303, 307, 308];
var ur = Symbol('kRawRequest');
function Ye(e, t) {
  Reflect.set(e, ur, t);
}
var hr = Object.defineProperty,
  dr = (e, t) => {
    for (var r in t) hr(e, r, { get: t[r], enumerable: !0 });
  },
  ye = {};
dr(ye, {
  blue: () => pr,
  gray: () => be,
  green: () => mr,
  red: () => gr,
  yellow: () => fr,
});
function fr(e) {
  return `\x1B[33m${e}\x1B[0m`;
}
function pr(e) {
  return `\x1B[34m${e}\x1B[0m`;
}
function be(e) {
  return `\x1B[90m${e}\x1B[0m`;
}
function gr(e) {
  return `\x1B[31m${e}\x1B[0m`;
}
function mr(e) {
  return `\x1B[32m${e}\x1B[0m`;
}
var oe = ke(),
  Qe = class {
    constructor(e) {
      f(this, 'prefix');
      (this.name = e), (this.prefix = `[${this.name}]`);
      const t = We('DEBUG'),
        r = We('LOG_LEVEL');
      t === '1' ||
      t === 'true' ||
      (typeof t != 'undefined' && this.name.startsWith(t))
        ? ((this.debug = B(r, 'debug') ? y : this.debug),
          (this.info = B(r, 'info') ? y : this.info),
          (this.success = B(r, 'success') ? y : this.success),
          (this.warning = B(r, 'warning') ? y : this.warning),
          (this.error = B(r, 'error') ? y : this.error))
        : ((this.info = y),
          (this.success = y),
          (this.warning = y),
          (this.error = y),
          (this.only = y));
    }
    extend(e) {
      return new Qe(`${this.name}:${e}`);
    }
    debug(e, ...t) {
      this.logEntry({
        level: 'debug',
        message: be(e),
        positionals: t,
        prefix: this.prefix,
        colors: { prefix: 'gray' },
      });
    }
    info(e, ...t) {
      this.logEntry({
        level: 'info',
        message: e,
        positionals: t,
        prefix: this.prefix,
        colors: { prefix: 'blue' },
      });
      const r = new yr();
      return (s, ...o) => {
        r.measure(),
          this.logEntry({
            level: 'info',
            message: `${s} ${be(`${r.deltaTime}ms`)}`,
            positionals: o,
            prefix: this.prefix,
            colors: { prefix: 'blue' },
          });
      };
    }
    success(e, ...t) {
      this.logEntry({
        level: 'info',
        message: e,
        positionals: t,
        prefix: `\u2714 ${this.prefix}`,
        colors: { timestamp: 'green', prefix: 'green' },
      });
    }
    warning(e, ...t) {
      this.logEntry({
        level: 'warning',
        message: e,
        positionals: t,
        prefix: `\u26A0 ${this.prefix}`,
        colors: { timestamp: 'yellow', prefix: 'yellow' },
      });
    }
    error(e, ...t) {
      this.logEntry({
        level: 'error',
        message: e,
        positionals: t,
        prefix: `\u2716 ${this.prefix}`,
        colors: { timestamp: 'red', prefix: 'red' },
      });
    }
    only(e) {
      e();
    }
    createEntry(e, t) {
      return { timestamp: new Date(), level: e, message: t };
    }
    logEntry(e) {
      const {
          level: t,
          message: r,
          prefix: s,
          colors: o,
          positionals: n = [],
        } = e,
        i = this.createEntry(t, r),
        a = (o == null ? void 0 : o.timestamp) || 'gray',
        l = (o == null ? void 0 : o.prefix) || 'gray',
        u = { timestamp: ye[a], prefix: ye[l] };
      this.getWriter(t)(
        [u.timestamp(this.formatTimestamp(i.timestamp))]
          .concat(s != null ? u.prefix(s) : [])
          .concat(_e(r))
          .join(' '),
        ...n.map(_e)
      );
    }
    formatTimestamp(e) {
      return `${e.toLocaleTimeString('en-GB')}:${e.getMilliseconds()}`;
    }
    getWriter(e) {
      switch (e) {
        case 'debug':
        case 'success':
        case 'info':
          return br;
        case 'warning':
          return vr;
        case 'error':
          return wr;
      }
    }
  },
  yr = class {
    constructor() {
      f(this, 'startTime');
      f(this, 'endTime');
      f(this, 'deltaTime');
      this.startTime = performance.now();
    }
    measure() {
      this.endTime = performance.now();
      const e = this.endTime - this.startTime;
      this.deltaTime = e.toFixed(2);
    }
  },
  y = () => {};
function br(e, ...t) {
  if (oe) {
    process.stdout.write(
      F(e, ...t) +
        `
`
    );
    return;
  }
  console.log(e, ...t);
}
function vr(e, ...t) {
  if (oe) {
    process.stderr.write(
      F(e, ...t) +
        `
`
    );
    return;
  }
  console.warn(e, ...t);
}
function wr(e, ...t) {
  if (oe) {
    process.stderr.write(
      F(e, ...t) +
        `
`
    );
    return;
  }
  console.error(e, ...t);
}
function We(e) {
  var t;
  return oe
    ? process.env[e]
    : (t = globalThis[e]) == null
    ? void 0
    : t.toString();
}
function B(e, t) {
  return e !== void 0 && e !== t;
}
function _e(e) {
  return typeof e == 'undefined'
    ? 'undefined'
    : e === null
    ? 'null'
    : typeof e == 'string'
    ? e
    : typeof e == 'object'
    ? JSON.stringify(e)
    : e.toString();
}
var Er = class extends Error {
    constructor(e, t, r) {
      super(
        `Possible EventEmitter memory leak detected. ${r} ${t.toString()} listeners added. Use emitter.setMaxListeners() to increase limit`
      ),
        (this.emitter = e),
        (this.type = t),
        (this.count = r),
        (this.name = 'MaxListenersExceededWarning');
    }
  },
  Ze = class {
    static listenerCount(e, t) {
      return e.listenerCount(t);
    }
    constructor() {
      (this.events = new Map()),
        (this.maxListeners = Ze.defaultMaxListeners),
        (this.hasWarnedAboutPotentialMemoryLeak = !1);
    }
    _emitInternalEvent(e, t, r) {
      this.emit(e, t, r);
    }
    _getListeners(e) {
      return Array.prototype.concat.apply([], this.events.get(e)) || [];
    }
    _removeListener(e, t) {
      const r = e.indexOf(t);
      return r > -1 && e.splice(r, 1), [];
    }
    _wrapOnceListener(e, t) {
      const r = (...s) => (this.removeListener(e, r), t.apply(this, s));
      return Object.defineProperty(r, 'name', { value: t.name }), r;
    }
    setMaxListeners(e) {
      return (this.maxListeners = e), this;
    }
    getMaxListeners() {
      return this.maxListeners;
    }
    eventNames() {
      return Array.from(this.events.keys());
    }
    emit(e, ...t) {
      const r = this._getListeners(e);
      return (
        r.forEach((s) => {
          s.apply(this, t);
        }),
        r.length > 0
      );
    }
    addListener(e, t) {
      this._emitInternalEvent('newListener', e, t);
      const r = this._getListeners(e).concat(t);
      if (
        (this.events.set(e, r),
        this.maxListeners > 0 &&
          this.listenerCount(e) > this.maxListeners &&
          !this.hasWarnedAboutPotentialMemoryLeak)
      ) {
        this.hasWarnedAboutPotentialMemoryLeak = !0;
        const s = new Er(this, e, this.listenerCount(e));
        console.warn(s);
      }
      return this;
    }
    on(e, t) {
      return this.addListener(e, t);
    }
    once(e, t) {
      return this.addListener(e, this._wrapOnceListener(e, t));
    }
    prependListener(e, t) {
      const r = this._getListeners(e);
      if (r.length > 0) {
        const s = [t].concat(r);
        this.events.set(e, s);
      } else this.events.set(e, r.concat(t));
      return this;
    }
    prependOnceListener(e, t) {
      return this.prependListener(e, this._wrapOnceListener(e, t));
    }
    removeListener(e, t) {
      const r = this._getListeners(e);
      return (
        r.length > 0 &&
          (this._removeListener(r, t),
          this.events.set(e, r),
          this._emitInternalEvent('removeListener', e, t)),
        this
      );
    }
    off(e, t) {
      return this.removeListener(e, t);
    }
    removeAllListeners(e) {
      return e ? this.events.delete(e) : this.events.clear(), this;
    }
    listeners(e) {
      return Array.from(this._getListeners(e));
    }
    listenerCount(e) {
      return this._getListeners(e).length;
    }
    rawListeners(e) {
      return this.listeners(e);
    }
  },
  et = Ze;
et.defaultMaxListeners = 10;
var kr = 'x-interceptors-internal-request-id';
function De(e) {
  return globalThis[e] || void 0;
}
function Sr(e, t) {
  globalThis[e] = t;
}
function Lr(e) {
  delete globalThis[e];
}
var Se = class {
  constructor(e) {
    (this.symbol = e),
      (this.readyState = 'INACTIVE'),
      (this.emitter = new et()),
      (this.subscriptions = []),
      (this.logger = new Qe(e.description)),
      this.emitter.setMaxListeners(0),
      this.logger.info('constructing the interceptor...');
  }
  checkEnvironment() {
    return !0;
  }
  apply() {
    const e = this.logger.extend('apply');
    if (
      (e.info('applying the interceptor...'), this.readyState === 'APPLIED')
    ) {
      e.info('intercepted already applied!');
      return;
    }
    if (!this.checkEnvironment()) {
      e.info('the interceptor cannot be applied in this environment!');
      return;
    }
    this.readyState = 'APPLYING';
    const r = this.getInstance();
    if (r) {
      e.info('found a running instance, reusing...'),
        (this.on = (s, o) => (
          e.info('proxying the "%s" listener', s),
          r.emitter.addListener(s, o),
          this.subscriptions.push(() => {
            r.emitter.removeListener(s, o),
              e.info('removed proxied "%s" listener!', s);
          }),
          this
        )),
        (this.readyState = 'APPLIED');
      return;
    }
    e.info('no running instance found, setting up a new instance...'),
      this.setup(),
      this.setInstance(),
      (this.readyState = 'APPLIED');
  }
  setup() {}
  on(e, t) {
    const r = this.logger.extend('on');
    return this.readyState === 'DISPOSING' || this.readyState === 'DISPOSED'
      ? (r.info('cannot listen to events, already disposed!'), this)
      : (r.info('adding "%s" event listener:', e, t),
        this.emitter.on(e, t),
        this);
  }
  once(e, t) {
    return this.emitter.once(e, t), this;
  }
  off(e, t) {
    return this.emitter.off(e, t), this;
  }
  removeAllListeners(e) {
    return this.emitter.removeAllListeners(e), this;
  }
  dispose() {
    const e = this.logger.extend('dispose');
    if (this.readyState === 'DISPOSED') {
      e.info('cannot dispose, already disposed!');
      return;
    }
    if (
      (e.info('disposing the interceptor...'),
      (this.readyState = 'DISPOSING'),
      !this.getInstance())
    ) {
      e.info('no interceptors running, skipping dispose...');
      return;
    }
    if (
      (this.clearInstance(),
      e.info('global symbol deleted:', De(this.symbol)),
      this.subscriptions.length > 0)
    ) {
      e.info('disposing of %d subscriptions...', this.subscriptions.length);
      for (const t of this.subscriptions) t();
      (this.subscriptions = []),
        e.info('disposed of all subscriptions!', this.subscriptions.length);
    }
    this.emitter.removeAllListeners(),
      e.info('destroyed the listener!'),
      (this.readyState = 'DISPOSED');
  }
  getInstance() {
    var e;
    const t = De(this.symbol);
    return (
      this.logger.info(
        'retrieved global instance:',
        (e = t == null ? void 0 : t.constructor) == null ? void 0 : e.name
      ),
      t
    );
  }
  setInstance() {
    Sr(this.symbol, this),
      this.logger.info('set global instance!', this.symbol.description);
  }
  clearInstance() {
    Lr(this.symbol),
      this.logger.info('cleared global instance!', this.symbol.description);
  }
};
function tt() {
  return Math.random().toString(16).slice(2);
}
var ve = class extends Se {
  constructor(e) {
    (ve.symbol = Symbol(e.name)),
      super(ve.symbol),
      (this.interceptors = e.interceptors);
  }
  setup() {
    const e = this.logger.extend('setup');
    e.info('applying all %d interceptors...', this.interceptors.length);
    for (const t of this.interceptors)
      e.info('applying "%s" interceptor...', t.constructor.name),
        t.apply(),
        e.info('adding interceptor dispose subscription'),
        this.subscriptions.push(() => t.dispose());
  }
  on(e, t) {
    for (const r of this.interceptors) r.on(e, t);
    return this;
  }
  once(e, t) {
    for (const r of this.interceptors) r.once(e, t);
    return this;
  }
  off(e, t) {
    for (const r of this.interceptors) r.off(e, t);
    return this;
  }
  removeAllListeners(e) {
    for (const t of this.interceptors) t.removeAllListeners(e);
    return this;
  }
};
function Rr(e) {
  return (t, r) => {
    var i;
    const { payload: s } = r,
      o = Ke(s.request);
    if ((i = s.response.type) != null && i.includes('opaque')) return;
    const n =
      s.response.status === 0
        ? Response.error()
        : new E(
            E.isResponseWithBody(s.response.status) ? s.response.body : null,
            { ...s, url: o.url }
          );
    e.emitter.emit(s.isMockedResponse ? 'response:mocked' : 'response:bypass', {
      requestId: s.request.id,
      request: o,
      response: n,
    });
  };
}
function Cr(e, t) {
  !(t != null && t.quiet) &&
    !location.href.startsWith(e.scope) &&
    h.warn(`Cannot intercept requests on this page because it's outside of the worker's scope ("${e.scope}"). If you wish to mock API requests on this page, you must resolve this scope issue.

- (Recommended) Register the worker at the root level ("/") of your application.
- Set the "Service-Worker-Allowed" response header to allow out-of-scope workers.`);
}
var Tr = (e) =>
  function (r, s) {
    return (async () => {
      e.events.removeAllListeners(),
        e.workerChannel.on('REQUEST', nr(e, r)),
        e.workerChannel.on('RESPONSE', Rr(e));
      const i = await er(
          r.serviceWorker.url,
          r.serviceWorker.options,
          r.findWorker
        ),
        [a, l] = i;
      if (!a) {
        const u =
          s != null && s.findWorker
            ? h.formatMessage(
                `Failed to locate the Service Worker registration using a custom "findWorker" predicate.

Please ensure that the custom predicate properly locates the Service Worker registration at "%s".
More details: https://mswjs.io/docs/api/setup-worker/start#findworker
`,
                r.serviceWorker.url
              )
            : h.formatMessage(
                `Failed to locate the Service Worker registration.

This most likely means that the worker script URL "%s" cannot resolve against the actual public hostname (%s). This may happen if your application runs behind a proxy, or has a dynamic hostname.

Please consider using a custom "serviceWorker.url" option to point to the actual worker script location, or a custom "findWorker" option to resolve the Service Worker registration manually. More details: https://mswjs.io/docs/api/setup-worker/start`,
                r.serviceWorker.url,
                location.host
              );
        throw new Error(u);
      }
      return (
        (e.worker = a),
        (e.registration = l),
        e.events.addListener(window, 'beforeunload', () => {
          a.state !== 'redundant' && e.workerChannel.send('CLIENT_CLOSED'),
            window.clearInterval(e.keepAliveInterval),
            window.postMessage({ type: 'msw/worker:stop' });
        }),
        await or(e).catch((u) => {
          h.error(
            'Error while checking the worker script integrity. Please report this on GitHub (https://github.com/mswjs/msw/issues), including the original error below.'
          ),
            console.error(u);
        }),
        (e.keepAliveInterval = window.setInterval(
          () => e.workerChannel.send('KEEPALIVE_REQUEST'),
          5e3
        )),
        Cr(l, e.startOptions),
        l
      );
    })().then(async (i) => {
      const a = i.installing || i.waiting;
      return (
        a &&
          (await new Promise((l) => {
            a.addEventListener('statechange', () => {
              if (a.state === 'activated') return l();
            });
          })),
        await tr(e, r).catch((l) => {
          throw new Error(
            `Failed to enable mocking: ${l == null ? void 0 : l.message}`
          );
        }),
        i
      );
    });
  };
function rt(e = {}) {
  e.quiet ||
    console.log(
      `%c${h.formatMessage('Mocking disabled.')}`,
      'color:orangered;font-weight:bold;'
    );
}
var Pr = (e) =>
    function () {
      var r;
      if (!e.isMockingEnabled) {
        h.warn(
          'Found a redundant "worker.stop()" call. Note that stopping the worker while mocking already stopped has no effect. Consider removing this "worker.stop()" call.'
        );
        return;
      }
      e.workerChannel.send('MOCK_DEACTIVATE'),
        (e.isMockingEnabled = !1),
        window.clearInterval(e.keepAliveInterval),
        window.postMessage({ type: 'msw/worker:stop' }),
        rt({ quiet: (r = e.startOptions) == null ? void 0 : r.quiet });
    },
  qr = {
    serviceWorker: { url: '/mockServiceWorker.js', options: null },
    quiet: !1,
    waitUntilReady: !0,
    onUnhandledRequest: 'warn',
    findWorker(e, t) {
      return e === t;
    },
  };
function xr() {
  const e = (t, r) => {
    (e.state = 'pending'),
      (e.resolve = (s) => {
        if (e.state !== 'pending') return;
        e.result = s;
        const o = (n) => ((e.state = 'fulfilled'), n);
        return t(s instanceof Promise ? s : Promise.resolve(s).then(o));
      }),
      (e.reject = (s) => {
        if (e.state === 'pending')
          return (
            queueMicrotask(() => {
              e.state = 'rejected';
            }),
            r((e.rejectionReason = s))
          );
      });
  };
  return e;
}
var L,
  D,
  J,
  $e,
  Le =
    (($e = class extends Promise {
      constructor(t = null) {
        const r = xr();
        super((s, o) => {
          r(s, o), t == null || t(r.resolve, r.reject);
        });
        j(this, D);
        j(this, L, void 0);
        f(this, 'resolve');
        f(this, 'reject');
        ae(this, L, r),
          (this.resolve = w(this, L).resolve),
          (this.reject = w(this, L).reject);
      }
      get state() {
        return w(this, L).state;
      }
      get rejectionReason() {
        return w(this, L).rejectionReason;
      }
      then(t, r) {
        return T(this, D, J).call(this, super.then(t, r));
      }
      catch(t) {
        return T(this, D, J).call(this, super.catch(t));
      }
      finally(t) {
        return T(this, D, J).call(this, super.finally(t));
      }
    }),
    (L = new WeakMap()),
    (D = new WeakSet()),
    (J = function (t) {
      return Object.defineProperties(t, {
        resolve: { configurable: !0, value: this.resolve },
        reject: { configurable: !0, value: this.reject },
      });
    }),
    $e),
  Z = class extends Error {
    constructor(e) {
      super(e),
        (this.name = 'InterceptorError'),
        Object.setPrototypeOf(this, Z.prototype);
    }
  },
  $ = Symbol('kRequestHandled'),
  b = Symbol('kResponsePromise'),
  Re = class {
    constructor(e) {
      (this.request = e), (this[$] = !1), (this[b] = new Le());
    }
    respondWith(e) {
      C.as(
        Z,
        !this[$],
        'Failed to respond to the "%s %s" request: the "request" event has already been handled.',
        this.request.method,
        this.request.url
      ),
        (this[$] = !0),
        this[b].resolve(e);
    }
    errorWith(e) {
      C.as(
        Z,
        !this[$],
        'Failed to error the "%s %s" request: the "request" event has already been handled.',
        this.request.method,
        this.request.url
      ),
        (this[$] = !0),
        this[b].resolve(e);
    }
  };
async function ee(e, t, ...r) {
  const s = e.listeners(t);
  if (s.length !== 0) for (const o of s) await o.apply(e, r);
}
function st(e, t = !1) {
  return t
    ? Object.prototype.toString.call(e).startsWith('[object ')
    : Object.prototype.toString.call(e) === '[object Object]';
}
function Y(e, t) {
  try {
    return e[t], !0;
  } catch {
    return !1;
  }
}
function Ir(e) {
  return new Response(
    JSON.stringify(
      e instanceof Error
        ? { name: e.name, message: e.message, stack: e.stack }
        : e
    ),
    {
      status: 500,
      statusText: 'Unhandled Exception',
      headers: { 'Content-Type': 'application/json' },
    }
  );
}
function Mr(e) {
  return (
    e != null && e instanceof Response && Y(e, 'type') && e.type === 'error'
  );
}
function Ar(e) {
  return st(e, !0) && Y(e, 'status') && Y(e, 'statusText') && Y(e, 'bodyUsed');
}
function Wr(e) {
  return e == null || !(e instanceof Error) ? !1 : 'code' in e && 'errno' in e;
}
async function nt(e) {
  const t = async (n) =>
      n instanceof Error
        ? (e.onError(n), !0)
        : Mr(n)
        ? (e.onRequestError(n), !0)
        : Ar(n)
        ? (await e.onResponse(n), !0)
        : st(n)
        ? (e.onError(n), !0)
        : !1,
    r = async (n) => {
      if (n instanceof Z) throw o.error;
      return Wr(n)
        ? (e.onError(n), !0)
        : n instanceof Response
        ? await t(n)
        : !1;
    };
  e.emitter.once('request', ({ requestId: n }) => {
    n === e.requestId &&
      e.controller[b].state === 'pending' &&
      e.controller[b].resolve(void 0);
  });
  const s = new Le();
  e.request.signal &&
    (e.request.signal.aborted
      ? s.reject(e.request.signal.reason)
      : e.request.signal.addEventListener(
          'abort',
          () => {
            s.reject(e.request.signal.reason);
          },
          { once: !0 }
        ));
  const o = await me(async () => {
    const n = ee(e.emitter, 'request', {
      requestId: e.requestId,
      request: e.request,
      controller: e.controller,
    });
    return await Promise.race([s, n, e.controller[b]]), await e.controller[b];
  });
  if (s.state === 'rejected') return e.onError(s.rejectionReason), !0;
  if (o.error) {
    if (await r(o.error)) return !0;
    if (e.emitter.listenerCount('unhandledException') > 0) {
      const n = new Re(e.request);
      await ee(e.emitter, 'unhandledException', {
        error: o.error,
        request: e.request,
        requestId: e.requestId,
        controller: n,
      }).then(() => {
        n[b].state === 'pending' && n[b].resolve(void 0);
      });
      const i = await me(() => n[b]);
      if (i.error) return r(i.error);
      if (i.data) return t(i.data);
    }
    return e.onResponse(Ir(o.error)), !0;
  }
  return o.data ? t(o.data) : !1;
}
function ot(e) {
  const t = Object.getOwnPropertyDescriptor(globalThis, e);
  return typeof t == 'undefined' ||
    (typeof t.get == 'function' && typeof t.get() == 'undefined') ||
    (typeof t.get == 'undefined' && t.value == null)
    ? !1
    : typeof t.set == 'undefined' && !t.configurable
    ? (console.error(
        `[MSW] Failed to apply interceptor: the global \`${e}\` property is non-configurable. This is likely an issue with your environment. If you are using a framework, please open an issue about this in their repository.`
      ),
      !1)
    : !0;
}
function P(e) {
  return Object.assign(new TypeError('Failed to fetch'), { cause: e });
}
var _r = [
    'content-encoding',
    'content-language',
    'content-location',
    'content-type',
    'content-length',
  ],
  he = Symbol('kRedirectCount');
async function Dr(e, t) {
  if (t.status !== 303 && e.body != null) return Promise.reject(P());
  const r = new URL(e.url);
  let s;
  try {
    s = new URL(t.headers.get('location'), e.url);
  } catch (n) {
    return Promise.reject(P(n));
  }
  if (!(s.protocol === 'http:' || s.protocol === 'https:'))
    return Promise.reject(P('URL scheme must be a HTTP(S) scheme'));
  if (Reflect.get(e, he) > 20)
    return Promise.reject(P('redirect count exceeded'));
  if (
    (Object.defineProperty(e, he, { value: (Reflect.get(e, he) || 0) + 1 }),
    e.mode === 'cors' && (s.username || s.password) && !Oe(r, s))
  )
    return Promise.reject(
      P('cross origin not allowed for request mode "cors"')
    );
  const o = {};
  return (
    (([301, 302].includes(t.status) && e.method === 'POST') ||
      (t.status === 303 && !['HEAD', 'GET'].includes(e.method))) &&
      ((o.method = 'GET'),
      (o.body = null),
      _r.forEach((n) => {
        e.headers.delete(n);
      })),
    Oe(r, s) ||
      (e.headers.delete('authorization'),
      e.headers.delete('proxy-authorization'),
      e.headers.delete('cookie'),
      e.headers.delete('host')),
    (o.headers = e.headers),
    fetch(new Request(s, o))
  );
}
function Oe(e, t) {
  return (
    (e.origin === t.origin && e.origin === 'null') ||
    (e.protocol === t.protocol &&
      e.hostname === t.hostname &&
      e.port === t.port)
  );
}
var Or = class extends TransformStream {
    constructor() {
      console.warn(
        '[Interceptors]: Brotli decompression of response streams is not supported in the browser'
      ),
        super({
          transform(e, t) {
            t.enqueue(e);
          },
        });
    }
  },
  jr = class extends TransformStream {
    constructor(e, ...t) {
      super({}, ...t);
      const r = [super.readable, ...e].reduce((s, o) => s.pipeThrough(o));
      Object.defineProperty(this, 'readable', {
        get() {
          return r;
        },
      });
    }
  };
function Hr(e) {
  return e
    .toLowerCase()
    .split(',')
    .map((t) => t.trim());
}
function Br(e) {
  if (e === '') return null;
  const t = Hr(e);
  if (t.length === 0) return null;
  const r = t.reduceRight(
    (s, o) =>
      o === 'gzip' || o === 'x-gzip'
        ? s.concat(new DecompressionStream('gzip'))
        : o === 'deflate'
        ? s.concat(new DecompressionStream('deflate'))
        : o === 'br'
        ? s.concat(new Or())
        : ((s.length = 0), s),
    []
  );
  return new jr(r);
}
function $r(e) {
  if (e.body === null) return null;
  const t = Br(e.headers.get('content-encoding') || '');
  return t ? (e.body.pipeTo(t.writable), t.readable) : null;
}
var it = class extends Se {
    constructor() {
      super(it.symbol);
    }
    checkEnvironment() {
      return ot('fetch');
    }
    async setup() {
      const e = globalThis.fetch;
      C(!e[W], 'Failed to patch the "fetch" module: already patched.'),
        (globalThis.fetch = async (t, r) => {
          const s = tt(),
            o =
              typeof t == 'string' && typeof location != 'undefined' && !Je(t)
                ? new URL(t, location.href)
                : t,
            n = new Request(o, r);
          t instanceof Request && Ye(n, t);
          const i = new Le(),
            a = new Re(n);
          if (
            (this.logger.info('[%s] %s', n.method, n.url),
            this.logger.info('awaiting for the mocked response...'),
            this.logger.info(
              'emitting the "request" event for %s listener(s)...',
              this.emitter.listenerCount('request')
            ),
            await nt({
              request: n,
              requestId: s,
              emitter: this.emitter,
              controller: a,
              onResponse: async (c) => {
                this.logger.info('received mocked response!', {
                  rawResponse: c,
                });
                const d = $r(c),
                  p = d === null ? c : new E(d, c);
                if ((E.setUrl(n.url, p), E.isRedirectResponse(p.status))) {
                  if (n.redirect === 'error') {
                    i.reject(P('unexpected redirect'));
                    return;
                  }
                  if (n.redirect === 'follow') {
                    Dr(n, p).then(
                      (m) => {
                        i.resolve(m);
                      },
                      (m) => {
                        i.reject(m);
                      }
                    );
                    return;
                  }
                }
                this.emitter.listenerCount('response') > 0 &&
                  (this.logger.info('emitting the "response" event...'),
                  await ee(this.emitter, 'response', {
                    response: p.clone(),
                    isMockedResponse: !0,
                    request: n,
                    requestId: s,
                  })),
                  i.resolve(p);
              },
              onRequestError: (c) => {
                this.logger.info('request has errored!', { response: c }),
                  i.reject(P(c));
              },
              onError: (c) => {
                this.logger.info('request has been aborted!', { error: c }),
                  i.reject(c);
              },
            }))
          )
            return (
              this.logger.info(
                'request has been handled, returning mock promise...'
              ),
              i
            );
          this.logger.info(
            'no mocked response received, performing request as-is...'
          );
          const u = n.clone();
          return e(n).then(async (c) => {
            if (
              (this.logger.info('original fetch performed', c),
              this.emitter.listenerCount('response') > 0)
            ) {
              this.logger.info('emitting the "response" event...');
              const d = c.clone();
              await ee(this.emitter, 'response', {
                response: d,
                isMockedResponse: !1,
                request: u,
                requestId: s,
              });
            }
            return c;
          });
        }),
        Object.defineProperty(globalThis.fetch, W, {
          enumerable: !0,
          configurable: !0,
          value: !0,
        }),
        this.subscriptions.push(() => {
          Object.defineProperty(globalThis.fetch, W, { value: void 0 }),
            (globalThis.fetch = e),
            this.logger.info(
              'restored native "globalThis.fetch"!',
              globalThis.fetch.name
            );
        });
    }
  },
  at = it;
at.symbol = Symbol('fetch');
function Ur(e, t) {
  const r = new Uint8Array(e.byteLength + t.byteLength);
  return r.set(e, 0), r.set(t, e.byteLength), r;
}
var lt = class {
    constructor(e, t) {
      (this.NONE = 0),
        (this.CAPTURING_PHASE = 1),
        (this.AT_TARGET = 2),
        (this.BUBBLING_PHASE = 3),
        (this.type = ''),
        (this.srcElement = null),
        (this.currentTarget = null),
        (this.eventPhase = 0),
        (this.isTrusted = !0),
        (this.composed = !1),
        (this.cancelable = !0),
        (this.defaultPrevented = !1),
        (this.bubbles = !0),
        (this.lengthComputable = !0),
        (this.loaded = 0),
        (this.total = 0),
        (this.cancelBubble = !1),
        (this.returnValue = !0),
        (this.type = e),
        (this.target = (t == null ? void 0 : t.target) || null),
        (this.currentTarget = (t == null ? void 0 : t.currentTarget) || null),
        (this.timeStamp = Date.now());
    }
    composedPath() {
      return [];
    }
    initEvent(e, t, r) {
      (this.type = e), (this.bubbles = !!t), (this.cancelable = !!r);
    }
    preventDefault() {
      this.defaultPrevented = !0;
    }
    stopPropagation() {}
    stopImmediatePropagation() {}
  },
  Nr = class extends lt {
    constructor(e, t) {
      super(e),
        (this.lengthComputable =
          (t == null ? void 0 : t.lengthComputable) || !1),
        (this.composed = (t == null ? void 0 : t.composed) || !1),
        (this.loaded = (t == null ? void 0 : t.loaded) || 0),
        (this.total = (t == null ? void 0 : t.total) || 0);
    }
  },
  Gr = typeof ProgressEvent != 'undefined';
function Fr(e, t, r) {
  const s = [
      'error',
      'progress',
      'loadstart',
      'loadend',
      'load',
      'timeout',
      'abort',
    ],
    o = Gr ? ProgressEvent : Nr;
  return s.includes(t)
    ? new o(t, {
        lengthComputable: !0,
        loaded: (r == null ? void 0 : r.loaded) || 0,
        total: (r == null ? void 0 : r.total) || 0,
      })
    : new lt(t, { target: e, currentTarget: e });
}
function ct(e, t) {
  if (!(t in e)) return null;
  if (Object.prototype.hasOwnProperty.call(e, t)) return e;
  const s = Reflect.getPrototypeOf(e);
  return s ? ct(s, t) : null;
}
function de(e, t) {
  return new Proxy(e, Xr(t));
}
function Xr(e) {
  const {
      constructorCall: t,
      methodCall: r,
      getProperty: s,
      setProperty: o,
    } = e,
    n = {};
  return (
    typeof t != 'undefined' &&
      (n.construct = function (i, a, l) {
        const u = Reflect.construct.bind(null, i, a, l);
        return t.call(l, a, u);
      }),
    (n.set = function (i, a, l) {
      const u = () => {
        const c = ct(i, a) || i,
          d = Reflect.getOwnPropertyDescriptor(c, a);
        return typeof (d == null ? void 0 : d.set) != 'undefined'
          ? (d.set.apply(i, [l]), !0)
          : Reflect.defineProperty(c, a, {
              writable: !0,
              enumerable: !0,
              configurable: !0,
              value: l,
            });
      };
      return typeof o != 'undefined' ? o.call(i, [a, l], u) : u();
    }),
    (n.get = function (i, a, l) {
      const u = () => i[a],
        c = typeof s != 'undefined' ? s.call(i, [a, l], u) : u();
      return typeof c == 'function'
        ? (...d) => {
            const p = c.bind(i, ...d);
            return typeof r != 'undefined' ? r.call(i, [a, d], p) : p();
          }
        : c;
    }),
    n
  );
}
function zr(e) {
  return [
    'application/xhtml+xml',
    'application/xml',
    'image/svg+xml',
    'text/html',
    'text/xml',
  ].some((r) => e.startsWith(r));
}
function Vr(e) {
  try {
    return JSON.parse(e);
  } catch {
    return null;
  }
}
function Kr(e, t) {
  const r = E.isResponseWithBody(e.status) ? t : null;
  return new E(r, {
    url: e.responseURL,
    status: e.status,
    statusText: e.statusText,
    headers: Jr(e.getAllResponseHeaders()),
  });
}
function Jr(e) {
  const t = new Headers(),
    r = e.split(/[\r\n]+/);
  for (const s of r) {
    if (s.trim() === '') continue;
    const [o, ...n] = s.split(': '),
      i = n.join(': ');
    t.append(o, i);
  }
  return t;
}
async function je(e) {
  const t = e.headers.get('content-length');
  return t != null && t !== '' ? Number(t) : (await e.arrayBuffer()).byteLength;
}
var U = Symbol('kIsRequestHandled'),
  Yr = ke(),
  fe = Symbol('kFetchRequest'),
  Qr = class {
    constructor(e, t) {
      (this.initialRequest = e),
        (this.logger = t),
        (this.method = 'GET'),
        (this.url = null),
        (this[U] = !1),
        (this.events = new Map()),
        (this.uploadEvents = new Map()),
        (this.requestId = tt()),
        (this.requestHeaders = new Headers()),
        (this.responseBuffer = new Uint8Array()),
        (this.request = de(e, {
          setProperty: ([r, s], o) => {
            switch (r) {
              case 'ontimeout': {
                const n = r.slice(2);
                return this.request.addEventListener(n, s), o();
              }
              default:
                return o();
            }
          },
          methodCall: ([r, s], o) => {
            var n;
            switch (r) {
              case 'open': {
                const [i, a] = s;
                return (
                  typeof a == 'undefined'
                    ? ((this.method = 'GET'), (this.url = He(i)))
                    : ((this.method = i), (this.url = He(a))),
                  (this.logger = this.logger.extend(
                    `${this.method} ${this.url.href}`
                  )),
                  this.logger.info('open', this.method, this.url.href),
                  o()
                );
              }
              case 'addEventListener': {
                const [i, a] = s;
                return (
                  this.registerEvent(i, a),
                  this.logger.info('addEventListener', i, a),
                  o()
                );
              }
              case 'setRequestHeader': {
                const [i, a] = s;
                return (
                  this.requestHeaders.set(i, a),
                  this.logger.info('setRequestHeader', i, a),
                  o()
                );
              }
              case 'send': {
                const [i] = s;
                this.request.addEventListener('load', () => {
                  if (typeof this.onResponse != 'undefined') {
                    const c = Kr(this.request, this.request.response);
                    this.onResponse.call(this, {
                      response: c,
                      isMockedResponse: this[U],
                      request: l,
                      requestId: this.requestId,
                    });
                  }
                });
                const a = typeof i == 'string' ? ar(i) : i,
                  l = this.toFetchApiRequest(a);
                (this[fe] = l.clone()),
                  (
                    ((n = this.onRequest) == null
                      ? void 0
                      : n.call(this, {
                          request: l,
                          requestId: this.requestId,
                        })) || Promise.resolve()
                  ).finally(() => {
                    if (!this[U])
                      return (
                        this.logger.info(
                          'request callback settled but request has not been handled (readystate %d), performing as-is...',
                          this.request.readyState
                        ),
                        Yr && this.request.setRequestHeader(kr, this.requestId),
                        o()
                      );
                  });
                break;
              }
              default:
                return o();
            }
          },
        })),
        I(
          this.request,
          'upload',
          de(this.request.upload, {
            setProperty: ([r, s], o) => {
              switch (r) {
                case 'onloadstart':
                case 'onprogress':
                case 'onaboart':
                case 'onerror':
                case 'onload':
                case 'ontimeout':
                case 'onloadend': {
                  const n = r.slice(2);
                  this.registerUploadEvent(n, s);
                }
              }
              return o();
            },
            methodCall: ([r, s], o) => {
              switch (r) {
                case 'addEventListener': {
                  const [n, i] = s;
                  return (
                    this.registerUploadEvent(n, i),
                    this.logger.info('upload.addEventListener', n, i),
                    o()
                  );
                }
              }
            },
          })
        );
    }
    registerEvent(e, t) {
      const s = (this.events.get(e) || []).concat(t);
      this.events.set(e, s), this.logger.info('registered event "%s"', e, t);
    }
    registerUploadEvent(e, t) {
      const s = (this.uploadEvents.get(e) || []).concat(t);
      this.uploadEvents.set(e, s),
        this.logger.info('registered upload event "%s"', e, t);
    }
    async respondWith(e) {
      if (((this[U] = !0), this[fe])) {
        const s = await je(this[fe]);
        this.trigger('loadstart', this.request.upload, { loaded: 0, total: s }),
          this.trigger('progress', this.request.upload, {
            loaded: s,
            total: s,
          }),
          this.trigger('load', this.request.upload, { loaded: s, total: s }),
          this.trigger('loadend', this.request.upload, { loaded: s, total: s });
      }
      this.logger.info(
        'responding with a mocked response: %d %s',
        e.status,
        e.statusText
      ),
        I(this.request, 'status', e.status),
        I(this.request, 'statusText', e.statusText),
        I(this.request, 'responseURL', this.url.href),
        (this.request.getResponseHeader = new Proxy(
          this.request.getResponseHeader,
          {
            apply: (s, o, n) => {
              if (
                (this.logger.info('getResponseHeader', n[0]),
                this.request.readyState < this.request.HEADERS_RECEIVED)
              )
                return (
                  this.logger.info('headers not received yet, returning null'),
                  null
                );
              const i = e.headers.get(n[0]);
              return (
                this.logger.info('resolved response header "%s" to', n[0], i), i
              );
            },
          }
        )),
        (this.request.getAllResponseHeaders = new Proxy(
          this.request.getAllResponseHeaders,
          {
            apply: () => {
              if (
                (this.logger.info('getAllResponseHeaders'),
                this.request.readyState < this.request.HEADERS_RECEIVED)
              )
                return (
                  this.logger.info(
                    'headers not received yet, returning empty string'
                  ),
                  ''
                );
              const o = Array.from(e.headers.entries()).map(
                ([n, i]) => `${n}: ${i}`
              ).join(`\r
`);
              return this.logger.info('resolved all response headers to', o), o;
            },
          }
        )),
        Object.defineProperties(this.request, {
          response: {
            enumerable: !0,
            configurable: !1,
            get: () => this.response,
          },
          responseText: {
            enumerable: !0,
            configurable: !1,
            get: () => this.responseText,
          },
          responseXML: {
            enumerable: !0,
            configurable: !1,
            get: () => this.responseXML,
          },
        });
      const t = await je(e.clone());
      this.logger.info('calculated response body length', t),
        this.trigger('loadstart', this.request, { loaded: 0, total: t }),
        this.setReadyState(this.request.HEADERS_RECEIVED),
        this.setReadyState(this.request.LOADING);
      const r = () => {
        this.logger.info('finalizing the mocked response...'),
          this.setReadyState(this.request.DONE),
          this.trigger('load', this.request, {
            loaded: this.responseBuffer.byteLength,
            total: t,
          }),
          this.trigger('loadend', this.request, {
            loaded: this.responseBuffer.byteLength,
            total: t,
          });
      };
      if (e.body) {
        this.logger.info('mocked response has body, streaming...');
        const s = e.body.getReader(),
          o = async () => {
            const { value: n, done: i } = await s.read();
            if (i) {
              this.logger.info('response body stream done!'), r();
              return;
            }
            n &&
              (this.logger.info('read response body chunk:', n),
              (this.responseBuffer = Ur(this.responseBuffer, n)),
              this.trigger('progress', this.request, {
                loaded: this.responseBuffer.byteLength,
                total: t,
              })),
              o();
          };
        o();
      } else r();
    }
    responseBufferToText() {
      return lr(this.responseBuffer);
    }
    get response() {
      if (
        (this.logger.info(
          'getResponse (responseType: %s)',
          this.request.responseType
        ),
        this.request.readyState !== this.request.DONE)
      )
        return null;
      switch (this.request.responseType) {
        case 'json': {
          const e = Vr(this.responseBufferToText());
          return this.logger.info('resolved response JSON', e), e;
        }
        case 'arraybuffer': {
          const e = cr(this.responseBuffer);
          return this.logger.info('resolved response ArrayBuffer', e), e;
        }
        case 'blob': {
          const e =
              this.request.getResponseHeader('Content-Type') || 'text/plain',
            t = new Blob([this.responseBufferToText()], { type: e });
          return (
            this.logger.info('resolved response Blob (mime type: %s)', t, e), t
          );
        }
        default: {
          const e = this.responseBufferToText();
          return (
            this.logger.info(
              'resolving "%s" response type as text',
              this.request.responseType,
              e
            ),
            e
          );
        }
      }
    }
    get responseText() {
      if (
        (C(
          this.request.responseType === '' ||
            this.request.responseType === 'text',
          'InvalidStateError: The object is in invalid state.'
        ),
        this.request.readyState !== this.request.LOADING &&
          this.request.readyState !== this.request.DONE)
      )
        return '';
      const e = this.responseBufferToText();
      return this.logger.info('getResponseText: "%s"', e), e;
    }
    get responseXML() {
      if (
        (C(
          this.request.responseType === '' ||
            this.request.responseType === 'document',
          'InvalidStateError: The object is in invalid state.'
        ),
        this.request.readyState !== this.request.DONE)
      )
        return null;
      const e = this.request.getResponseHeader('Content-Type') || '';
      return typeof DOMParser == 'undefined'
        ? (console.warn(
            'Cannot retrieve XMLHttpRequest response body as XML: DOMParser is not defined. You are likely using an environment that is not browser or does not polyfill browser globals correctly.'
          ),
          null)
        : zr(e)
        ? new DOMParser().parseFromString(this.responseBufferToText(), e)
        : null;
    }
    errorWith(e) {
      (this[U] = !0),
        this.logger.info('responding with an error'),
        this.setReadyState(this.request.DONE),
        this.trigger('error', this.request),
        this.trigger('loadend', this.request);
    }
    setReadyState(e) {
      if (
        (this.logger.info(
          'setReadyState: %d -> %d',
          this.request.readyState,
          e
        ),
        this.request.readyState === e)
      ) {
        this.logger.info('ready state identical, skipping transition...');
        return;
      }
      I(this.request, 'readyState', e),
        this.logger.info('set readyState to: %d', e),
        e !== this.request.UNSENT &&
          (this.logger.info('triggerring "readystatechange" event...'),
          this.trigger('readystatechange', this.request));
    }
    trigger(e, t, r) {
      const s = t[`on${e}`],
        o = Fr(t, e, r);
      this.logger.info('trigger "%s"', e, r || ''),
        typeof s == 'function' &&
          (this.logger.info('found a direct "%s" callback, calling...', e),
          s.call(t, o));
      const n =
        t instanceof XMLHttpRequestUpload ? this.uploadEvents : this.events;
      for (const [i, a] of n)
        i === e &&
          (this.logger.info(
            'found %d listener(s) for "%s" event, calling...',
            a.length,
            e
          ),
          a.forEach((l) => l.call(t, o)));
    }
    toFetchApiRequest(e) {
      this.logger.info('converting request to a Fetch API Request...');
      const t = e instanceof Document ? e.documentElement.innerText : e,
        r = new Request(this.url.href, {
          method: this.method,
          headers: this.requestHeaders,
          credentials: this.request.withCredentials ? 'include' : 'same-origin',
          body: ['GET', 'HEAD'].includes(this.method.toUpperCase()) ? null : t,
        }),
        s = de(r.headers, {
          methodCall: ([o, n], i) => {
            switch (o) {
              case 'append':
              case 'set': {
                const [a, l] = n;
                this.request.setRequestHeader(a, l);
                break;
              }
              case 'delete': {
                const [a] = n;
                console.warn(
                  `XMLHttpRequest: Cannot remove a "${a}" header from the Fetch API representation of the "${r.method} ${r.url}" request. XMLHttpRequest headers cannot be removed.`
                );
                break;
              }
            }
            return i();
          },
        });
      return (
        I(r, 'headers', s),
        Ye(r, this.request),
        this.logger.info('converted request to a Fetch API Request!', r),
        r
      );
    }
  };
function He(e) {
  return typeof location == 'undefined'
    ? new URL(e)
    : new URL(e.toString(), location.href);
}
function I(e, t, r) {
  Reflect.defineProperty(e, t, { writable: !0, enumerable: !0, value: r });
}
function Zr({ emitter: e, logger: t }) {
  return new Proxy(globalThis.XMLHttpRequest, {
    construct(s, o, n) {
      t.info('constructed new XMLHttpRequest');
      const i = Reflect.construct(s, o, n),
        a = Object.getOwnPropertyDescriptors(s.prototype);
      for (const u in a) Reflect.defineProperty(i, u, a[u]);
      const l = new Qr(i, t);
      return (
        (l.onRequest = async function ({ request: u, requestId: c }) {
          const d = new Re(u);
          this.logger.info('awaiting mocked response...'),
            this.logger.info(
              'emitting the "request" event for %s listener(s)...',
              e.listenerCount('request')
            ),
            (await nt({
              request: u,
              requestId: c,
              controller: d,
              emitter: e,
              onResponse: async (m) => {
                await this.respondWith(m);
              },
              onRequestError: () => {
                this.errorWith(new TypeError('Network error'));
              },
              onError: (m) => {
                this.logger.info('request errored!', { error: m }),
                  m instanceof Error && this.errorWith(m);
              },
            })) ||
              this.logger.info(
                'no mocked response received, performing request as-is...'
              );
        }),
        (l.onResponse = async function ({
          response: u,
          isMockedResponse: c,
          request: d,
          requestId: p,
        }) {
          this.logger.info(
            'emitting the "response" event for %s listener(s)...',
            e.listenerCount('response')
          ),
            e.emit('response', {
              response: u,
              isMockedResponse: c,
              request: d,
              requestId: p,
            });
        }),
        l.request
      );
    },
  });
}
var ut = class extends Se {
    constructor() {
      super(ut.interceptorSymbol);
    }
    checkEnvironment() {
      return ot('XMLHttpRequest');
    }
    setup() {
      const e = this.logger.extend('setup');
      e.info('patching "XMLHttpRequest" module...');
      const t = globalThis.XMLHttpRequest;
      C(!t[W], 'Failed to patch the "XMLHttpRequest" module: already patched.'),
        (globalThis.XMLHttpRequest = Zr({
          emitter: this.emitter,
          logger: this.logger,
        })),
        e.info(
          'native "XMLHttpRequest" module patched!',
          globalThis.XMLHttpRequest.name
        ),
        Object.defineProperty(globalThis.XMLHttpRequest, W, {
          enumerable: !0,
          configurable: !0,
          value: !0,
        }),
        this.subscriptions.push(() => {
          Object.defineProperty(globalThis.XMLHttpRequest, W, {
            value: void 0,
          }),
            (globalThis.XMLHttpRequest = t),
            e.info(
              'native "XMLHttpRequest" module restored!',
              globalThis.XMLHttpRequest.name
            );
        });
    }
  },
  ht = ut;
ht.interceptorSymbol = Symbol('xhr');
function es(e, t) {
  const r = new ve({ name: 'fallback', interceptors: [new at(), new ht()] });
  return (
    r.on('request', async ({ request: s, requestId: o, controller: n }) => {
      const i = s.clone(),
        a = await Ne(
          s,
          o,
          e.getRequestHandlers().filter(we('RequestHandler')),
          t,
          e.emitter,
          {
            onMockedResponse(l, { handler: u, parsedResult: c }) {
              t.quiet ||
                e.emitter.once('response:mocked', ({ response: d }) => {
                  u.log({ request: i, response: d, parsedResult: c });
                });
            },
          }
        );
      a && n.respondWith(a);
    }),
    r.on(
      'response',
      ({ response: s, isMockedResponse: o, request: n, requestId: i }) => {
        e.emitter.emit(o ? 'response:mocked' : 'response:bypass', {
          response: s,
          request: n,
          requestId: i,
        });
      }
    ),
    r.apply(),
    r
  );
}
function ts(e) {
  return async function (r) {
    (e.fallbackInterceptor = es(e, r)),
      Ve({ message: 'Mocking enabled (fallback mode).', quiet: r.quiet });
  };
}
function rs(e) {
  return function () {
    var r, s;
    (r = e.fallbackInterceptor) == null || r.dispose(),
      rt({ quiet: (s = e.startOptions) == null ? void 0 : s.quiet });
  };
}
function ss() {
  try {
    const e = new ReadableStream({ start: (r) => r.close() });
    return new MessageChannel().port1.postMessage(e, [e]), !0;
  } catch {
    return !1;
  }
}
var ns = class extends kt {
  constructor(...t) {
    super(...t);
    f(this, 'context');
    f(this, 'startHandler', null);
    f(this, 'stopHandler', null);
    f(this, 'listeners');
    C(
      !ke(),
      h.formatMessage(
        'Failed to execute `setupWorker` in a non-browser environment. Consider using `setupServer` for Node.js environment instead.'
      )
    ),
      (this.listeners = []),
      (this.context = this.createWorkerContext());
  }
  createWorkerContext() {
    const t = {
      isMockingEnabled: !1,
      startOptions: null,
      worker: null,
      getRequestHandlers: () => this.handlersController.currentHandlers(),
      registration: null,
      emitter: this.emitter,
      workerChannel: {
        on: (r, s) => {
          this.context.events.addListener(
            navigator.serviceWorker,
            'message',
            (o) => {
              if (o.source !== this.context.worker) return;
              const n = o.data;
              !n || (n.type === r && s(o, n));
            }
          );
        },
        send: (r) => {
          var s;
          (s = this.context.worker) == null || s.postMessage(r);
        },
      },
      events: {
        addListener: (r, s, o) => (
          r.addEventListener(s, o),
          this.listeners.push({ eventType: s, target: r, callback: o }),
          () => {
            r.removeEventListener(s, o);
          }
        ),
        removeAllListeners: () => {
          for (const { target: r, eventType: s, callback: o } of this.listeners)
            r.removeEventListener(s, o);
          this.listeners = [];
        },
        once: (r) => {
          const s = [];
          return new Promise((o, n) => {
            const i = (a) => {
              try {
                const l = a.data;
                l.type === r && o(l);
              } catch (l) {
                n(l);
              }
            };
            s.push(
              this.context.events.addListener(
                navigator.serviceWorker,
                'message',
                i
              ),
              this.context.events.addListener(
                navigator.serviceWorker,
                'messageerror',
                n
              )
            );
          }).finally(() => {
            s.forEach((o) => o());
          });
        },
      },
      supports: {
        serviceWorkerApi:
          !('serviceWorker' in navigator) || location.protocol === 'file:',
        readableStreamTransfer: ss(),
      },
    };
    return (
      (this.startHandler = t.supports.serviceWorkerApi ? ts(t) : Tr(t)),
      (this.stopHandler = t.supports.serviceWorkerApi ? rs(t) : Pr(t)),
      t
    );
  }
  async start(t = {}) {
    return (
      t.waitUntilReady === !0 &&
        h.warn(
          'The "waitUntilReady" option has been deprecated. Please remove it from this "worker.start()" call. Follow the recommended Browser integration (https://mswjs.io/docs/integrations/browser) to eliminate any race conditions between the Service Worker registration and any requests made by your application on initial render.'
        ),
      (this.context.startOptions = Ge(qr, t)),
      Ht({
        getUnhandledRequestStrategy: () =>
          this.context.startOptions.onUnhandledRequest,
        getHandlers: () => this.handlersController.currentHandlers(),
        onMockedConnection: (r) => {
          this.context.startOptions.quiet || Bt(r);
        },
        onPassthroughConnection() {},
      }),
      ge.apply(),
      this.subscriptions.push(() => {
        ge.dispose();
      }),
      await this.startHandler(this.context.startOptions, t)
    );
  }
  stop() {
    super.dispose(),
      this.context.events.removeAllListeners(),
      this.context.emitter.removeAllListeners(),
      this.stopHandler();
  }
};
function as(...e) {
  return new ns(...e);
}
export { ns as SetupWorkerApi, as as setupWorker };
