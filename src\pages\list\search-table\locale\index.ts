const i18n = {
  'en-US': {
    'menu.list': 'List',
    'menu.list.searchTable': 'Search Table',
    'searchTable.form.search': 'Search',
    'searchTable.form.reset': 'Reset',
    'searchTable.columns.id': 'Collection ID',
    'searchTable.columns.name': 'Collection Name',
    'searchTable.columns.contentType': 'Content genre',
    'searchTable.columns.filterType': 'Filter method',
    'searchTable.columns.createdTime': 'Creation time',
    'searchTable.columns.status': 'Status',
    'searchTable.columns.contentNum': 'Content quantity',
    'searchTable.columns.operations': 'Operation',
    'searchTable.columns.operations.view': 'View',
    'searchTable.columns.operations.update': 'Edit',
    'searchTable.columns.operations.offline': 'Offline',
    'searchTable.columns.operations.online': 'Online',
    'searchTable.operations.add': 'New',
    'searchTable.operations.upload': 'Bulk upload',
    'searchTable.operation.download': 'Download',
    'searchForm.id.placeholder': 'Please enter the collection ID',
    'searchForm.name.placeholder': 'Please enter the collection name',
    'searchForm.all.placeholder': 'all',
  },
  'zh-CN': {
    'menu.list': '列表页',
    'menu.list.searchTable': '查询表格',
    'searchTable.form.search': '查询',
    'searchTable.form.reset': '重置',
    'searchTable.columns.id': '集合编号',
    'searchTable.columns.name': '集合名称',
    'searchTable.columns.contentType': '内容体裁',
    'searchTable.columns.filterType': '筛选方式',
    'searchTable.columns.createdTime': '创建时间',
    'searchTable.columns.status': '状态',
    'searchTable.columns.contentNum': '内容量',
    'searchTable.columns.operations': '操作',
    'searchTable.columns.operations.view': '查看',
    'searchTable.columns.operations.update': '修改',
    'searchTable.columns.operations.online': '上线',
    'searchTable.columns.operations.offline': '下线',
    'searchTable.operations.add': '新建',
    'searchTable.operations.upload': '批量导入',
    'searchTable.operation.download': '下载',
    'searchForm.id.placeholder': '请输入集合编号',
    'searchForm.name.placeholder': '请输入集合名称',
    'searchForm.all.placeholder': '全部',
  },
};

export default i18n;
