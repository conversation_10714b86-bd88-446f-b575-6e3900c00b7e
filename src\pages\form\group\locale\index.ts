const i18n = {
  'en-US': {
    'menu.form': 'Form',
    'menu.form.group': 'Group Form',
    'groupForm.title.video': 'Video Parameters',
    'groupForm.title.audio': 'Audio Parameters',
    'groupForm.title.explanation': 'Enter Explanation',
    'groupForm.form.label.video.mode': 'Match Mode',
    'groupForm.form.label.video.acquisition.resolution':
      'Acquisition Resolution',
    'groupForm.form.label.video.acquisition.frameRate':
      'Acquisition Frame Rate',
    'groupForm.form.label.video.encoding.resolution': 'Encoding Resolution',
    'groupForm.form.label.video.encoding.rate.min': 'Encoding Min Rate',
    'groupForm.form.label.video.encoding.rate.max': 'Encoding Max Rate',
    'groupForm.form.label.video.encoding.rate.default': 'Encoding Default Rate',
    'groupForm.form.label.video.encoding.frameRate': 'Encoding Frame Rate',
    'groupForm.form.label.video.encoding.profile': 'Encoding Profile',
    'groupForm.placeholder.video.mode': 'Please Select',
    'groupForm.placeholder.video.acquisition.resolution': 'Please Select',
    'groupForm.placeholder.video.acquisition.frameRate': 'Enter Range [1, 30]',
    'groupForm.placeholder.video.encoding.resolution': 'Please Select',
    'groupForm.placeholder.video.encoding.rate.min': 'Enter Range [150, 1800]',
    'groupForm.placeholder.video.encoding.rate.max': 'Enter Range [150, 1800]',
    'groupForm.placeholder.video.encoding.rate.default':
      'Enter Range [150, 1800]',
    'groupForm.placeholder.video.encoding.frameRate': 'Enter Range [1, 30]',
    'groupForm.placeholder.video.encoding.profile': 'Enter Range [150, 1800]',
    'groupForm.form.label.audio.mode': 'Match Mode',
    'groupForm.form.label.audio.acquisition.channels': 'Acquisition Channels',
    'groupForm.form.label.audio.encoding.rate': 'Encoding Rate',
    'groupForm.form.label.audio.encoding.profile': 'Encoding Profile',
    'groupForm.placeholder.audio.mode': 'Please Select',
    'groupForm.placeholder.audio.acquisition.channels': 'Please Select',
    'groupForm.placeholder.audio.encoding.rate': 'Enter Range [150, 1800]',
    'groupForm.placeholder.audio.encoding.profile': 'Enter Range [150, 1800]',
    'groupForm.form.label.explanation': 'Explanation',
    'groupForm.placeholder.explanation':
      'Please fill in the parameter description, no more than 200 characters',
    'groupForm.submit': 'Submit',
    'groupForm.reset': 'Reset',
    'groupForm.submitSuccess': 'Submit Success',
  },
  'zh-CN': {
    'menu.form': '表单页',
    'menu.form.group': '分组表单',
    'groupForm.title.video': '视频参数',
    'groupForm.title.audio': '音频参数',
    'groupForm.title.explanation': '填写说明',
    'groupForm.form.label.video.mode': '匹配模式',
    'groupForm.form.label.video.acquisition.resolution': '采集分辨率',
    'groupForm.form.label.video.acquisition.frameRate': '采集帧率',
    'groupForm.form.label.video.encoding.resolution': '编码分辨率',
    'groupForm.form.label.video.encoding.rate.min': '编码码率最小值',
    'groupForm.form.label.video.encoding.rate.max': '编码码率最大值',
    'groupForm.form.label.video.encoding.rate.default': '编码码率默认值',
    'groupForm.form.label.video.encoding.frameRate': '编码帧率',
    'groupForm.form.label.video.encoding.profile': '编码profile',
    'groupForm.placeholder.video.mode': '请选择',
    'groupForm.placeholder.video.acquisition.resolution': '请选择',
    'groupForm.placeholder.video.acquisition.frameRate': '输入范围[1, 30]',
    'groupForm.placeholder.video.encoding.resolution': '请选择',
    'groupForm.placeholder.video.encoding.rate.min': '输入范围[150, 1800]',
    'groupForm.placeholder.video.encoding.rate.max': '输入范围[150, 1800]',
    'groupForm.placeholder.video.encoding.rate.default': '输入范围[150, 1800]',
    'groupForm.placeholder.video.encoding.frameRate': '输入范围[1, 30]',
    'groupForm.placeholder.video.encoding.profile': '输入范围[150, 1800]',
    'groupForm.form.label.audio.mode': '配置模式',
    'groupForm.form.label.audio.acquisition.channels': '采集声道数',
    'groupForm.form.label.audio.encoding.rate': '编码码率',
    'groupForm.form.label.audio.encoding.profile': '编码profile',
    'groupForm.placeholder.audio.mode': '请选择',
    'groupForm.placeholder.audio.acquisition.channels': '请选择',
    'groupForm.placeholder.audio.encoding.rate': '输入范围[150, 1800]',
    'groupForm.placeholder.audio.encoding.profile': '输入范围[150, 1800]',
    'groupForm.form.label.explanation': '参数说明',
    'groupForm.placeholder.explanation': '请填写参数说明，最多不超多200字',
    'groupForm.submit': '提交',
    'groupForm.reset': '重置',
    'groupForm.submitSuccess': '提交成功',
  },
};

export default i18n;
