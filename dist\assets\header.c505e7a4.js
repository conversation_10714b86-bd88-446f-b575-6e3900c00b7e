import {
  r as v,
  v as Z,
  j as I,
  a as s,
  aP as ar,
  e as A,
  t as le,
  aQ as gn,
  aD as Qe,
  aR as mn,
  T as Oe,
  C as ve,
  f as ir,
  w as Nt,
  R as k,
  g as H,
  _ as B,
  aS as Te,
  F as Re,
  aT as hn,
  x as bn,
  ap as yn,
  s as or,
  G as xn,
  a6 as wn,
  aB as On,
  aA as Cn,
  o as er,
  aU as Pn,
  aV as _n,
  aW as In,
  $ as He,
  a0 as Be,
  X as jn,
  aX as Nn,
  aY as Sn,
  aZ as Ce,
  a3 as kn,
  a_ as Tn,
  au as Rn,
  K as St,
  a$ as Ln,
  b0 as Dn,
  b1 as En,
  b2 as Un,
  b3 as $n,
  b4 as Mn,
  b5 as Fn,
  b6 as qe,
  V as rr,
  B as An,
  O as kt,
  b7 as Vn,
  u as zn,
  a7 as Wn,
  d as Hr,
  L as Br,
} from './index.7dafa16d.js';
import { S as qr } from './index.43d26da3.js';
import { D as Zn } from './index.69569894.js';
import { l as Hn } from './index.69444b8c.js';
import { I as Bn } from './index.05c05262.js';
var Pe =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (Pe =
          Object.assign ||
          function (e) {
            for (var n, t = 1, r = arguments.length; t < r; t++) {
              n = arguments[t];
              for (var a in n)
                Object.prototype.hasOwnProperty.call(n, a) && (e[a] = n[a]);
            }
            return e;
          }),
        Pe.apply(this, arguments)
      );
    },
  Kr = function (e, n) {
    if (le(e)) {
      var t = Object.keys(e)
          .map(function (a) {
            return e[a] + ' ' + a;
          })
          .join(','),
        r = n ? { backgroundSize: (100 * 100) / n + '%' } : {};
      return Pe({ backgroundImage: 'linear-gradient(to right, ' + t + ')' }, r);
    }
    return { backgroundColor: e };
  },
  qn = { small: 3, default: 4, large: 8 };
function Kn(e) {
  var n,
    t,
    r = e.type,
    a = e.prefixCls,
    o = e.buffer,
    l = e.percent,
    i = e.color,
    u = e.animation,
    c = e.bufferColor,
    f = e.formatText,
    d = e.trailColor,
    g = e.showText,
    x = g === void 0 ? !0 : g,
    y = e.size,
    j = y === void 0 ? 'default' : y,
    w = e.status,
    b = w === void 0 ? 'normal' : w,
    _ = e.strokeWidth || qn[j],
    p = a + '-' + r,
    O = _,
    h = b === 'success' || b === 'error' || l >= 100,
    C = v.exports.useCallback(
      function () {
        if (Z(f)) return f(l);
        switch (b) {
          case 'error':
            return I('span', { children: [l, '% ', s(ar, {})] });
          default:
            return l + '%';
        }
      },
      [f, l, b]
    );
  return I('div', {
    className: p + '-wrapper',
    children: [
      I('div', {
        className: p + '-outer',
        role: 'progressbar',
        'aria-valuemin': 0,
        'aria-valuemax': 100,
        'aria-valuenow': l,
        style: { height: O, backgroundColor: d },
        children: [
          o &&
            !h &&
            s('div', {
              className: p + '-inner-buffer',
              style: Pe({ width: (l > 0 ? l + 10 : 0) + '%' }, Kr(c)),
            }),
          s('div', {
            className: A(
              p + '-inner',
              ((n = {}), (n[p + '-inner-animate'] = u), n)
            ),
            style: Pe({ width: l + '%' }, Kr(i, l)),
          }),
        ],
      }),
      x &&
        s('div', {
          className: A(
            p + '-text',
            ((t = {}), (t[p + '-text-with-icon'] = b), t)
          ),
          children: C(),
        }),
    ],
  });
}
var Gn = { mini: 4, small: 3, default: 4, large: 4 },
  Xn = { mini: 16, small: 48, default: 64, large: 80 },
  Yn = function (e) {
    var n = e.size,
      t = e.percent,
      r = t === void 0 ? 0 : t,
      a = e.prefixCls,
      o = e.showText,
      l = e.status,
      i = e.formatText,
      u = le(e.color),
      c = e.width || Xn[n],
      f = e.strokeWidth || (n === 'mini' ? c / 2 : Gn[n]),
      d = (c - f) / 2,
      g = Math.PI * 2 * d,
      x = c / 2,
      y = a + '-circle',
      j = y + '-svg',
      w = v.exports.useCallback(
        function (O) {
          if (Z(i)) return i(r);
          switch (O) {
            case 'success':
              return s(Qe, {});
            case 'error':
              return s(gn, {});
            default:
              return r + '%';
          }
        },
        [i, r]
      ),
      b = mn(a + '-linear-gradient-'),
      _ = u ? 'url(#' + b + ')' : e.color,
      p = I('div', {
        className: y + '-wrapper',
        role: 'progressbar',
        'aria-valuemin': 0,
        'aria-valuemax': 100,
        'aria-valuenow': r,
        style: { width: c, height: c },
        children: [
          I('svg', {
            viewBox: '0 0 ' + c + ' ' + c,
            className: '' + j,
            children: [
              u &&
                s('defs', {
                  children: s('linearGradient', {
                    id: b,
                    x1: '0',
                    y1: '1',
                    x2: '0',
                    y2: '0',
                    children: Object.keys(e.color)
                      .sort()
                      .map(function (O) {
                        return s(
                          'stop',
                          { offset: O, stopColor: e.color[O] },
                          O
                        );
                      }),
                  }),
                }),
              s('circle', {
                className: y + '-mask',
                fill: 'none',
                cx: x,
                cy: x,
                r: d,
                strokeWidth:
                  e.pathStrokeWidth || (n === 'mini' ? f : Math.max(2, f - 2)),
                style: { stroke: e.pathStrokeColor },
              }),
              s('circle', {
                className: y + '-path',
                fill: 'none',
                cx: x,
                cy: x,
                r: d,
                strokeWidth: f,
                style: {
                  stroke: _,
                  strokeDasharray: g,
                  strokeDashoffset: (r > 100 ? 100 : 1 - r / 100) * g,
                },
              }),
            ],
          }),
          o &&
            n !== 'mini' &&
            s('div', { className: y + '-text', children: w(l) }),
        ],
      });
    return (
      n === 'mini' &&
        l === 'success' &&
        e.type === 'circle' &&
        (p = s('div', {
          className: y + '-wrapper',
          style: { width: c, height: c },
          children: s(Qe, { style: { fontSize: c - 2, color: _ } }),
        })),
      n === 'mini' && o
        ? s(Oe, {
            content: s('div', {
              className: y + '-text',
              children: w('normal'),
            }),
            children: p,
          })
        : p
    );
  },
  Gr = Yn,
  Jn =
    (globalThis && globalThis.__read) ||
    function (e, n) {
      var t = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!t) return e;
      var r = t.call(e),
        a,
        o = [],
        l;
      try {
        for (; (n === void 0 || n-- > 0) && !(a = r.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (t = r.return) && t.call(r);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    },
  Qn =
    (globalThis && globalThis.__spreadArray) ||
    function (e, n, t) {
      if (t || arguments.length === 2)
        for (var r = 0, a = n.length, o; r < a; r++)
          (o || !(r in n)) &&
            (o || (o = Array.prototype.slice.call(n, 0, r)), (o[r] = n[r]));
      return e.concat(o || Array.prototype.slice.call(n));
    },
  ea = function (e) {
    var n,
      t = e.prefixCls,
      r = e.percent,
      a = e.color,
      o = e.type,
      l = e.formatText,
      i = e.trailColor,
      u = e.showText,
      c = u === void 0 ? !0 : u,
      f = e.size,
      d = f === void 0 ? 'default' : f,
      g = e.status,
      x = g === void 0 ? 'normal' : g,
      y = e.strokeWidth || (d === 'small' ? 8 : 4),
      j = t + '-' + o,
      w = y,
      b = v.exports.useCallback(
        function () {
          if (Z(l)) return l(r);
          switch (x) {
            case 'error':
              return I('span', { children: [r, '% ', s(ar, {})] });
            default:
              return r + '%';
          }
        },
        [l, r, x]
      );
    return I('div', {
      className: j + '-wrapper',
      children: [
        s('div', {
          className: j + '-outer',
          role: 'progressbar',
          'aria-valuemin': 0,
          'aria-valuemax': 100,
          'aria-valuenow': r,
          style: { height: w },
          children: Qn([], Jn(new Array(e.steps)), !1).map(function (_, p) {
            var O,
              h = r > (100 / e.steps) * p;
            return s(
              'div',
              {
                className: A(
                  j + '-item',
                  ((O = {}), (O[j + '-item-active'] = h), O)
                ),
                style: { backgroundColor: h ? a : i || '' },
              },
              p
            );
          }),
        }),
        c &&
          s('div', {
            className: A(
              j + '-text',
              ((n = {}), (n[j + '-text-with-icon'] = x), n)
            ),
            children: b(),
          }),
      ],
    });
  },
  ra = ea,
  oe =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (oe =
          Object.assign ||
          function (e) {
            for (var n, t = 1, r = arguments.length; t < r; t++) {
              n = arguments[t];
              for (var a in n)
                Object.prototype.hasOwnProperty.call(n, a) && (e[a] = n[a]);
            }
            return e;
          }),
        oe.apply(this, arguments)
      );
    },
  ta =
    (globalThis && globalThis.__rest) ||
    function (e, n) {
      var t = {};
      for (var r in e)
        Object.prototype.hasOwnProperty.call(e, r) &&
          n.indexOf(r) < 0 &&
          (t[r] = e[r]);
      if (e != null && typeof Object.getOwnPropertySymbols == 'function')
        for (var a = 0, r = Object.getOwnPropertySymbols(e); a < r.length; a++)
          n.indexOf(r[a]) < 0 &&
            Object.prototype.propertyIsEnumerable.call(e, r[a]) &&
            (t[r[a]] = e[r[a]]);
      return t;
    },
  na = { type: 'line', showText: !0, percent: 0, size: 'default' };
function aa(e, n) {
  var t,
    r = v.exports.useContext(ve),
    a = r.getPrefixCls,
    o = r.componentConfig,
    l = r.rtl,
    i = ir(e, na, o == null ? void 0 : o.Progress),
    u = i.className,
    c = i.style,
    f = i.size,
    d = i.width,
    g = i.strokeWidth,
    x = i.steps,
    y = i.percent,
    j = ta(i, [
      'className',
      'style',
      'size',
      'width',
      'strokeWidth',
      'steps',
      'percent',
    ]),
    w = x && i.type !== 'circle' ? 'steps' : i.type,
    b = a('progress'),
    _ = 'status' in i ? i.status : y >= 100 ? 'success' : 'normal',
    p = { width: d };
  return (
    f === 'mini' && w === 'line' && ((p.width = d || 16), (p.height = d || 16)),
    I('div', {
      ...oe(
        {
          ref: n,
          className: A(
            b,
            b + '-' + w,
            b + '-' + f,
            ((t = {}),
            (t[b + '-is-' + _] = _ !== 'normal'),
            (t[b + '-rtl'] = l),
            t),
            u
          ),
          style: oe(oe({}, p), c),
        },
        Nt(j, [
          'type',
          'animation',
          'status',
          'color',
          'trailColor',
          'showText',
          'formatText',
          'buffer',
          'bufferColor',
        ])
      ),
      children: [
        w === 'steps' &&
          s(ra, { ...oe({}, i, { type: w, status: _, prefixCls: b }) }),
        w === 'circle' &&
          s(Gr, {
            ...oe({ width: i.width }, i, {
              pathStrokeColor: i.trailColor,
              status: _,
              prefixCls: b,
            }),
          }),
        w === 'line' &&
          (f === 'mini'
            ? s(Gr, {
                ...oe({ pathStrokeColor: i.trailColor }, i, {
                  pathStrokeWidth: g || 4,
                  width: d || 16,
                  strokeWidth: g || 4,
                  prefixCls: b,
                  status: _,
                }),
              })
            : s(Kn, { ...oe({}, i, { status: _, prefixCls: b }) })),
      ],
    })
  );
}
var Tt = v.exports.forwardRef(aa);
Tt.displayName = 'Progress';
var ia = Tt;
function Xr(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function Yr(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? Xr(Object(t), !0).forEach(function (r) {
          B(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : Xr(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function oa(e, n) {
  var t = v.exports.useContext(H),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    o = e.spin,
    l = e.className,
    i = Yr(
      Yr({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-delete'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    s('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: s('path', {
        d: 'M5 11h5.5m0 0v29a1 1 0 0 0 1 1h25a1 1 0 0 0 1-1V11m-27 0H16m21.5 0H43m-5.5 0H32m-16 0V7h16v4m-16 0h16M20 18v15m8-15v15',
      }),
    })
  );
}
var lr = k.forwardRef(oa);
lr.defaultProps = { isIcon: !0 };
lr.displayName = 'IconDelete';
var Rt = lr,
  R = { init: 'init', uploading: 'uploading', success: 'done', fail: 'error' };
function Jr(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function Qr(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? Jr(Object(t), !0).forEach(function (r) {
          B(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : Jr(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function la(e, n) {
  var t = v.exports.useContext(H),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    o = e.spin,
    l = e.className,
    i = Qr(
      Qr({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-upload'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    s('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: s('path', {
        d: 'M14.93 17.071 24.001 8l9.071 9.071m-9.07 16.071v-25M40 35v6H8v-6',
      }),
    })
  );
}
var sr = k.forwardRef(la);
sr.defaultProps = { isIcon: !0 };
sr.displayName = 'IconUpload';
var cr = sr;
function et(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function rt(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? et(Object(t), !0).forEach(function (r) {
          B(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : et(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function sa(e, n) {
  var t = v.exports.useContext(H),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    o = e.spin,
    l = e.className,
    i = rt(
      rt({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-play-arrow-fill'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    s('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: s('path', {
        fill: 'currentColor',
        stroke: 'none',
        d: 'M17.533 10.974a1 1 0 0 0-1.537.844v24.356a1 1 0 0 0 1.537.844L36.67 24.84a1 1 0 0 0 0-1.688L17.533 10.974Z',
      }),
    })
  );
}
var ur = k.forwardRef(sa);
ur.defaultProps = { isIcon: !0 };
ur.displayName = 'IconPlayArrowFill';
var ca = ur;
function tt(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function nt(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? tt(Object(t), !0).forEach(function (r) {
          B(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : tt(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function ua(e, n) {
  var t = v.exports.useContext(H),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    o = e.spin,
    l = e.className,
    i = nt(
      nt({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-pause'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    I('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: [
        s('path', { d: 'M14 12H18V36H14z' }),
        s('path', { d: 'M30 12H34V36H30z' }),
        s('path', {
          fill: 'currentColor',
          stroke: 'none',
          d: 'M14 12H18V36H14z',
        }),
        s('path', {
          fill: 'currentColor',
          stroke: 'none',
          d: 'M30 12H34V36H30z',
        }),
      ],
    })
  );
}
var fr = k.forwardRef(ua);
fr.defaultProps = { isIcon: !0 };
fr.displayName = 'IconPause';
var fa = fr,
  he =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (he =
          Object.assign ||
          function (e) {
            for (var n, t = 1, r = arguments.length; t < r; t++) {
              n = arguments[t];
              for (var a in n)
                Object.prototype.hasOwnProperty.call(n, a) && (e[a] = n[a]);
            }
            return e;
          }),
        he.apply(this, arguments)
      );
    },
  da = function (e) {
    var n = Te(),
      t = e.file,
      r = e.prefixCls,
      a = e.progressProps,
      o = e.progressRender,
      l = v.exports.useContext(ve).locale,
      i = t.status,
      u = t.percent,
      c = u === void 0 ? 0 : u,
      f = r + '-list',
      d = a && a.width ? { width: a.width } : {},
      g = I(Re, {
        children: [
          i === R.fail &&
            e.reuploadIcon !== null &&
            s('span', {
              ...he(
                {
                  className: r + '-list-reupload-icon',
                  onClick: function () {
                    e.onReupload && e.onReupload(t);
                  },
                  tabIndex: 0,
                  role: 'button',
                  'aria-label': l.Upload.reupload,
                },
                n({
                  onPressEnter: function () {
                    e.onReupload && e.onReupload(t);
                  },
                })
              ),
              children:
                e.reuploadIcon ||
                (e.listType === 'picture-card' ? s(cr, {}) : l.Upload.reupload),
            }),
          i === R.success &&
            e.successIcon !== null &&
            s('span', {
              className: r + '-list-success-icon',
              children: e.successIcon || s(Qe, {}),
            }),
          i !== R.success &&
            I('div', {
              className: f + '-status',
              style: d,
              children: [
                s(ia, {
                  ...he(
                    {
                      showText: !1,
                      className: f + '-progress',
                      type: 'circle',
                      status:
                        i === R.fail
                          ? 'error'
                          : i === R.success
                          ? 'success'
                          : 'normal',
                      percent: c,
                      size: 'mini',
                    },
                    a
                  ),
                }),
                i === R.init &&
                  e.startIcon !== null &&
                  s('span', {
                    ...he(
                      {
                        tabIndex: 0,
                        role: 'button',
                        'aria-label': l.Upload.start,
                        className: r + '-list-start-icon',
                        onClick: function () {
                          e.onUpload && e.onUpload(t);
                        },
                      },
                      n({
                        onPressEnter: function () {
                          e.onUpload && e.onUpload(t);
                        },
                      })
                    ),
                    children:
                      e.startIcon ||
                      s(Oe, { content: l.Upload.start, children: s(ca, {}) }),
                  }),
                i === R.uploading &&
                  e.cancelIcon !== null &&
                  s('span', {
                    ...he(
                      {
                        className: e.prefixCls + '-list-cancel-icon',
                        onClick: function () {
                          e.onAbort && e.onAbort(t);
                        },
                        tabIndex: 0,
                        'aria-label': l.Upload.cancel,
                      },
                      n({
                        onPressEnter: function () {
                          e.onAbort && e.onAbort(t);
                        },
                      })
                    ),
                    children:
                      e.cancelIcon ||
                      s(Oe, { content: l.Upload.cancel, children: s(fa, {}) }),
                  }),
              ],
            }),
        ],
      });
    return Z(o) ? o(t, g) : g;
  },
  Lt = da;
function at(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function it(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? at(Object(t), !0).forEach(function (r) {
          B(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : at(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function va(e, n) {
  var t = v.exports.useContext(H),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    o = e.spin,
    l = e.className,
    i = it(
      it({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-image-close'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    I('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: [
        s('path', {
          d: 'M41 26V9a2 2 0 0 0-2-2H9a2 2 0 0 0-2 2v30a2 2 0 0 0 2 2h17',
        }),
        s('path', {
          d: 'm24 33 9-8.5V27s-2 1-3.5 2.5C27.841 31.159 27 33 27 33h-3Zm0 0-3.5-4.5L17 33h7Z',
        }),
        s('path', {
          fill: 'currentColor',
          stroke: 'none',
          d: 'M20.5 28.5 17 33h7l-3.5-4.5ZM33 24.5 24 33h3s.841-1.841 2.5-3.5C31 28 33 27 33 27v-2.5Z',
        }),
        s('path', {
          fill: 'currentColor',
          fillRule: 'evenodd',
          stroke: 'none',
          d: 'M46 38a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-4.95-4.782 1.74 1.74-3.045 3.046 3.046 3.046-1.74 1.74-3.047-3.045-3.046 3.046-1.74-1.74 3.046-3.047-3.046-3.046 1.74-1.74 3.046 3.046 3.046-3.046Z',
          clipRule: 'evenodd',
        }),
        s('path', { d: 'M17 15h-2v2h2v-2Z' }),
      ],
    })
  );
}
var dr = k.forwardRef(va);
dr.defaultProps = { isIcon: !0 };
dr.displayName = 'IconImageClose';
var pa = dr,
  be =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (be =
          Object.assign ||
          function (e) {
            for (var n, t = 1, r = arguments.length; t < r; t++) {
              n = arguments[t];
              for (var a in n)
                Object.prototype.hasOwnProperty.call(n, a) && (e[a] = n[a]);
            }
            return e;
          }),
        be.apply(this, arguments)
      );
    },
  ga = function (e, n) {
    var t = e.disabled,
      r = e.prefixCls,
      a = e.file,
      o = e.showUploadList,
      l = e.locale,
      i = Te(),
      u = r + '-list-item-picture',
      c = a.status,
      f = a.originFile,
      d =
        a.url !== void 0
          ? a.url
          : f && Z(URL.createObjectURL) && URL.createObjectURL(f),
      g = le(o) ? o : {};
    return s('div', {
      className: u,
      ref: n,
      children:
        c === R.uploading
          ? s(Lt, {
              ...be(
                {
                  onReupload: e.onReupload,
                  onUpload: e.onUpload,
                  onAbort: e.onAbort,
                  listType: 'picture-card',
                  file: a,
                  prefixCls: r,
                  progressProps: e.progressProps,
                },
                g
              ),
            })
          : I(Re, {
              children: [
                Z(g.imageRender)
                  ? g.imageRender(a)
                  : s('img', { src: d, alt: a.name }),
                I('div', {
                  className: u + '-mask',
                  role: 'radiogroup',
                  children: [
                    a.status === R.fail &&
                      s('div', {
                        className: u + '-error-tip',
                        children:
                          g.errorIcon !== null &&
                          s('span', {
                            className: r + '-list-error-icon',
                            children: g.errorIcon || s(pa, {}),
                          }),
                      }),
                    I('div', {
                      className: u + '-operation',
                      children: [
                        a.status !== R.fail &&
                          g.previewIcon !== null &&
                          s('span', {
                            ...be(
                              {
                                className: r + '-list-preview-icon',
                                tabIndex: 0,
                                role: 'button',
                                'aria-label': l.Upload.preview,
                              },
                              i({
                                onPressEnter: function () {
                                  e.onPreview && e.onPreview(a);
                                },
                              }),
                              {
                                onClick: function () {
                                  e.onPreview && e.onPreview(a);
                                },
                              }
                            ),
                            children: g.previewIcon || s(hn, {}),
                          }),
                        a.status === R.fail &&
                          g.reuploadIcon !== null &&
                          s('span', {
                            ...be(
                              {
                                className: e.prefixCls + '-list-reupload-icon',
                                onClick: function () {
                                  e.onReupload && e.onReupload(a);
                                },
                                tabIndex: 0,
                                role: 'button',
                                'aria-label': l.Upload.reupload,
                              },
                              i({
                                onPressEnter: function () {
                                  e.onReupload && e.onReupload(a);
                                },
                              })
                            ),
                            children: g.reuploadIcon || s(cr, {}),
                          }),
                        !t &&
                          g.removeIcon !== null &&
                          s('span', {
                            ...be(
                              {
                                className: r + '-list-remove-icon',
                                onClick: function () {
                                  e.onRemove && e.onRemove(a);
                                },
                                role: 'button',
                                'aria-label': l.Upload.delete,
                                tabIndex: 0,
                              },
                              i({
                                onPressEnter: function () {
                                  e.onRemove && e.onRemove(a);
                                },
                              })
                            ),
                            children: g.removeIcon || s(Rt, {}),
                          }),
                      ],
                    }),
                  ],
                }),
              ],
            }),
    });
  },
  ma = v.exports.forwardRef(ga);
function ot(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function lt(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? ot(Object(t), !0).forEach(function (r) {
          B(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : ot(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function ha(e, n) {
  var t = v.exports.useContext(H),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    o = e.spin,
    l = e.className,
    i = lt(
      lt({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-file-pdf'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    I('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: [
        s('path', {
          d: 'M11 42h26a2 2 0 0 0 2-2V13.828a2 2 0 0 0-.586-1.414l-5.828-5.828A2 2 0 0 0 31.172 6H11a2 2 0 0 0-2 2v32a2 2 0 0 0 2 2Z',
        }),
        s('path', {
          d: 'M22.305 21.028c.874 1.939 3.506 6.265 4.903 8.055 1.747 2.237 3.494 2.685 4.368 2.237.873-.447 1.21-4.548-7.425-2.685-7.523 1.623-7.424 3.58-6.988 4.476.728 1.193 2.522 2.627 5.678-6.266C25.699 18.79 24.489 17 23.277 17c-1.409 0-2.538.805-.972 4.028Z',
        }),
      ],
    })
  );
}
var vr = k.forwardRef(ha);
vr.defaultProps = { isIcon: !0 };
vr.displayName = 'IconFilePdf';
var ba = vr;
function st(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function ct(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? st(Object(t), !0).forEach(function (r) {
          B(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : st(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function ya(e, n) {
  var t = v.exports.useContext(H),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    o = e.spin,
    l = e.className,
    i = ct(
      ct({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-file-image'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    s('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: s('path', {
        d: 'm26 33 5-6v6h-5Zm0 0-3-4-4 4h7Zm11 9H11a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h21l7 7v27a2 2 0 0 1-2 2ZM17 19h1v1h-1v-1Z',
      }),
    })
  );
}
var pr = k.forwardRef(ya);
pr.defaultProps = { isIcon: !0 };
pr.displayName = 'IconFileImage';
var Dt = pr;
function ut(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function ft(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? ut(Object(t), !0).forEach(function (r) {
          B(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : ut(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function xa(e, n) {
  var t = v.exports.useContext(H),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    o = e.spin,
    l = e.className,
    i = ft(
      ft({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-file-video'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    I('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: [
        s('path', {
          d: 'M37 42H11a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h21l7 7v27a2 2 0 0 1-2 2Z',
        }),
        s('path', { d: 'M22 27.796v-6l5 3-5 3Z' }),
      ],
    })
  );
}
var gr = k.forwardRef(xa);
gr.defaultProps = { isIcon: !0 };
gr.displayName = 'IconFileVideo';
var wa = gr;
function dt(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function vt(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? dt(Object(t), !0).forEach(function (r) {
          B(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : dt(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function Oa(e, n) {
  var t = v.exports.useContext(H),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    o = e.spin,
    l = e.className,
    i = vt(
      vt({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-file-audio'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    I('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: [
        s('path', {
          d: 'M37 42H11a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h21l7 7v27a2 2 0 0 1-2 2Z',
        }),
        s('path', {
          fill: 'currentColor',
          stroke: 'none',
          d: 'M25 30a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z',
        }),
        s('path', {
          d: 'M25 30a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm0 0-.951-12.363a.5.5 0 0 1 .58-.532L30 18',
        }),
      ],
    })
  );
}
var mr = k.forwardRef(Oa);
mr.defaultProps = { isIcon: !0 };
mr.displayName = 'IconFileAudio';
var Ca = mr,
  we =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (we =
          Object.assign ||
          function (e) {
            for (var n, t = 1, r = arguments.length; t < r; t++) {
              n = arguments[t];
              for (var a in n)
                Object.prototype.hasOwnProperty.call(n, a) && (e[a] = n[a]);
            }
            return e;
          }),
        we.apply(this, arguments)
      );
    },
  Pa = function (e) {
    var n = '';
    if (e.originFile && e.originFile.type) n = e.originFile.type;
    else {
      var t = e.name || '',
        r = t.split('.').pop() || '';
      (n = r),
        ['png', 'jpg', 'jpeg', 'bmp', 'gif'].indexOf(r) > -1
          ? (n = 'image')
          : ['mp4', 'm2v', 'mkv'].indexOf(r) > -1
          ? (n = 'video')
          : ['mp3', 'wav', 'wmv'].indexOf(r) > -1 && (n = 'audio');
    }
    return n.indexOf('image') > -1
      ? Dt
      : n.indexOf('pdf') > -1
      ? ba
      : n.indexOf('audio') > -1
      ? Ca
      : n.indexOf('video') > -1
      ? wa
      : yn;
  },
  _a = function (e, n) {
    var t = e.prefixCls,
      r = e.disabled,
      a = e.file,
      o = e.locale,
      l = t + '-list-item-text',
      i = Te(),
      u = Pa(a),
      c = le(e.showUploadList) ? e.showUploadList : {},
      f = le(c) ? c : {},
      d = a.name || (a.originFile && a.originFile.name),
      g =
        a.url !== void 0
          ? a.url
          : a.originFile &&
            Z(URL.createObjectURL) &&
            URL.createObjectURL(a.originFile),
      x = {};
    return (
      a.status !== R.fail && (x = { popupVisible: !1 }),
      I('div', {
        className: t + '-list-item ' + t + '-list-item-' + a.status,
        ref: n,
        children: [
          I('div', {
            className: l,
            children: [
              e.listType === 'picture-list' &&
                s('div', {
                  className: l + '-thumbnail',
                  children: Z(c.imageRender)
                    ? c.imageRender(a)
                    : s('img', { src: g }),
                }),
              I('div', {
                className: l + '-content',
                children: [
                  I('div', {
                    className: l + '-name',
                    children: [
                      e.listType === 'text' &&
                        f.fileIcon !== null &&
                        s('span', {
                          className: t + '-list-file-icon',
                          children: f.fileIcon || s(u, {}),
                        }),
                      Z(c.fileName)
                        ? s('span', {
                            className: l + '-name-text',
                            children: c.fileName(a),
                          })
                        : a.url
                        ? s('a', {
                            href: a.url,
                            target: '_blank',
                            rel: 'noreferrer',
                            className: l + '-name-link',
                            children: d,
                          })
                        : s('span', {
                            className: l + '-name-text',
                            children: d,
                          }),
                      a.status === R.fail &&
                        f.errorIcon !== null &&
                        s(Oe, {
                          ...we(
                            {
                              content:
                                (typeof a.response == 'object'
                                  ? v.exports.isValidElement(a.response) &&
                                    a.response
                                  : a.response) || o.Upload.error,
                            },
                            x,
                            { disabled: a.status !== R.fail }
                          ),
                          children: s('span', {
                            className: e.prefixCls + '-list-error-icon',
                            children:
                              f.errorIcon ||
                              (e.listType === 'picture-card'
                                ? s(Dt, {})
                                : s(ar, {})),
                          }),
                        }),
                    ],
                  }),
                  s(Lt, {
                    ...we(
                      {
                        file: a,
                        prefixCls: t,
                        progressProps: e.progressProps,
                        onReupload: e.onReupload,
                        onUpload: e.onUpload,
                        onAbort: e.onAbort,
                      },
                      f
                    ),
                  }),
                ],
              }),
            ],
          }),
          s('div', {
            className: t + '-list-item-operation',
            children:
              !r &&
              f.removeIcon !== null &&
              s(bn, {
                ...we(
                  {
                    className: t + '-list-remove-icon-hover',
                    onClick: function () {
                      e.onRemove && e.onRemove(a);
                    },
                    tabIndex: 0,
                    'aria-label': o.Upload.delete,
                  },
                  i({
                    onPressEnter: function () {
                      e.onRemove && e.onRemove(a);
                    },
                  })
                ),
                children: s('span', {
                  className: t + '-list-remove-icon',
                  children: f.removeIcon || s(Rt, {}),
                }),
              }),
          }),
        ],
      })
    );
  },
  Ia = v.exports.forwardRef(_a);
function pt(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function gt(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? pt(Object(t), !0).forEach(function (r) {
          B(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : pt(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function ja(e, n) {
  var t = v.exports.useContext(H),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    o = e.spin,
    l = e.className,
    i = gt(
      gt({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-zoom-out'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    s('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: s('path', {
        d: 'M32.607 32.607A14.953 14.953 0 0 0 37 22c0-8.284-6.716-15-15-15-8.284 0-15 6.716-15 15 0 8.284 6.716 15 15 15 4.142 0 7.892-1.679 10.607-4.393Zm0 0L41.5 41.5M29 22H15',
      }),
    })
  );
}
var hr = k.forwardRef(ja);
hr.defaultProps = { isIcon: !0 };
hr.displayName = 'IconZoomOut';
var Na = hr;
function mt(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function ht(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? mt(Object(t), !0).forEach(function (r) {
          B(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : mt(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function Sa(e, n) {
  var t = v.exports.useContext(H),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    o = e.spin,
    l = e.className,
    i = ht(
      ht({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-zoom-in'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    s('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: s('path', {
        d: 'M32.607 32.607A14.953 14.953 0 0 0 37 22c0-8.284-6.716-15-15-15-8.284 0-15 6.716-15 15 0 8.284 6.716 15 15 15 4.142 0 7.892-1.679 10.607-4.393Zm0 0L41.5 41.5M29 22H15m7 7V15',
      }),
    })
  );
}
var br = k.forwardRef(Sa);
br.defaultProps = { isIcon: !0 };
br.displayName = 'IconZoomIn';
var ka = br;
function bt(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function yt(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? bt(Object(t), !0).forEach(function (r) {
          B(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : bt(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function Ta(e, n) {
  var t = v.exports.useContext(H),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    o = e.spin,
    l = e.className,
    i = yt(
      yt({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-fullscreen'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    s('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: s('path', {
        d: 'M42 17V9a1 1 0 0 0-1-1h-8M6 17V9a1 1 0 0 1 1-1h8m27 23v8a1 1 0 0 1-1 1h-8M6 31v8a1 1 0 0 0 1 1h8',
      }),
    })
  );
}
var yr = k.forwardRef(Ta);
yr.defaultProps = { isIcon: !0 };
yr.displayName = 'IconFullscreen';
var Ra = yr;
function xt(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function wt(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? xt(Object(t), !0).forEach(function (r) {
          B(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : xt(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function La(e, n) {
  var t = v.exports.useContext(H),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    o = e.spin,
    l = e.className,
    i = wt(
      wt({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-rotate-left'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    s('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: s('path', {
        d: 'M10 22a1 1 0 0 1 1-1h20a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H11a1 1 0 0 1-1-1V22ZM23 11h11a6 6 0 0 1 6 6v6M22.5 12.893 19.587 11 22.5 9.107v3.786Z',
      }),
    })
  );
}
var xr = k.forwardRef(La);
xr.defaultProps = { isIcon: !0 };
xr.displayName = 'IconRotateLeft';
var Da = xr;
function Ot(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function Ct(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? Ot(Object(t), !0).forEach(function (r) {
          B(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : Ot(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function Ea(e, n) {
  var t = v.exports.useContext(H),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    o = e.spin,
    l = e.className,
    i = Ct(
      Ct({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-rotate-right'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    s('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: s('path', {
        d: 'M38 22a1 1 0 0 0-1-1H17a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h20a1 1 0 0 0 1-1V22ZM25 11H14a6 6 0 0 0-6 6v6M25.5 12.893 28.413 11 25.5 9.107v3.786Z',
      }),
    })
  );
}
var wr = k.forwardRef(Ea);
wr.defaultProps = { isIcon: !0 };
wr.displayName = 'IconRotateRight';
var Ua = wr;
function Pt(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function _t(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? Pt(Object(t), !0).forEach(function (r) {
          B(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : Pt(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function $a(e, n) {
  var t = v.exports.useContext(H),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    o = e.spin,
    l = e.className,
    i = _t(
      _t({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-original-size'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    I('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: [
        s('path', { d: 'm5.5 11.5 5-2.5h1v32M34 11.5 39 9h1v32' }),
        s('path', {
          fill: 'currentColor',
          stroke: 'none',
          d: 'M24 17h1v1h-1v-1ZM24 30h1v1h-1v-1Z',
        }),
        s('path', { d: 'M24 17h1v1h-1v-1ZM24 30h1v1h-1v-1Z' }),
      ],
    })
  );
}
var Or = k.forwardRef($a);
Or.defaultProps = { isIcon: !0 };
Or.displayName = 'IconOriginalSize';
var Ma = Or,
  Fa =
    (globalThis && globalThis.__read) ||
    function (e, n) {
      var t = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!t) return e;
      var r = t.call(e),
        a,
        o = [],
        l;
      try {
        for (; (n === void 0 || n-- > 0) && !(a = r.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (t = r.return) && t.call(r);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    };
function Aa(e) {
  var n = Fa(v.exports.useState(e), 2),
    t = n[0],
    r = n[1],
    a = t === 'beforeLoad',
    o = t === 'loading',
    l = t === 'error',
    i = t === 'loaded',
    u = t === 'lazyload';
  return {
    status: t,
    isBeforeLoad: a,
    isLoading: o,
    isError: l,
    isLoaded: i,
    isLazyLoad: u,
    setStatus: r,
  };
}
var Et = [
    25, 33, 50, 67, 75, 80, 90, 100, 110, 125, 150, 175, 200, 250, 300, 400,
    500,
  ],
  Va = (function () {
    function e(n) {
      this.updateScale(n);
    }
    return (
      Object.defineProperty(e.prototype, 'scales', {
        get: function () {
          return this.scaleAttr;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'minScale', {
        get: function () {
          return this.scaleAttr[0];
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'maxScale', {
        get: function () {
          return this.scaleAttr[this.scaleAttr.length - 1];
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.updateScale = function (n) {
        var t = Et;
        if (
          (or(n) &&
            n.filter(function (l) {
              return l > 0;
            }).length &&
            (t = n.filter(function (l) {
              return l > 0;
            })),
          (t = t.map(function (l) {
            return +(l / 100).toFixed(2);
          })),
          !t.includes(1))
        ) {
          var r = this.findClosestIndex(1, t),
            a = t[r],
            o = a < 1 ? r + 1 : r;
          t.splice(o, 0, 1);
        }
        this.scaleAttr = t;
      }),
      (e.prototype.findClosestIndex = function (n, t) {
        if ((t === void 0 && (t = this.scaleAttr), !!t.length)) {
          if (t.length === 1) return 0;
          for (var r = t.length - 1, a = 0; a < t.length; a++) {
            var o = t[a];
            if (n === o) {
              r = a;
              break;
            }
            if (n < o) {
              var l = t[a - 1];
              r =
                l === void 0 || Math.abs(l - n) <= Math.abs(o - n) ? a - 1 : a;
              break;
            }
          }
          return r;
        }
      }),
      (e.prototype.getNextScale = function (n, t) {
        t === void 0 && (t = 'zoomIn');
        var r = this.scaleAttr.indexOf(n);
        return (
          r === -1 && (r = this.findClosestIndex(n)),
          t === 'zoomIn'
            ? r === this.scaleAttr.length - 1
              ? n
              : this.scaleAttr[r + 1]
            : r === 0
            ? n
            : this.scaleAttr[r - 1]
        );
      }),
      e
    );
  })(),
  za = Va;
function Wa(e, n, t, r, a) {
  var o = t,
    l = r;
  return (
    t &&
      (e.width > n.width
        ? (o = 0)
        : (n.left > e.left && (o -= Math.abs(e.left - n.left) / a),
          n.right < e.right && (o += Math.abs(e.right - n.right) / a))),
    r &&
      (e.height > n.height
        ? (l = 0)
        : (n.top > e.top && (l -= Math.abs(e.top - n.top) / a),
          n.bottom < e.bottom && (l += Math.abs(e.bottom - n.bottom) / a))),
    [o, l]
  );
}
var Za = function (e) {
    var n = e.style,
      t = e.className,
      r = e.prefixCls,
      a = e.popup,
      o = e.children,
      l = A(r + '-trigger', t);
    return k.createElement(
      xn,
      { style: n, className: l, popup: a, showArrow: !0 },
      o
    );
  },
  tr =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (tr =
          Object.assign ||
          function (e) {
            for (var n, t = 1, r = arguments.length; t < r; t++) {
              n = arguments[t];
              for (var a in n)
                Object.prototype.hasOwnProperty.call(n, a) && (e[a] = n[a]);
            }
            return e;
          }),
        tr.apply(this, arguments)
      );
    },
  Ha =
    (globalThis && globalThis.__rest) ||
    function (e, n) {
      var t = {};
      for (var r in e)
        Object.prototype.hasOwnProperty.call(e, r) &&
          n.indexOf(r) < 0 &&
          (t[r] = e[r]);
      if (e != null && typeof Object.getOwnPropertySymbols == 'function')
        for (var a = 0, r = Object.getOwnPropertySymbols(e); a < r.length; a++)
          n.indexOf(r[a]) < 0 &&
            Object.prototype.propertyIsEnumerable.call(e, r[a]) &&
            (t[r[a]] = e[r[a]]);
      return t;
    },
  Ke =
    (globalThis && globalThis.__read) ||
    function (e, n) {
      var t = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!t) return e;
      var r = t.call(e),
        a,
        o = [],
        l;
      try {
        for (; (n === void 0 || n-- > 0) && !(a = r.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (t = r.return) && t.call(r);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    },
  Ge =
    (globalThis && globalThis.__spreadArray) ||
    function (e, n, t) {
      if (t || arguments.length === 2)
        for (var r = 0, a = n.length, o; r < a; r++)
          (o || !(r in n)) &&
            (o || (o = Array.prototype.slice.call(n, 0, r)), (o[r] = n[r]));
      return e.concat(o || Array.prototype.slice.call(n));
    },
  Ba = function (e, n) {
    var t,
      r = e.prefixCls,
      a = e.previewPrefixCls,
      o = e.simple,
      l = o === void 0 ? !1 : o,
      i = e.actions,
      u = i === void 0 ? [] : i,
      c = e.actionsLayout,
      f = c === void 0 ? [] : c,
      d = e.defaultActions,
      g = d === void 0 ? [] : d,
      x = new Set(f),
      y = function (h) {
        return x.has(h.key);
      },
      j = Ge(Ge([], Ke(g.filter(y)), !1), Ke(u.filter(y)), !1),
      w = u.filter(function (h) {
        return !x.has(h.key);
      }),
      b = j.sort(function (h, C) {
        var N = f.indexOf(h.key),
          L = f.indexOf(C.key);
        return N > L ? 1 : -1;
      });
    if (x.has('extra')) {
      var _ = f.indexOf('extra');
      b.splice.apply(b, Ge([_, 0], Ke(w), !1));
    }
    var p = function (h, C) {
      var N;
      C === void 0 && (C = !1);
      var L = h.content,
        V = h.disabled,
        U = h.key,
        Q = h.name,
        X = h.getContainer,
        Y = h.onClick,
        te = Ha(h, [
          'content',
          'disabled',
          'key',
          'name',
          'getContainer',
          'onClick',
        ]),
        q = I('div', {
          ...tr(
            {
              className: A(
                a + '-toolbar-action',
                ((N = {}), (N[a + '-toolbar-action-disabled'] = V), N)
              ),
              key: U,
              onClick: function (ee) {
                !V && Y && Y(ee);
              },
              onMouseDown: function (ee) {
                ee.preventDefault();
              },
            },
            te
          ),
          children: [
            L &&
              s('span', {
                className: a + '-toolbar-action-content',
                children: L,
              }),
            C &&
              Q &&
              s('span', { className: a + '-toolbar-action-name', children: Q }),
          ],
        });
      return X ? X(q) : q;
    };
    if (!b.length) return null;
    var O = b.map(function (h) {
      var C = p(h, l);
      return !l && h.name && !h.getContainer
        ? s(Oe, { content: h.name, children: C }, h.key)
        : C;
    });
    return I('div', {
      ref: n,
      className: A(
        a + '-toolbar',
        ((t = {}), (t[a + '-toolbar-simple'] = l), t),
        e.className
      ),
      style: e.style,
      children: [
        l &&
          s(Za, {
            prefixCls: r,
            className: a + '-trigger',
            popup: function () {
              return s('div', { children: O });
            },
            children: p({
              key: 'trigger',
              content: s('span', { children: s(wn, {}) }),
            }),
          }),
        !l && O,
      ],
    });
  },
  qa = v.exports.forwardRef(Ba),
  Ut = v.exports.createContext({
    previewGroup: !1,
    previewUrlMap: new Map(),
    previewPropsMap: new Map(),
    infinite: !0,
    currentIndex: 0,
    setCurrentIndex: function () {
      return null;
    },
    setPreviewUrlMap: function () {
      return null;
    },
    registerPreviewUrl: function () {
      return null;
    },
    registerPreviewProps: function () {
      return null;
    },
    visible: !1,
    handleVisibleChange: function () {
      return null;
    },
  });
function Ka(e) {
  var n,
    t,
    r = e.current,
    a = e.previewCount,
    o = e.infinite,
    l = o === void 0 ? !1 : o,
    i = e.onPrev,
    u = e.onNext,
    c = v.exports.useContext(ve).getPrefixCls,
    f = c('image-preview'),
    d = A(f + '-arrow'),
    g = !l && r <= 0,
    x = !l && r >= a - 1;
  return I('div', {
    className: d,
    children: [
      s('div', {
        className: A(
          f + '-arrow-left',
          ((n = {}), (n[f + '-arrow-disabled'] = g), n)
        ),
        onClick: function (y) {
          y.preventDefault(), !g && (i == null || i());
        },
        children: s(On, {}),
      }),
      s('div', {
        className: A(
          f + '-arrow-right',
          ((t = {}), (t[f + '-arrow-disabled'] = x), t)
        ),
        onClick: function (y) {
          y.preventDefault(), !x && (u == null || u());
        },
        children: s(Cn, {}),
      }),
    ],
  });
}
var ce =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (ce =
          Object.assign ||
          function (e) {
            for (var n, t = 1, r = arguments.length; t < r; t++) {
              n = arguments[t];
              for (var a in n)
                Object.prototype.hasOwnProperty.call(n, a) && (e[a] = n[a]);
            }
            return e;
          }),
        ce.apply(this, arguments)
      );
    },
  Ga =
    (globalThis && globalThis.__rest) ||
    function (e, n) {
      var t = {};
      for (var r in e)
        Object.prototype.hasOwnProperty.call(e, r) &&
          n.indexOf(r) < 0 &&
          (t[r] = e[r]);
      if (e != null && typeof Object.getOwnPropertySymbols == 'function')
        for (var a = 0, r = Object.getOwnPropertySymbols(e); a < r.length; a++)
          n.indexOf(r[a]) < 0 &&
            Object.prototype.propertyIsEnumerable.call(e, r[a]) &&
            (t[r[a]] = e[r[a]]);
      return t;
    },
  ne =
    (globalThis && globalThis.__read) ||
    function (e, n) {
      var t = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!t) return e;
      var r = t.call(e),
        a,
        o = [],
        l;
      try {
        for (; (n === void 0 || n-- > 0) && !(a = r.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (t = r.return) && t.call(r);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    },
  Xe = 90,
  Xa = {
    maskClosable: !0,
    closable: !0,
    breakPoint: 316,
    actionsLayout: [
      'fullScreen',
      'rotateRight',
      'rotateLeft',
      'zoomIn',
      'zoomOut',
      'originalSize',
      'extra',
    ],
    getPopupContainer: function () {
      return document.body;
    },
    escToExit: !0,
    scales: Et,
    resetTranslate: !0,
  };
function Ya(e, n) {
  var t,
    r = v.exports.useContext(Ut),
    a = r.previewGroup,
    o = r.previewUrlMap,
    l = r.currentIndex,
    i = r.setCurrentIndex,
    u = r.infinite,
    c = r.previewPropsMap,
    f = a ? c.get(l) : {},
    d = ir(e, Xa, f),
    g = d.className,
    x = d.style,
    y = d.src,
    j = d.defaultVisible,
    w = d.maskClosable,
    b = d.closable,
    _ = d.breakPoint,
    p = d.actions,
    O = d.actionsLayout,
    h = d.getPopupContainer,
    C = d.onVisibleChange,
    N = d.scales,
    L = d.escToExit,
    V = d.imgAttributes,
    U = V === void 0 ? {} : V,
    Q = d.imageRender,
    X = d.extra,
    Y = X === void 0 ? null : X,
    te = d.resetTranslate,
    q = a ? o.get(l) : y,
    ee = ne(v.exports.useState(q), 2),
    S = ee[0],
    z = ee[1],
    E = ne(er(!1, { defaultValue: j, value: d.visible }), 2),
    P = E[0],
    D = E[1],
    $ = v.exports.useContext(ve),
    J = $.getPrefixCls,
    W = $.locale,
    M = $.rtl,
    pe = J('image'),
    re = pe + '-preview',
    Vt = A(re, ((t = {}), (t[re + '-hide'] = !P), (t[re + '-rtl'] = M), t), g),
    _e = v.exports.useRef(),
    zt = v.exports.useRef(),
    ge = v.exports.useRef(),
    Pr = v.exports.useRef(),
    Le = v.exports.useRef(!1),
    ye = v.exports.useRef({ pageX: 0, pageY: 0, originX: 0, originY: 0 }),
    De = Aa('loading'),
    Wt = De.isLoading,
    Zt = De.isLoaded,
    Ee = De.setStatus,
    _r = ne(v.exports.useState(!1), 2),
    Ht = _r[0],
    Bt = _r[1],
    Ir = ne(v.exports.useState({ x: 0, y: 0 }), 2),
    ae = Ir[0],
    Ue = Ir[1],
    jr = ne(v.exports.useState(1), 2),
    F = jr[0],
    $e = jr[1],
    Nr = ne(v.exports.useState(!1), 2),
    Sr = Nr[0],
    kr = Nr[1],
    Tr = ne(v.exports.useState(0), 2),
    Ie = Tr[0],
    Me = Tr[1],
    Rr = ne(v.exports.useState(!1), 2),
    se = Rr[0],
    Lr = Rr[1],
    ue = v.exports.useMemo(function () {
      return new za(N);
    }, []),
    Dr = U.onLoad,
    Er = U.onError,
    Fe = U.onMouseDown,
    qt = U.style,
    Kt = U.className,
    Gt = Ga(U, ['onLoad', 'onError', 'onMouseDown', 'style', 'className']);
  function Ae() {
    Ue({ x: 0, y: 0 }), $e(1), Me(0);
  }
  v.exports.useImperativeHandle(n, function () {
    return {
      reset: Ae,
      getRootDOMNode: function () {
        return Pr.current;
      },
    };
  });
  var Ur = ne(v.exports.useState(), 2),
    je = Ur[0],
    Xt = Ur[1],
    $r = v.exports.useCallback(
      function () {
        return je;
      },
      [je]
    );
  v.exports.useEffect(
    function () {
      var m = h == null ? void 0 : h(),
        T = Pn(m) || document.body;
      Xt(T);
    },
    [h]
  ),
    _n($r, { hidden: P });
  var Yt = v.exports.useMemo(
    function () {
      return !In && je === document.body;
    },
    [je]
  );
  function Mr(m) {
    var T = o.size;
    u && ((m %= T), m < 0 && (m = T - Math.abs(m))),
      m !== l && m >= 0 && m <= T - 1 && i(m);
  }
  function Fr() {
    Mr(l - 1);
  }
  function Ar() {
    Mr(l + 1);
  }
  function Jt() {
    Me(Ie === 0 ? 360 - Xe : Ie - Xe);
  }
  function Qt() {
    Me((Ie + Xe) % 360);
  }
  var Ve = v.exports.useRef(null),
    en = function () {
      !Sr && kr(!0),
        Ve.current && clearTimeout(Ve.current),
        (Ve.current = setTimeout(function () {
          kr(!1);
        }, 1e3));
    },
    Ne = function (m) {
      F !== m && ($e(m), en());
    };
  function ze() {
    var m = ue.getNextScale(F, 'zoomIn');
    Ne(m);
  }
  function We() {
    var m = ue.getNextScale(F, 'zoomOut');
    Ne(m);
  }
  function rn(m) {
    m.deltaY > 0 ? F >= ue.minScale && We() : F <= ue.maxScale && ze();
  }
  function tn() {
    Ne(1);
  }
  function nn() {
    var m = ge.current.getBoundingClientRect(),
      T = _e.current.getBoundingClientRect(),
      K = m.height / (T.height / F),
      ie = m.width / (T.width / F),
      me = Math.max(K, ie);
    Ne(me);
  }
  function Vr(m) {
    m.target === m.currentTarget && w && Ze();
  }
  function an() {
    Ze();
  }
  function Ze() {
    P && (C && C(!1, P), St(d.visible) && D(!1));
  }
  function on(m) {
    if (m && m.length) {
      var T = m[0].contentRect,
        K = T.width < _;
      Bt(K);
    }
  }
  var zr = function () {
      if (!(!ge.current || !_e.current)) {
        var m = ge.current.getBoundingClientRect(),
          T = _e.current.getBoundingClientRect(),
          K = ne(Wa(m, T, ae.x, ae.y, F), 2),
          ie = K[0],
          me = K[1];
        (ie !== ae.x || me !== ae.y) && Ue({ x: ie, y: me });
      }
    },
    Wr = function (m) {
      if (P && se) {
        m.preventDefault && m.preventDefault();
        var T = ye.current,
          K = T.originX,
          ie = T.originY,
          me = T.pageX,
          dn = T.pageY,
          vn = K + (m.pageX - me) / F,
          pn = ie + (m.pageY - dn) / F;
        Ue({ x: vn, y: pn });
      }
    },
    Zr = function (m) {
      m.preventDefault && m.preventDefault(), Lr(!1);
    };
  function ln(m) {
    Ee('loaded'), Dr && Dr(m);
  }
  function sn(m) {
    Ee('error'), Er && Er(m);
  }
  var cn = function (m) {
    var T;
    (T = m.preventDefault) === null || T === void 0 || T.call(m), Lr(!0);
    var K = m.type === 'touchstart' ? m.touches[0] : m;
    (ye.current.pageX = K.pageX),
      (ye.current.pageY = K.pageY),
      (ye.current.originX = ae.x),
      (ye.current.originY = ae.y),
      Fe == null || Fe(m);
  };
  v.exports.useEffect(
    function () {
      return (
        P &&
          se &&
          (He(document, 'mousemove', Wr, !1), He(document, 'mouseup', Zr, !1)),
        function () {
          Be(document, 'mousemove', Wr, !1), Be(document, 'mouseup', Zr, !1);
        }
      );
    },
    [P, se]
  ),
    v.exports.useEffect(
      function () {
        te && !se && zr();
      },
      [se, ae]
    ),
    v.exports.useEffect(
      function () {
        te && zr();
      },
      [F]
    ),
    v.exports.useEffect(
      function () {
        P && Ae();
      },
      [P]
    ),
    v.exports.useEffect(
      function () {
        z(q), Ee(q ? 'loading' : 'loaded'), Ae();
      },
      [q]
    ),
    jn(
      function () {
        ue.updateScale(N), $e(1);
      },
      [N]
    ),
    v.exports.useEffect(
      function () {
        var m = function (T) {
          if (T)
            switch (T.key) {
              case $n.key:
                L && Ze();
                break;
              case Un.key:
                Ar();
                break;
              case En.key:
                Fr();
                break;
              case Dn.key:
                ze();
                break;
              case Ln.key:
                We();
                break;
            }
        };
        return (
          P &&
            !se &&
            !Le.current &&
            ((Le.current = !0), He(document, 'keydown', m)),
          function () {
            (Le.current = !1), Be(document, 'keydown', m);
          }
        );
      },
      [P, L, se, l, F]
    );
  var un = [
      {
        key: 'fullScreen',
        name: W.ImagePreview.fullScreen,
        content: s(Ra, {}),
        onClick: nn,
      },
      {
        key: 'rotateRight',
        name: W.ImagePreview.rotateRight,
        content: s(Ua, {}),
        onClick: Qt,
      },
      {
        key: 'rotateLeft',
        name: W.ImagePreview.rotateLeft,
        content: s(Da, {}),
        onClick: Jt,
      },
      {
        key: 'zoomIn',
        name: W.ImagePreview.zoomIn,
        content: s(ka, {}),
        onClick: ze,
        disabled: F === ue.maxScale,
      },
      {
        key: 'zoomOut',
        name: W.ImagePreview.zoomOut,
        content: s(Na, {}),
        onClick: We,
        disabled: F === ue.minScale,
      },
      {
        key: 'originalSize',
        name: W.ImagePreview.originalSize,
        content: s(Ma, {}),
        onClick: tn,
      },
    ],
    fn = function () {
      var m,
        T,
        K = s('img', {
          ...ce(
            {
              onWheel: rn,
              ref: _e,
              className: A(
                Kt,
                re + '-img',
                ((m = {}), (m[re + '-img-moving'] = se), m)
              ),
              style: ce(ce({}, qt), {
                transform:
                  'translate(' +
                  ae.x +
                  'px, ' +
                  ae.y +
                  'px) rotate(' +
                  Ie +
                  'deg)',
              }),
              key: S,
              src: S,
            },
            Gt,
            {
              onLoad: ln,
              onError: sn,
              onMouseDown: function (ie) {
                ie.button === 0 && cn(ie);
              },
            }
          ),
        });
      return (T = Q == null ? void 0 : Q(K)) !== null && T !== void 0 ? T : K;
    };
  return s(Nn, {
    visible: P,
    forceRender: !1,
    getContainer: $r,
    children: s(Sn, {
      ...ce({}, $, {
        getPopupContainer: function () {
          return ge.current;
        },
      }),
      children: I('div', {
        className: Vt,
        style: ce(
          ce({}, x || {}),
          Yt ? {} : { zIndex: 'inherit', position: 'absolute' }
        ),
        ref: Pr,
        children: [
          s(Ce, {
            in: P,
            timeout: 400,
            appear: !0,
            classNames: 'fadeImage',
            mountOnEnter: !0,
            unmountOnExit: !1,
            onEnter: function (m) {
              !m ||
                ((m.parentNode.style.display = 'block'),
                (m.style.display = 'block'));
            },
            onExited: function (m) {
              !m ||
                ((m.parentNode.style.display = ''), (m.style.display = 'none'));
            },
            children: s('div', { className: re + '-mask' }),
          }),
          P &&
            s(kn, {
              onResize: on,
              getTargetDOMNode: function () {
                return ge.current;
              },
              children: I('div', {
                ref: ge,
                className: re + '-wrapper',
                onClick: Vr,
                children: [
                  I('div', {
                    ref: zt,
                    className: re + '-img-container',
                    style: { transform: 'scale(' + F + ', ' + F + ')' },
                    onClick: Vr,
                    children: [
                      fn(),
                      Wt &&
                        s('div', {
                          className: re + '-loading',
                          children: s(Tn, {}),
                        }),
                    ],
                  }),
                  s(Ce, {
                    in: Sr,
                    timeout: 400,
                    appear: !0,
                    classNames: 'fadeImage',
                    unmountOnExit: !0,
                    children: I('div', {
                      className: re + '-scale-value',
                      children: [(F * 100).toFixed(0), '%'],
                    }),
                  }),
                  Zt &&
                    s(qa, {
                      prefixCls: pe,
                      previewPrefixCls: re,
                      actions: p,
                      actionsLayout: O,
                      defaultActions: un,
                      simple: Ht,
                    }),
                  b &&
                    s('div', {
                      className: re + '-close-btn',
                      onClick: an,
                      children: s(Rn, {}),
                    }),
                  a &&
                    s(Ka, {
                      previewCount: o.size,
                      current: l,
                      infinite: u,
                      onPrev: Fr,
                      onNext: Ar,
                    }),
                  Y,
                ],
              }),
            }),
        ],
      }),
    }),
  });
}
var $t = v.exports.forwardRef(Ya);
$t.displayName = 'ImagePreview';
var Ja = $t,
  nr =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (nr =
          Object.assign ||
          function (e) {
            for (var n, t = 1, r = arguments.length; t < r; t++) {
              n = arguments[t];
              for (var a in n)
                Object.prototype.hasOwnProperty.call(n, a) && (e[a] = n[a]);
            }
            return e;
          }),
        nr.apply(this, arguments)
      );
    },
  Qa =
    (globalThis && globalThis.__rest) ||
    function (e, n) {
      var t = {};
      for (var r in e)
        Object.prototype.hasOwnProperty.call(e, r) &&
          n.indexOf(r) < 0 &&
          (t[r] = e[r]);
      if (e != null && typeof Object.getOwnPropertySymbols == 'function')
        for (var a = 0, r = Object.getOwnPropertySymbols(e); a < r.length; a++)
          n.indexOf(r[a]) < 0 &&
            Object.prototype.propertyIsEnumerable.call(e, r[a]) &&
            (t[r[a]] = e[r[a]]);
      return t;
    },
  xe =
    (globalThis && globalThis.__read) ||
    function (e, n) {
      var t = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!t) return e;
      var r = t.call(e),
        a,
        o = [],
        l;
      try {
        for (; (n === void 0 || n-- > 0) && !(a = r.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (t = r.return) && t.call(r);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    };
function ei(e, n) {
  var t = e.children,
    r = e.srcList,
    a = e.infinite,
    o = e.current,
    l = e.defaultCurrent,
    i = e.onChange,
    u = e.visible,
    c = e.defaultVisible,
    f = e.forceRender,
    d = e.onVisibleChange,
    g = Qa(e, [
      'children',
      'srcList',
      'infinite',
      'current',
      'defaultCurrent',
      'onChange',
      'visible',
      'defaultVisible',
      'forceRender',
      'onVisibleChange',
    ]),
    x = xe(er(!1, { value: u, defaultValue: c }), 2),
    y = x[0],
    j = x[1],
    w = v.exports.useMemo(
      function () {
        return r
          ? new Map(
              r.map(function (P, D) {
                return [D, { url: P, preview: !0 }];
              })
            )
          : null;
      },
      [r]
    ),
    b = Mn(),
    _ = function () {
      return w ? new Map(w) : new Map();
    },
    p = xe(v.exports.useState(_()), 2),
    O = p[0],
    h = p[1],
    C = v.exports.useRef(),
    N = C.current || new Map(),
    L = function (P) {
      C.current = P(C.current);
    };
  v.exports.useEffect(
    function () {
      b || h(_());
    },
    [w]
  );
  var V = new Map(
      Array.from(O)
        .filter(function (P) {
          var D = xe(P, 2),
            $ = D[1].preview;
          return $;
        })
        .map(function (P) {
          var D = xe(P, 2),
            $ = D[0],
            J = D[1].url;
          return [$, J];
        })
    ),
    U = xe(er(0, { value: o, defaultValue: l }), 2),
    Q = U[0],
    X = U[1];
  function Y(P, D, $) {
    return (
      w ||
        h(function (J) {
          return new Map(J).set(P, { url: D, preview: $ });
        }),
      function () {
        w ||
          h(function (W) {
            var M = new Map(W),
              pe = M.delete(P);
            return pe ? M : W;
          });
      }
    );
  }
  function te(P, D) {
    return (
      L(function ($) {
        return new Map($).set(P, le(D) ? D : {});
      }),
      function () {
        L(function (J) {
          var W = new Map(J),
            M = W.delete(P);
          return M ? W : J;
        });
      }
    );
  }
  var q = v.exports.useRef();
  v.exports.useImperativeHandle(n, function () {
    return {
      reset: function () {
        q.current && q.current.reset();
      },
    };
  });
  var ee = function (P, D) {
      var $ = St(D) ? y : D;
      d && d(P, $), j(P);
    },
    S = function (P) {
      i && i(P), X(P);
    },
    z = function (P) {
      var D = 0,
        $ = function (J) {
          var W = k.Children.map(J, function (M) {
            if (M && M.props && M.type) {
              var pe = M.type.displayName;
              if (pe === 'Image') return k.cloneElement(M, { _index: D++ });
            }
            return M && M.props && M.props.children
              ? k.cloneElement(M, { children: $(M.props.children) })
              : M;
          });
          return !or(J) && k.Children.count(J) === 1 ? W[0] : W;
        };
      return $(P);
    },
    E = function () {
      var P = Array.from(V.values());
      return P.length > 0
        ? s('div', {
            style: { display: 'none' },
            children: P.map(function (D) {
              return s('img', { src: D }, D);
            }),
          })
        : null;
    };
  return I(Ut.Provider, {
    value: {
      previewGroup: !0,
      previewUrlMap: V,
      previewPropsMap: N,
      infinite: a,
      currentIndex: Q,
      setCurrentIndex: S,
      setPreviewUrlMap: h,
      registerPreviewUrl: Y,
      registerPreviewProps: te,
      visible: y,
      handleVisibleChange: ee,
    },
    children: [
      z(t),
      s(Ja, { ...nr({ ref: q, src: '', visible: y, onVisibleChange: ee }, g) }),
      f && E(),
    ],
  });
}
var Mt = v.exports.forwardRef(ei);
Mt.displayName = 'ImagePreviewGroup';
var ri = Mt,
  Se =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (Se =
          Object.assign ||
          function (e) {
            for (var n, t = 1, r = arguments.length; t < r; t++) {
              n = arguments[t];
              for (var a in n)
                Object.prototype.hasOwnProperty.call(n, a) && (e[a] = n[a]);
            }
            return e;
          }),
        Se.apply(this, arguments)
      );
    },
  ti =
    (globalThis && globalThis.__rest) ||
    function (e, n) {
      var t = {};
      for (var r in e)
        Object.prototype.hasOwnProperty.call(e, r) &&
          n.indexOf(r) < 0 &&
          (t[r] = e[r]);
      if (e != null && typeof Object.getOwnPropertySymbols == 'function')
        for (var a = 0, r = Object.getOwnPropertySymbols(e); a < r.length; a++)
          n.indexOf(r[a]) < 0 &&
            Object.prototype.propertyIsEnumerable.call(e, r[a]) &&
            (t[r[a]] = e[r[a]]);
      return t;
    },
  ni =
    (globalThis && globalThis.__read) ||
    function (e, n) {
      var t = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!t) return e;
      var r = t.call(e),
        a,
        o = [],
        l;
      try {
        for (; (n === void 0 || n-- > 0) && !(a = r.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (t = r.return) && t.call(r);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    },
  Ft = function (e) {
    var n,
      t = v.exports.useContext(ve),
      r = t.locale,
      a = t.rtl,
      o = e.listType,
      l = e.fileList,
      i = e.renderUploadList,
      u = e.renderUploadItem,
      c = e.prefixCls,
      f = ti(e, [
        'listType',
        'fileList',
        'renderUploadList',
        'renderUploadItem',
        'prefixCls',
      ]),
      d = ni(v.exports.useState(-1), 2),
      g = d[0],
      x = d[1],
      y = v.exports.useMemo(
        function () {
          return l
            .map(function (w) {
              var b = w.url;
              return (
                w.url === void 0 &&
                  [R.init, R.success].indexOf(w.status) > -1 &&
                  (b =
                    w.originFile &&
                    Z(URL.createObjectURL) &&
                    URL.createObjectURL(w.originFile)),
                b
              );
            })
            .filter(Boolean);
        },
        [l]
      );
    if (Z(i)) return s('div', { className: c + '-list', children: i(l, f) });
    var j = function (w) {
      e.imagePreview && x(w);
    };
    return I(Re, {
      children: [
        s(Fn, {
          className: A(
            c + '-list',
            c + '-list-type-' + o,
            ((n = {}), (n[c + '-list-rtl'] = a), n)
          ),
          children: l.map(function (w, b) {
            var _ =
              o === 'picture-card'
                ? s('div', {
                    className: c + '-list-item ' + c + '-list-item-' + w.status,
                    children: s(ma, {
                      ...Se({}, e, {
                        onPreview: function (p) {
                          var O;
                          j(b),
                            (O = e.onPreview) === null ||
                              O === void 0 ||
                              O.call(e, p);
                        },
                        file: w,
                        locale: r,
                      }),
                    }),
                  })
                : s(Ia, { ...Se({}, e, { file: w, locale: r }) });
            return (
              Z(u) && (_ = u(_, w, l)),
              o === 'picture-card'
                ? s(
                    Ce,
                    {
                      timeout: { enter: 200, exit: 400 },
                      classNames: c + '-slide-inline',
                      onEntered: function (p) {
                        !p || (p.style.width = '');
                      },
                      onExit: function (p) {
                        !p || (p.style.width = p.scrollWidth + 'px');
                      },
                      onExiting: function (p) {
                        !p || (p.style.width = 0);
                      },
                      onExited: function (p) {
                        !p || (p.style.width = 0);
                      },
                      children: _,
                    },
                    w.uid
                  )
                : s(
                    Ce,
                    {
                      timeout: { enter: 200, exit: 400 },
                      classNames: c + '-slide-up',
                      onExit: function (p) {
                        !p || (p.style.height = p.scrollHeight + 'px');
                      },
                      onExiting: function (p) {
                        !p || (p.style.height = 0);
                      },
                      onExited: function (p) {
                        !p || (p.style.height = 0);
                      },
                      children: _,
                    },
                    w.uid
                  )
            );
          }),
        }),
        o === 'picture-card' &&
          e.imagePreview &&
          s(ri, {
            srcList: y,
            visible: g !== -1,
            current: g,
            onChange: j,
            onVisibleChange: function (w) {
              j(w ? g : -1);
            },
          }),
      ],
    });
  };
Ft.displayName = 'FileList';
var ai = Ft;
function It(e) {
  var n = e.responseText || e.response;
  if (!n) return n;
  try {
    return JSON.parse(n);
  } catch {
    return n;
  }
}
var ii = function (e) {
    var n = e.onProgress,
      t = n === void 0 ? qe : n,
      r = e.onError,
      a = r === void 0 ? qe : r,
      o = e.onSuccess,
      l = o === void 0 ? qe : o,
      i = e.action,
      u = e.method,
      c = e.headers,
      f = c === void 0 ? {} : c,
      d = e.name,
      g = e.file,
      x = e.data,
      y = x === void 0 ? {} : x,
      j = e.withCredentials,
      w = j === void 0 ? !1 : j;
    function b(N) {
      return typeof N == 'function' ? N(g) : N;
    }
    var _ = b(d),
      p = b(y),
      O = new XMLHttpRequest();
    t &&
      O.upload &&
      (O.upload.onprogress = function (N) {
        var L;
        N.total > 0 && (L = (N.loaded / N.total) * 100), t(parseInt(L, 10), N);
      }),
      (O.onerror = function (L) {
        a(L);
      }),
      (O.onload = function () {
        if (O.status < 200 || O.status >= 300) return a(It(O));
        l(It(O));
      });
    var h = new FormData();
    p &&
      Object.keys(p).map(function (N) {
        return h.append(N, p[N]);
      }),
      h.append(_ || 'file', g),
      O.open(u, i, !0),
      w && 'withCredentials' in O && (O.withCredentials = !0);
    for (var C in f)
      f.hasOwnProperty(C) && f[C] !== null && O.setRequestHeader(C, f[C]);
    return (
      O.send(h),
      {
        abort: function () {
          O.abort();
        },
      }
    );
  },
  oi = ii,
  Cr = function (e, n) {
    var t = le(n) ? (n == null ? void 0 : n.type) : n,
      r = !(le(n) && n.strict === !1);
    if (r && t && e) {
      var a = or(t)
          ? t
          : t
              .split(',')
              .map(function (l) {
                return l.trim();
              })
              .filter(function (l) {
                return l;
              }),
        o = (
          e.name.indexOf('.') > -1 ? '.' + e.name.split('.').pop() : ''
        ).toLowerCase();
      return a.some(function (l) {
        var i = l && l.toLowerCase(),
          u = (e.type || '').toLowerCase(),
          c = u.split('/')[0];
        if (
          i === u ||
          '' + c + o.replace('.', '/') === i ||
          /^\*(\/\*)?$/.test(i)
        )
          return !0;
        if (/\/\*/.test(i))
          return u.replace(/\/.*$/, '') === i.replace(/\/.*$/, '');
        if (/\..*/.test(i)) {
          var f = [i];
          return (
            (i === '.jpg' || i === '.jpeg') && (f = ['.jpg', '.jpeg']),
            f.indexOf(o) > -1
          );
        }
        return !1;
      });
    }
    return !!e;
  },
  li = function (e, n) {
    if (!!e) {
      var t = [].slice.call(e);
      return (
        n &&
          (t = t.filter(function (r) {
            return Cr(r, n);
          })),
        t
      );
    }
  },
  si = function (e, n, t) {
    var r = [],
      a = 0,
      o = function () {
        !a && t(r);
      },
      l = function (u) {
        if (((a += 1), u != null && u.isFile)) {
          u.file(function (g) {
            (a -= 1),
              Cr(g, n) &&
                (Object.defineProperty(g, 'webkitRelativePath', {
                  value: u.fullPath.replace(/^\//, ''),
                }),
                r.push(g)),
              o();
          });
          return;
        }
        if (u != null && u.isDirectory) {
          var c = u.createReader(),
            f = !1,
            d = function () {
              c.readEntries(function (g) {
                f || ((a -= 1), (f = !0)),
                  g.length === 0 ? o() : (d(), g.forEach(l));
              });
            };
          d();
          return;
        }
        (a -= 1), o();
      },
      i = [].slice.call(e);
    i.forEach(function (u) {
      u.webkitGetAsEntry && l(u.webkitGetAsEntry());
    });
  },
  ke =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (ke =
          Object.assign ||
          function (e) {
            for (var n, t = 1, r = arguments.length; t < r; t++) {
              n = arguments[t];
              for (var a in n)
                Object.prototype.hasOwnProperty.call(n, a) && (e[a] = n[a]);
            }
            return e;
          }),
        ke.apply(this, arguments)
      );
    },
  Ye =
    (globalThis && globalThis.__read) ||
    function (e, n) {
      var t = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!t) return e;
      var r = t.call(e),
        a,
        o = [],
        l;
      try {
        for (; (n === void 0 || n-- > 0) && !(a = r.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (t = r.return) && t.call(r);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    },
  jt =
    (globalThis && globalThis.__spreadArray) ||
    function (e, n, t) {
      if (t || arguments.length === 2)
        for (var r = 0, a = n.length, o; r < a; r++)
          (o || !(r in n)) &&
            (o || (o = Array.prototype.slice.call(n, 0, r)), (o[r] = n[r]));
      return e.concat(o || Array.prototype.slice.call(n));
    },
  ci = function (e, n) {
    var t,
      r,
      a = Te(),
      o = v.exports.useContext(ve).locale,
      l = Ye(v.exports.useState(!1), 2),
      i = l[0],
      u = l[1],
      c = Ye(v.exports.useState(0), 2),
      f = c[0],
      d = c[1],
      g = e.tip,
      x = e.children,
      y = e.disabled,
      j = e.drag,
      w = e.listType,
      b = e.prefixCls,
      _ = e.accept,
      p = e.multiple,
      O = { disabled: y };
    return (
      v.exports.useEffect(
        function () {
          d(0);
        },
        [i]
      ),
      x === null
        ? null
        : s('div', {
            ...ke(
              { className: b + '-trigger', onClick: y ? void 0 : e.onClick },
              a({
                onPressEnter: function () {
                  var h;
                  !y && ((h = e.onClick) === null || h === void 0 || h.call(e));
                },
              }),
              {
                ref: n,
                onDragEnter: function () {
                  d(f + 1);
                },
                onDragLeave: function (h) {
                  var C;
                  h.preventDefault(),
                    f === 0
                      ? (u(!1),
                        !y &&
                          ((C = e.onDragLeave) === null ||
                            C === void 0 ||
                            C.call(e, h)))
                      : d(f - 1);
                },
                onDrop: function (h) {
                  if ((h.preventDefault(), !y && e.drag !== !1)) {
                    if ((u(!1), e.directory))
                      si(h.dataTransfer.items, _, function (V) {
                        e.onDragFiles && e.onDragFiles(V);
                      });
                    else {
                      var C = [].slice
                          .call(h.dataTransfer.items || [])
                          .reduce(function (V, U, Q) {
                            if (U.webkitGetAsEntry) {
                              var X = U.webkitGetAsEntry();
                              return X != null && X.isDirectory
                                ? jt(jt([], Ye(V), !1), [Q], !1)
                                : V;
                            }
                          }, []),
                        N = [].slice
                          .call(h.dataTransfer.files || [])
                          .filter(function (V, U) {
                            return !C.includes(U);
                          }),
                        L = li(N, _);
                      L.length > 0 &&
                        e.onDragFiles &&
                        e.onDragFiles(p ? L : L.slice(0, 1));
                    }
                    e.onDrop && e.onDrop(h);
                  }
                },
                onDragOver: function (h) {
                  var C;
                  h.preventDefault(),
                    !y &&
                      !i &&
                      (u(!0),
                      (C = e.onDragOver) === null ||
                        C === void 0 ||
                        C.call(e, h));
                },
              }
            ),
            children: k.isValidElement(x)
              ? s('div', {
                  className: A(
                    ((t = {}), (t[b + '-trigger-custom-active'] = i), t)
                  ),
                  children: k.cloneElement(x, O),
                })
              : w === 'picture-card'
              ? s('div', {
                  className: b + '-trigger-picture-wrapper',
                  children: s('div', {
                    className: b + '-trigger-picture',
                    tabIndex: 0,
                    'aria-label': o.Upload.upload,
                    children: s('div', {
                      className: b + '-trigger-picture-text',
                      children: s(rr, {}),
                    }),
                  }),
                })
              : j
              ? I('div', {
                  className: A(
                    b + '-trigger-drag',
                    ((r = {}), (r[b + '-trigger-drag-active'] = i), r)
                  ),
                  tabIndex: 0,
                  'aria-label': o.Upload.drag,
                  children: [
                    s(rr, {}),
                    s('p', {
                      className: b + '-trigger-drag-text',
                      children: i ? o.Upload.dragHover : o.Upload.drag,
                    }),
                    g &&
                      s('div', { className: b + '-trigger-tip', children: g }),
                  ],
                })
              : I(An, {
                  ...ke({}, O, {
                    'aria-label': o.Upload.upload,
                    type: 'primary',
                    className: b + '-trigger-with-icon',
                  }),
                  children: [s(cr, {}), o.Upload.upload],
                }),
          })
    );
  },
  ui = v.exports.forwardRef(ci),
  fi =
    (globalThis && globalThis.__extends) ||
    (function () {
      var e = function (n, t) {
        return (
          (e =
            Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array &&
              function (r, a) {
                r.__proto__ = a;
              }) ||
            function (r, a) {
              for (var o in a)
                Object.prototype.hasOwnProperty.call(a, o) && (r[o] = a[o]);
            }),
          e(n, t)
        );
      };
      return function (n, t) {
        if (typeof t != 'function' && t !== null)
          throw new TypeError(
            'Class extends value ' + String(t) + ' is not a constructor or null'
          );
        e(n, t);
        function r() {
          this.constructor = n;
        }
        n.prototype =
          t === null
            ? Object.create(t)
            : ((r.prototype = t.prototype), new r());
      };
    })(),
  G =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (G =
          Object.assign ||
          function (e) {
            for (var n, t = 1, r = arguments.length; t < r; t++) {
              n = arguments[t];
              for (var a in n)
                Object.prototype.hasOwnProperty.call(n, a) && (e[a] = n[a]);
            }
            return e;
          }),
        G.apply(this, arguments)
      );
    },
  di =
    (globalThis && globalThis.__awaiter) ||
    function (e, n, t, r) {
      function a(o) {
        return o instanceof t
          ? o
          : new t(function (l) {
              l(o);
            });
      }
      return new (t || (t = Promise))(function (o, l) {
        function i(f) {
          try {
            c(r.next(f));
          } catch (d) {
            l(d);
          }
        }
        function u(f) {
          try {
            c(r.throw(f));
          } catch (d) {
            l(d);
          }
        }
        function c(f) {
          f.done ? o(f.value) : a(f.value).then(i, u);
        }
        c((r = r.apply(e, n || [])).next());
      });
    },
  vi =
    (globalThis && globalThis.__generator) ||
    function (e, n) {
      var t = {
          label: 0,
          sent: function () {
            if (o[0] & 1) throw o[1];
            return o[1];
          },
          trys: [],
          ops: [],
        },
        r,
        a,
        o,
        l;
      return (
        (l = { next: i(0), throw: i(1), return: i(2) }),
        typeof Symbol == 'function' &&
          (l[Symbol.iterator] = function () {
            return this;
          }),
        l
      );
      function i(c) {
        return function (f) {
          return u([c, f]);
        };
      }
      function u(c) {
        if (r) throw new TypeError('Generator is already executing.');
        for (; t; )
          try {
            if (
              ((r = 1),
              a &&
                (o =
                  c[0] & 2
                    ? a.return
                    : c[0]
                    ? a.throw || ((o = a.return) && o.call(a), 0)
                    : a.next) &&
                !(o = o.call(a, c[1])).done)
            )
              return o;
            switch (((a = 0), o && (c = [c[0] & 2, o.value]), c[0])) {
              case 0:
              case 1:
                o = c;
                break;
              case 4:
                return t.label++, { value: c[1], done: !1 };
              case 5:
                t.label++, (a = c[1]), (c = [0]);
                continue;
              case 7:
                (c = t.ops.pop()), t.trys.pop();
                continue;
              default:
                if (
                  ((o = t.trys),
                  !(o = o.length > 0 && o[o.length - 1]) &&
                    (c[0] === 6 || c[0] === 2))
                ) {
                  t = 0;
                  continue;
                }
                if (c[0] === 3 && (!o || (c[1] > o[0] && c[1] < o[3]))) {
                  t.label = c[1];
                  break;
                }
                if (c[0] === 6 && t.label < o[1]) {
                  (t.label = o[1]), (o = c);
                  break;
                }
                if (o && t.label < o[2]) {
                  (t.label = o[2]), t.ops.push(c);
                  break;
                }
                o[2] && t.ops.pop(), t.trys.pop();
                continue;
            }
            c = n.call(e, t);
          } catch (f) {
            (c = [6, f]), (a = 0);
          } finally {
            r = o = 0;
          }
        if (c[0] & 5) throw c[1];
        return { value: c[0] ? c[1] : void 0, done: !0 };
      }
    },
  pi = (function (e) {
    fi(n, e);
    function n(t) {
      var r = e.call(this, t) || this;
      return (
        (r.upload = function (a) {
          r.doUpload(a);
        }),
        (r.abort = function (a) {
          var o = r.state.uploadRequests[a.uid];
          o &&
            (o.abort && o.abort(),
            r.updateFileStatus(G(G({}, a), { status: R.fail })),
            r.deleteReq(a.uid));
        }),
        (r.reupload = function (a) {
          r.doUpload(G(G({}, a), { percent: 0, status: R.uploading }));
        }),
        (r.deleteReq = function (a) {
          var o = G({}, r.state.uploadRequests);
          delete o[a], r.setState({ uploadRequests: o });
        }),
        (r.delete = r.deleteReq),
        (r.updateFileStatus = function (a, o) {
          o === void 0 && (o = r.props.fileList);
          var l = r.props.onFileStatusChange,
            i = 'uid' in a ? 'uid' : 'name';
          l &&
            l(
              o.map(function (u) {
                return u[i] === a[i] ? a : u;
              }),
              a
            );
        }),
        (r.getTargetFile = function (a) {
          var o = 'uid' in a ? 'uid' : 'name',
            l = r.props.fileList.find(function (i) {
              return i[o] === a[o];
            });
          return l;
        }),
        (r.doUpload = function (a) {
          return di(r, void 0, void 0, function () {
            var o,
              l,
              i,
              u,
              c,
              f,
              d,
              g,
              x,
              y,
              j,
              w,
              b,
              _,
              p = this;
            return vi(this, function (O) {
              switch (O.label) {
                case 0:
                  return (
                    (o = this.props),
                    (l = o.action),
                    (i = o.headers),
                    (u = o.name),
                    (c = o.data),
                    (f = o.withCredentials),
                    (d = o.customRequest),
                    (g = o.method),
                    (x = function (h, C) {
                      var N = p.getTargetFile(a);
                      N &&
                        ((N.status = R.uploading),
                        (N.percent = h),
                        p.props.onProgress && p.props.onProgress(N, C));
                    }),
                    (y = function (h) {
                      var C = p.getTargetFile(a);
                      C &&
                        ((C.status = R.success),
                        (C.response = h),
                        p.updateFileStatus(C)),
                        p.deleteReq(a.uid);
                    }),
                    (j = function (h) {
                      var C = p.getTargetFile(a);
                      C &&
                        ((C.status = R.fail),
                        (C.response = h),
                        p.updateFileStatus(C)),
                        p.deleteReq(a.uid);
                    }),
                    (w = {
                      onProgress: x,
                      onSuccess: y,
                      onError: j,
                      headers: i,
                      name: u,
                      file: a.originFile,
                      data: c,
                      withCredentials: f,
                    }),
                    this.updateFileStatus(a),
                    l
                      ? ((b = oi(G(G({}, w), { action: l, method: g }))),
                        [3, 3])
                      : [3, 1]
                  );
                case 1:
                  return d ? [4, d(w)] : [3, 3];
                case 2:
                  (b = O.sent()), (O.label = 3);
                case 3:
                  return (
                    this.setState({
                      uploadRequests: G(
                        G({}, this.state.uploadRequests),
                        ((_ = {}), (_[a.uid] = b), _)
                      ),
                    }),
                    [2]
                  );
              }
            });
          });
        }),
        (r.handleFiles = function (a) {
          var o = r.props,
            l = o.limit,
            i = o.fileList,
            u = o.onExceedLimit,
            c = o.autoUpload;
          if (kt(l) && l < i.length + a.length) return u && u(a, i);
          var f = function (d, g) {
            var x = r.props.fileList || [],
              y = {
                uid: '' + String(+new Date()) + g,
                originFile: d,
                percent: 0,
                status: R.init,
                name: d.name,
              };
            x.push(y),
              r.updateFileStatus(y, x),
              c &&
                setTimeout(function () {
                  r.doUpload(G(G({}, y), { status: R.uploading }));
                }, 0);
          };
          a.forEach(function (d, g) {
            Cr(d, r.props.accept) &&
              (Z(r.props.beforeUpload)
                ? Promise.resolve(r.props.beforeUpload(d, a))
                    .then(function (x) {
                      if (x !== !1) {
                        var y = Vn(x) ? x : d;
                        f(y, g);
                      }
                    })
                    .catch(function (x) {
                      console.error(x);
                    })
                : f(d, g));
          });
        }),
        (r.state = { uploadRequests: {} }),
        r
      );
    }
    return (
      (n.prototype.render = function () {
        var t = this,
          r = this.props,
          a = r.accept,
          o = r.multiple,
          l = r.children,
          i = r.prefixCls,
          u = r.tip,
          c = r.disabled,
          f = r.drag,
          d = r.listType,
          g = r.hide,
          x = r.directory,
          y = r.onDrop,
          j = r.onDragOver,
          w = r.onDragLeave;
        return k.createElement(
          k.Fragment,
          null,
          k.createElement(
            'input',
            G(
              {
                key: 'trigger-input',
                ref: function (b) {
                  return (t.inputRef = b);
                },
                style: { display: 'none' },
                type: 'file',
                accept: le(a) ? (a == null ? void 0 : a.type) : a,
                multiple: o,
              },
              x ? { webkitdirectory: 'true' } : {},
              {
                onChange: function (b) {
                  var _ = b.target.files;
                  _ &&
                    (t.handleFiles([].slice.call(_)), (t.inputRef.value = ''));
                },
                onClick: function (b) {
                  b.stopPropagation();
                },
              }
            )
          ),
          k.createElement(
            Ce,
            {
              key: 'trigger-node',
              in: !g,
              timeout: 100,
              unmountOnExit: !0,
              classNames: 'fadeIn',
            },
            k.createElement(
              ui,
              {
                directory: x,
                tip: u,
                multiple: o,
                accept: a,
                disabled: c,
                drag: f,
                listType: d,
                onDrop: y,
                onDragOver: j,
                onDragLeave: w,
                onDragFiles: this.handleFiles,
                onClick: function () {
                  !c && t.inputRef && t.inputRef.click();
                },
                prefixCls: i,
              },
              Z(l) ? l({ fileList: this.props.fileList }) : l
            )
          ),
          u && d !== 'picture-card' && !f
            ? k.createElement(
                'div',
                { key: 'trigger-tip', className: i + '-trigger-tip' },
                u
              )
            : null
        );
      }),
      n
    );
  })(k.Component),
  gi = pi,
  de =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (de =
          Object.assign ||
          function (e) {
            for (var n, t = 1, r = arguments.length; t < r; t++) {
              n = arguments[t];
              for (var a in n)
                Object.prototype.hasOwnProperty.call(n, a) && (e[a] = n[a]);
            }
            return e;
          }),
        de.apply(this, arguments)
      );
    },
  mi =
    (globalThis && globalThis.__rest) ||
    function (e, n) {
      var t = {};
      for (var r in e)
        Object.prototype.hasOwnProperty.call(e, r) &&
          n.indexOf(r) < 0 &&
          (t[r] = e[r]);
      if (e != null && typeof Object.getOwnPropertySymbols == 'function')
        for (var a = 0, r = Object.getOwnPropertySymbols(e); a < r.length; a++)
          n.indexOf(r[a]) < 0 &&
            Object.prototype.propertyIsEnumerable.call(e, r[a]) &&
            (t[r[a]] = e[r[a]]);
      return t;
    },
  hi =
    (globalThis && globalThis.__read) ||
    function (e, n) {
      var t = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!t) return e;
      var r = t.call(e),
        a,
        o = [],
        l;
      try {
        for (; (n === void 0 || n-- > 0) && !(a = r.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (t = r.return) && t.call(r);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    },
  Je = function (e) {
    var n = [].concat(e || []).filter(Boolean);
    return n.reduce(function (t, r, a) {
      if (r.uid) {
        var o = n.findIndex(function (u) {
            return r.uid === u.uid && r !== u;
          }),
          l = de({ status: R.success, percent: 100 }, r);
        o === -1 ? t.push(l) : t.splice(o, 1, l);
      } else {
        var i = '' + String(+new Date()) + a;
        t.push(de({ uid: i, status: R.success, percent: 100 }, r));
      }
      return t;
    }, []);
  },
  bi = {
    listType: 'text',
    autoUpload: !0,
    showUploadList: !0,
    beforeUpload: function () {
      return !0;
    },
    method: 'post',
  },
  yi = function (e, n) {
    var t,
      r = v.exports.useContext(ve),
      a = r.getPrefixCls,
      o = r.componentConfig,
      l = r.rtl,
      i = ir(e, bi, o == null ? void 0 : o.Upload),
      u = a('upload'),
      c = v.exports.useRef(),
      f = v.exports.useRef(),
      d = hi(
        v.exports.useState(function () {
          return 'fileList' in i
            ? Je(i.fileList)
            : 'defaultFileList' in i
            ? Je(i.defaultFileList)
            : [];
        }),
        2
      ),
      g = d[0],
      x = d[1],
      y = 'fileList' in i ? Je(i.fileList) : g,
      j = function (S, z) {
        var E;
        'fileList' in i || x(S),
          (E = i.onChange) === null || E === void 0 || E.call(i, S, z);
      },
      w = function (S) {
        S &&
          setTimeout(function () {
            c.current && c.current.upload(S);
          }, 0);
      },
      b = function (S) {
        c.current && c.current.reupload(S), i.onReupload && i.onReupload(S);
      },
      _ = function (S) {
        if (S) {
          var z = i.onRemove;
          Promise.resolve(Z(z) ? z(S, y) : z)
            .then(function (E) {
              E !== !1 &&
                (c.current && c.current.abort(S),
                j(
                  y.filter(function (P) {
                    return P.uid !== S.uid;
                  }),
                  S
                ));
            })
            .catch(function (E) {
              console.error(E);
            });
        }
      },
      p = function (S) {
        S && c.current && c.current.abort(S);
      };
    v.exports.useImperativeHandle(n, function () {
      return {
        submit: function (S) {
          var z = [];
          S
            ? (z = [S])
            : (z = y.filter(function (E) {
                return E.status === R.init;
              })),
            z.forEach(function (E) {
              w(E);
            });
        },
        abort: function (S) {
          p(S);
        },
        reupload: function (S) {
          b(S);
        },
        getRootDOMNode: function () {
          return f.current;
        },
      };
    });
    var O = i.listType,
      h = i.className,
      C = i.style,
      N = i.renderUploadItem,
      L = i.showUploadList,
      V = i.renderUploadList,
      U = i.progressProps,
      Q = i.imagePreview,
      X = mi(i, [
        'listType',
        'className',
        'style',
        'renderUploadItem',
        'showUploadList',
        'renderUploadList',
        'progressProps',
        'imagePreview',
      ]),
      Y = kt(i.limit)
        ? { hideOnExceedLimit: !0, maxCount: i.limit }
        : de({ hideOnExceedLimit: !0 }, i.limit),
      te = Y.maxCount && Y.maxCount <= y.length,
      q = 'disabled' in i ? i.disabled : !Y.hideOnExceedLimit && te,
      ee = s('div', {
        ...de(
          {},
          Nt(X, [
            'disabled',
            'directory',
            'onReupload',
            'defaultFileList',
            'fileList',
            'autoUpload',
            'error',
            'action',
            'method',
            'multiple',
            'name',
            'accept',
            'customRequest',
            'children',
            'autoUpload',
            'limit',
            'drag',
            'tip',
            'headers',
            'data',
            'withCredentials',
            'onChange',
            'onPreview',
            'onRemove',
            'onProgress',
            'onExceedLimit',
            'beforeUpload',
            'onDrop',
            'onDragOver',
            'onDragLeave',
          ]),
          {
            className: A(
              u,
              ((t = {}),
              (t[u + '-type-' + O] = O),
              (t[u + '-drag'] = i.drag),
              (t[u + '-disabled'] = q),
              (t[u + '-hide'] = Y.hideOnExceedLimit && te),
              (t[u + '-rtl'] = l),
              t),
              h
            ),
            style: C,
            ref: f,
          }
        ),
        children: s(gi, {
          ...de({ ref: c }, i, {
            limit: Y.maxCount,
            hide: Y.hideOnExceedLimit && te,
            disabled: q,
            prefixCls: u,
            fileList: y,
            onProgress: function (S, z) {
              S &&
                ('fileList' in i ||
                  x(
                    y.map(function (E) {
                      return E.uid === S.uid ? S : E;
                    })
                  ),
                i.onProgress && i.onProgress(S, z));
            },
            onFileStatusChange: j,
          }),
        }),
      });
    return I(Re, {
      children: [
        O !== 'picture-card' && ee,
        L &&
          s(ai, {
            imagePreview: Q,
            progressProps: U,
            showUploadList: L,
            disabled: i.disabled,
            listType: O,
            fileList: y,
            renderUploadItem: N,
            renderUploadList: V,
            onUpload: w,
            onAbort: p,
            onRemove: _,
            onReupload: b,
            onPreview: i.onPreview,
            prefixCls: u,
          }),
        O === 'picture-card' && ee,
        i.tip &&
          O === 'picture-card' &&
          s('div', { className: u + '-trigger-tip', children: i.tip }),
      ],
    });
  },
  At = v.exports.forwardRef(yi);
At.displayName = 'Upload';
var xi = At;
var fe = {
  'info-wrapper': '_info-wrapper_1rqux_1',
  'info-avatar': '_info-avatar_1rqux_4',
  'info-content': '_info-content_1rqux_14',
  'verified-tag': '_verified-tag_1rqux_20',
  'edit-btn': '_edit-btn_1rqux_25',
};
function Ii({ userInfo: e = {}, loading: n }) {
  const t = zn(Hn),
    [r, a] = v.exports.useState('');
  function o(u, c) {
    a(c.originFile ? URL.createObjectURL(c.originFile) : '');
  }
  v.exports.useEffect(() => {
    a(e.avatar);
  }, [e]);
  const l = s(qr, {
      text: { rows: 0 },
      style: { width: '100px', height: '100px' },
      animation: !0,
    }),
    i = s(qr, { text: { rows: 1 }, animation: !0 });
  return I('div', {
    className: fe['info-wrapper'],
    children: [
      s(xi, {
        showUploadList: !1,
        onChange: o,
        children: n
          ? l
          : s(Wn, {
              size: 100,
              triggerIcon: s(Bn, {}),
              className: fe['info-avatar'],
              children: r ? s('img', { src: r }) : s(rr, {}),
            }),
      }),
      s(Zn, {
        className: fe['info-content'],
        column: 2,
        colon: '\uFF1A',
        labelStyle: { textAlign: 'right' },
        data: [
          { label: t['userSetting.label.name'], value: n ? i : e.name },
          {
            label: t['userSetting.label.verified'],
            value: n
              ? i
              : I('span', {
                  children: [
                    e.verified
                      ? s(Hr, {
                          color: 'green',
                          className: fe['verified-tag'],
                          children: t['userSetting.value.verified'],
                        })
                      : s(Hr, {
                          color: 'red',
                          className: fe['verified-tag'],
                          children: t['userSetting.value.notVerified'],
                        }),
                    s(Br, {
                      role: 'button',
                      className: fe['edit-btn'],
                      children: t['userSetting.btn.edit'],
                    }),
                  ],
                }),
          },
          {
            label: t['userSetting.label.accountId'],
            value: n ? i : e.accountId,
          },
          {
            label: t['userSetting.label.phoneNumber'],
            value: n
              ? i
              : I('span', {
                  children: [
                    e.phoneNumber,
                    s(Br, {
                      role: 'button',
                      className: fe['edit-btn'],
                      children: t['userSetting.btn.edit'],
                    }),
                  ],
                }),
          },
          {
            label: t['userSetting.label.registrationTime'],
            value: n ? i : e.registrationTime,
          },
        ],
      }),
    ],
  });
}
export { Ii as default };
