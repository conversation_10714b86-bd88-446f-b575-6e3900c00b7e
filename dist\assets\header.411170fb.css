.arco-progress {
  position: relative;
  line-height: 1;
  font-size: 12px;
}
.arco-progress-line,
.arco-progress-steps {
  display: inline-block;
  max-width: 100%;
  width: 100%;
}
.arco-progress-line-wrapper,
.arco-progress-steps-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 100%;
  height: 100%;
}
.arco-progress-line-text,
.arco-progress-steps-text {
  font-size: 12px;
  margin-left: 16px;
  color: var(--color-text-2);
  white-space: nowrap;
  text-align: right;
  flex-grow: 1;
  flex-shrink: 0;
  min-width: 32px;
}
.arco-progress-line-text .arco-icon,
.arco-progress-steps-text .arco-icon {
  font-size: 12px;
  margin-left: 4px;
}
.arco-progress-line-outer {
  background-color: var(--color-fill-3);
  border-radius: 100px;
  width: 100%;
  position: relative;
  display: inline-block;
  overflow: hidden;
}
.arco-progress-line-inner {
  height: 100%;
  border-radius: 100px;
  background-color: rgb(var(--primary-6));
  position: relative;
  transition: width 0.6s cubic-bezier(0.34, 0.69, 0.1, 1),
    background 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
  max-width: 100%;
}
.arco-progress-line-inner-buffer {
  position: absolute;
  background-color: var(--color-primary-light-3);
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 0 100px 100px 0;
  max-width: 100%;
  transition: all 0.6s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-progress-line-inner-animate:after {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: inherit;
  background: linear-gradient(
    90deg,
    transparent 25%,
    rgba(255, 255, 255, 0.5) 50%,
    transparent 75%
  );
  background-size: 400% 100%;
  animation: arco-progress-loading 1.5s cubic-bezier(0.34, 0.69, 0.1, 1)
    infinite;
}
.arco-progress-line-text .arco-icon {
  color: var(--color-text-2);
}
.arco-progress-steps-outer {
  display: flex;
  width: 100%;
}
.arco-progress-steps-text {
  margin-left: 8px;
  min-width: unset;
}
.arco-progress-steps-text .arco-icon {
  color: var(--color-text-2);
}
.arco-progress-steps-item {
  height: 100%;
  flex: 1;
  background-color: var(--color-fill-3);
  position: relative;
  display: inline-block;
}
.arco-progress-steps-item:not(:last-of-type) {
  margin-right: 3px;
}
.arco-progress-steps-item:last-of-type {
  border-top-right-radius: 100px;
  border-bottom-right-radius: 100px;
}
.arco-progress-steps-item:first-of-type {
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
}
.arco-progress-steps-item-active {
  background-color: rgb(var(--primary-6));
}
.arco-progress-steps.arco-progress-small {
  width: auto;
}
.arco-progress-steps.arco-progress-small .arco-progress-steps-item {
  width: 2px;
  flex: unset;
  border-radius: 2px;
}
.arco-progress-steps.arco-progress-small
  .arco-progress-steps-item:not(:last-of-type) {
  margin-right: 3px;
}
.arco-progress-is-warning .arco-progress-line-inner,
.arco-progress-is-warning .arco-progress-steps-item-active {
  background-color: rgb(var(--warning-6));
}
.arco-progress-is-warning .arco-progress-line-text .arco-icon,
.arco-progress-is-warning .arco-progress-steps-text .arco-icon {
  color: rgb(var(--warning-6));
}
.arco-progress-is-success .arco-progress-line-inner,
.arco-progress-is-success .arco-progress-steps-item-active {
  background-color: rgb(var(--success-6));
}
.arco-progress-is-success .arco-progress-line-text .arco-icon,
.arco-progress-is-success .arco-progress-steps-text .arco-icon {
  color: rgb(var(--success-6));
}
.arco-progress-is-error .arco-progress-line-inner,
.arco-progress-is-error .arco-progress-steps-item-active {
  background-color: rgb(var(--danger-6));
}
.arco-progress-is-error .arco-progress-line-text .arco-icon,
.arco-progress-is-error .arco-progress-steps-text .arco-icon {
  color: rgb(var(--danger-6));
}
.arco-progress-small .arco-progress-line-text {
  font-size: 12px;
  margin-left: 16px;
}
.arco-progress-small .arco-progress-line-text .arco-icon {
  font-size: 12px;
}
.arco-progress-large .arco-progress-line-text {
  font-size: 16px;
  margin-left: 16px;
}
.arco-progress-large .arco-progress-line-text .arco-icon {
  font-size: 14px;
}
.arco-progress-circle {
  display: inline-block;
}
.arco-progress-circle-wrapper {
  position: relative;
  text-align: center;
  line-height: 1;
  display: inline-block;
  vertical-align: text-bottom;
}
.arco-progress-circle-svg {
  transform: rotate(-90deg);
}
.arco-progress-circle-text {
  font-size: 14px;
}
.arco-progress-circle-text .arco-icon {
  font-size: 16px;
  color: var(--color-text-2);
}
.arco-progress-circle .arco-progress-circle-text {
  position: absolute;
  top: 50%;
  left: 50%;
  color: var(--color-text-3);
  transform: translate(-50%, -50%);
}
.arco-progress-circle-mask {
  stroke: var(--color-fill-3);
}
.arco-progress-circle-path {
  stroke: rgb(var(--primary-6));
  transition: stroke-dashoffset 0.6s cubic-bezier(0, 0, 1, 1) 0s,
    stroke 0.6s cubic-bezier(0, 0, 1, 1);
}
.arco-progress-mini .arco-progress-circle-mask {
  stroke: var(--color-primary-light-3);
}
.arco-progress-mini .arco-progress-circle-path {
  stroke: rgb(var(--primary-6));
}
.arco-progress-mini.arco-progress-is-warning .arco-progress-circle-mask {
  stroke: var(--color-warning-light-3);
}
.arco-progress-mini.arco-progress-is-error .arco-progress-circle-mask {
  stroke: var(--color-danger-light-3);
}
.arco-progress-mini.arco-progress-is-success .arco-progress-circle-mask {
  stroke: var(--color-success-light-3);
}
.arco-progress-mini.arco-progress-is-success
  .arco-progress-circle-wrapper
  .arco-icon-check {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%) translateY(-50%);
}
.arco-progress-mini .arco-progress-circle-text {
  position: static;
  top: unset;
  left: unset;
  transform: unset;
}
.arco-progress-small .arco-progress-circle-text {
  font-size: 13px;
}
.arco-progress-small .arco-progress-circle-text .arco-icon {
  font-size: 14px;
}
.arco-progress-large .arco-progress-circle-text,
.arco-progress-large .arco-progress-circle-text .arco-icon {
  font-size: 16px;
}
.arco-progress-is-warning .arco-progress-circle-path {
  stroke: rgb(var(--warning-6));
}
.arco-progress-is-warning .arco-icon {
  color: rgb(var(--warning-6));
}
.arco-progress-is-success .arco-progress-circle-path {
  stroke: rgb(var(--success-6));
}
.arco-progress-is-success .arco-icon {
  color: rgb(var(--success-6));
}
.arco-progress-is-error .arco-progress-circle-path {
  stroke: rgb(var(--danger-6));
}
.arco-progress-is-error .arco-icon {
  color: rgb(var(--danger-6));
}
@keyframes arco-progress-loading {
  0% {
    background-position: 100% 50%;
  }
  to {
    background-position: 0 50%;
  }
}
.arco-progress-rtl .arco-progress-line-text,
.arco-progress-rtl .arco-progress-steps-text {
  margin-left: 0;
  margin-right: 16px;
}
.arco-progress-rtl .arco-progress-line-text .arco-icon,
.arco-progress-rtl .arco-progress-steps-text .arco-icon {
  margin-left: 0;
  margin-right: 4px;
}
.arco-progress-rtl .arco-progress-steps-text {
  margin-left: 0;
  margin-right: 8px;
}
.arco-progress-rtl .arco-progress-steps-item:not(:last-of-type) {
  margin-right: 0;
  margin-left: 3px;
}
.arco-progress-rtl.arco-progress-steps.arco-progress-small
  .arco-progress-steps-item:not(:last-of-type) {
  margin-right: 0;
  margin-left: 3px;
}
.arco-progress-rtl.arco-progress-small .arco-progress-line-text,
.arco-progress-rtl.arco-progress-large .arco-progress-line-text {
  margin-right: 16px;
  margin-left: 0;
}
.arco-progress-rtl.arco-progress-line .arco-progress-line-inner-buffer {
  left: initial;
  right: 0;
}
.arco-image-trigger {
  padding: 6px 4px;
  background: var(--color-bg-5);
  border: 1px solid var(--color-neutral-3);
  border-radius: 4px;
}
.arco-image-trigger .arco-trigger-arrow {
  border: 1px solid var(--color-neutral-3);
  background-color: var(--color-bg-5);
}
.arco-image {
  position: relative;
  display: inline-block;
  border-radius: var(--border-radius-small);
  vertical-align: middle;
}
.arco-image-img {
  vertical-align: middle;
  border-radius: inherit;
}
.arco-image-img:focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--primary-6));
}
.arco-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.arco-image-footer {
  width: 100%;
  max-width: 100%;
  display: flex;
}
.arco-image-footer-block {
  flex: auto;
}
.arco-image-caption-title {
  font-size: 16px;
  font-weight: 500;
}
.arco-image-caption-description {
  font-size: 14px;
}
.arco-image-actions {
  padding-left: 12px;
}
.arco-image-actions-list {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.arco-image-actions-item {
  font-size: 14px;
  line-height: 1;
  margin-left: 12px;
  border-radius: var(--border-radius-small);
  padding: 0;
  cursor: pointer;
}
.arco-image-actions-item:first-child {
  margin-left: 0;
}
.arco-image-actions-item-trigger {
  padding: 5px 4px;
  display: inline-block;
}
.arco-image-with-footer-inner .arco-image-footer {
  background: linear-gradient(
    360deg,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  color: var(--color-white);
  box-sizing: border-box;
  padding: 9px 16px;
  align-items: center;
  position: absolute;
  left: 0;
  bottom: 0;
  border-bottom-right-radius: inherit;
  border-bottom-left-radius: inherit;
}
.arco-image-with-footer-inner .arco-image-caption-title,
.arco-image-with-footer-inner .arco-image-caption-description {
  color: var(--color-white);
}
.arco-image-with-footer-inner .arco-image-actions-item:hover {
  background: rgba(0, 0, 0, 0.5);
}
.arco-image-with-footer-outer .arco-image-footer {
  color: var(--color-neutral-8);
  margin-top: 4px;
}
.arco-image-with-footer-outer .arco-image-caption-title {
  color: var(--color-text-1);
}
.arco-image-with-footer-outer .arco-image-caption-description {
  color: var(--color-neutral-6);
}
.arco-image-with-footer-outer .arco-image-actions-item:hover {
  background: var(--color-neutral-2);
}
.arco-image-with-preview:hover {
  cursor: zoom-in;
}
.arco-image-error {
  width: 100%;
  height: 100%;
  background-color: var(--color-neutral-1);
  color: var(--color-neutral-4);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.arco-image-error-icon {
  width: 60px;
  height: 60px;
  max-width: 100%;
  max-height: 100%;
}
.arco-image-error-icon > svg {
  width: 100%;
  height: 100%;
}
.arco-image-error-alt {
  font-size: 12px;
  line-height: 1.6667;
  text-align: center;
  padding: 8px 16px;
}
.arco-image-loader {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: var(--color-neutral-1);
}
.arco-image-loader-spin {
  font-size: 32px;
  transform: translate(-50%, -50%);
  position: absolute;
  color: rgb(var(--primary-6));
  left: 50%;
  top: 50%;
  text-align: center;
}
.arco-image-loader-spin-text {
  color: var(--color-neutral-6);
  font-size: 16px;
}
.arco-image-simple.arco-image-with-footer-inner .arco-image-footer {
  padding: 12px 16px;
}
.arco-image-before-load .arco-image-img,
.arco-image-loading .arco-image-img,
.arco-image-loading-error .arco-image-img {
  visibility: hidden;
}
.arco-image-trigger .arco-image-actions-list {
  flex-direction: column;
}
.arco-image-trigger .arco-image-actions-item {
  color: var(--color-neutral-8);
  margin-left: 0;
}
.arco-image-trigger .arco-image-actions-item:hover {
  background: var(--color-neutral-2);
}
.arco-image-preview {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1001;
}
.arco-image-preview-hide {
  display: none;
}
.arco-image-preview-mask,
.arco-image-preview-wrapper {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.arco-image-preview-mask {
  background-color: var(--color-mask-bg);
}
.arco-image-preview-img-container {
  width: 100%;
  height: 100%;
  text-align: center;
}
.arco-image-preview-img-container:before {
  content: '';
  width: 0;
  height: 100%;
  vertical-align: middle;
  display: inline-block;
}
.arco-image-preview-img-container .arco-image-preview-img {
  max-width: 100%;
  max-height: 100%;
  display: inline-block;
  vertical-align: middle;
  user-select: none;
  cursor: grab;
}
.arco-image-preview-img-container
  .arco-image-preview-img.arco-image-preview-img-moving {
  cursor: grabbing;
}
.arco-image-preview-scale-value {
  padding: 7px 10px;
  box-sizing: border-box;
  font-size: 12px;
  color: var(--color-white);
  background-color: #ffffff14;
  line-height: initial;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.arco-image-preview-toolbar {
  background-color: var(--color-bg-2);
  border-radius: var(--border-radius-medium);
  display: flex;
  align-items: flex-start;
  padding: 4px 16px;
  position: absolute;
  bottom: 46px;
  left: 50%;
  transform: translate(-50%);
}
.arco-image-preview-toolbar-action {
  font-size: 14px;
  color: var(--color-neutral-8);
  border-radius: var(--border-radius-small);
  background-color: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
}
.arco-image-preview-toolbar-action:not(:last-of-type) {
  margin-right: 0;
}
.arco-image-preview-toolbar-action:hover {
  background-color: var(--color-neutral-2);
  color: rgb(var(--primary-6));
}
.arco-image-preview-toolbar-action-disabled,
.arco-image-preview-toolbar-action-disabled:hover {
  color: var(--color-text-4);
  background-color: transparent;
  cursor: not-allowed;
}
.arco-image-preview-toolbar-action-name {
  font-size: 12px;
  padding-right: 12px;
}
.arco-image-preview-toolbar-action-content {
  padding: 13px;
  line-height: 1;
}
.arco-image-preview-toolbar-simple {
  padding: 4px;
}
.arco-image-preview-toolbar-simple .arco-image-preview-toolbar-action {
  margin-right: 0;
}
.arco-image-preview-trigger.arco-image-trigger {
  padding: 12px 16px;
}
.arco-image-preview-trigger.arco-image-trigger
  .arco-image-preview-toolbar-action {
  text-align: left;
  margin-right: 0;
}
.arco-image-preview-trigger.arco-image-trigger
  .arco-image-preview-toolbar-action:not(:last-of-type) {
  margin-bottom: 0;
}
.arco-image-preview-loading {
  color: rgb(var(--primary-6));
  background-color: #232324;
  font-size: 18px;
  padding: 10px;
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-medium);
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.arco-image-preview-close-btn {
  width: 32px;
  height: 32px;
  line-height: 32px;
  background: rgba(0, 0, 0, 0.5);
  color: var(--color-white);
  text-align: center;
  border-radius: 50%;
  position: absolute;
  right: 36px;
  top: 36px;
  cursor: pointer;
  font-size: 14px;
}
.arco-image-preview-arrow-left,
.arco-image-preview-arrow-right {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  color: var(--color-white);
  background-color: #ffffff4d;
  cursor: pointer;
  z-index: 2;
}
.arco-image-preview-arrow-left > svg,
.arco-image-preview-arrow-right > svg {
  color: var(--color-white);
  font-size: 16px;
}
.arco-image-preview-arrow-left:hover,
.arco-image-preview-arrow-right:hover {
  background-color: #ffffff80;
}
.arco-image-preview-arrow-left {
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
}
.arco-image-preview-arrow-right {
  top: 50%;
  transform: translateY(-50%);
  right: 20px;
}
.arco-image-preview-arrow-disabled {
  cursor: not-allowed;
  background-color: #fff3;
  color: #ffffff4d;
}
.arco-image-preview-arrow-disabled > svg {
  color: #ffffff4d;
}
.arco-image-preview-arrow-disabled:hover {
  background-color: #fff3;
}
.fadeImage-enter,
.fadeImage-appear {
  opacity: 0;
}
.fadeImage-enter-active,
.fadeImage-appear-active {
  opacity: 1;
  transition: opacity 0.4s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.fadeImage-exit {
  opacity: 1;
}
.fadeImage-exit-active {
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.arco-image-rtl {
  direction: rtl;
}
.arco-image-rtl .arco-image-actions-item {
  margin-left: 0;
  margin-right: 12px;
}
.arco-image-rtl .arco-image-actions-item:first-child {
  margin-right: 0;
}
.arco-upload {
  display: inline-block;
  max-width: 100%;
}
.arco-upload-type-picture-card {
  vertical-align: top;
}
.arco-upload-drag {
  width: 100%;
}
.arco-upload-hide {
  display: none;
}
.arco-upload-disabled .arco-upload-trigger-picture,
.arco-upload-disabled .arco-upload-trigger-picture:hover {
  cursor: not-allowed;
  border-color: var(--color-neutral-4);
  background-color: var(--color-fill-1);
  color: var(--color-text-4);
}
.arco-upload-disabled .arco-upload-trigger-drag,
.arco-upload-disabled .arco-upload-trigger-drag:hover {
  cursor: not-allowed;
  border-color: var(--color-text-4);
  background-color: var(--color-fill-1);
}
.arco-upload-disabled .arco-upload-trigger-drag .arco-icon-plus,
.arco-upload-disabled .arco-upload-trigger-drag:hover .arco-icon-plus,
.arco-upload-disabled .arco-upload-trigger-drag .arco-upload-trigger-drag-text,
.arco-upload-disabled
  .arco-upload-trigger-drag:hover
  .arco-upload-trigger-drag-text,
.arco-upload-disabled .arco-upload-trigger-tip {
  color: var(--color-text-4);
}
.arco-upload-trigger {
  cursor: pointer;
  display: inline-block;
  vertical-align: top;
  width: 100%;
}
.arco-upload-trigger-tip {
  color: var(--color-text-3);
  margin-top: 4px;
  font-size: 12px;
  line-height: 1.5;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.arco-upload-trigger-picture {
  min-width: 80px;
  height: 80px;
  color: var(--color-text-2);
  text-align: center;
  margin-bottom: 0;
  background: var(--color-fill-2);
  border-radius: var(--border-radius-small);
  border: 1px dashed var(--color-neutral-3);
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-upload-trigger-picture:hover {
  border-color: var(--color-neutral-4);
  background-color: var(--color-fill-3);
  color: var(--color-text-2);
}
.arco-upload-trigger-picture:focus-visible {
  box-shadow: 0 0 0 2px var(--color-primary-light-3);
}
.arco-upload-trigger-picture-text {
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}
.arco-upload-trigger-drag {
  width: 100%;
  border-radius: var(--border-radius-small);
  border: 1px dashed var(--color-neutral-3);
  background-color: var(--color-fill-1);
  text-align: center;
  transition: all 0.2s ease;
  color: var(--color-text-1);
  padding: 50px 0;
}
.arco-upload-trigger-drag .arco-icon-plus {
  font-size: 14px;
  margin-bottom: 24px;
  color: var(--color-text-2);
}
.arco-upload-trigger-drag:hover {
  border-color: var(--color-neutral-4);
  background-color: var(--color-fill-3);
}
.arco-upload-trigger-drag:hover .arco-upload-trigger-drag-text {
  color: var(--color-text-1);
}
.arco-upload-trigger-drag:hover .arco-icon-plus {
  color: var(--color-text-2);
}
.arco-upload-trigger-drag:focus-visible {
  box-shadow: 0 0 0 2px var(--color-primary-light-3);
}
.arco-upload-trigger-drag-active {
  border-color: rgb(var(--primary-6));
  color: var(--color-text-1);
  background-color: var(--color-primary-light-1);
}
.arco-upload-trigger-drag-active .arco-upload-trigger-drag-text {
  color: var(--color-text-1);
}
.arco-upload-trigger-drag-active .arco-icon-plus {
  color: rgb(var(--primary-6));
}
.arco-upload-trigger-drag .arco-upload-trigger-tip {
  margin-top: 0;
}
.arco-upload-trigger-drag-text {
  color: var(--color-text-1);
  line-height: 1.5;
  font-size: 14px;
}
.arco-upload-hide + .arco-upload-list .arco-upload-list-item:first-of-type {
  margin-top: 0;
}
.arco-upload-list {
  width: 100%;
}
.arco-upload-list-type-text .arco-upload-list-item:first-of-type,
.arco-upload-list-type-picture-list .arco-upload-list-item:first-of-type {
  margin-top: 24px;
}
.arco-upload-list-file-icon {
  line-height: 16px;
  font-size: 16px;
  color: rgb(var(--primary-6));
  margin-right: 12px;
}
.arco-upload-list-preview-icon {
  cursor: pointer;
}
.arco-upload-list-error-icon {
  cursor: pointer;
  color: rgb(var(--danger-6));
  font-size: 14px;
  margin-left: 4px;
}
.arco-upload-list-success-icon {
  cursor: pointer;
  color: rgb(var(--success-6));
  font-size: 14px;
  line-height: 14px;
}
.arco-upload-list-remove-icon {
  position: relative;
  cursor: pointer;
  font-size: 14px;
}
.arco-upload-list-start-icon,
.arco-upload-list-cancel-icon {
  position: absolute;
  color: var(--color-white);
  transform: translate(-50%) translateY(-50%);
  top: 50%;
  left: 50%;
  font-size: 12px;
}
.arco-upload-list-start-icon:focus-visible,
.arco-upload-list-cancel-icon:focus-visible {
  color: rgb(var(--primary-6));
}
.arco-upload-list-reupload-icon {
  cursor: pointer;
  color: rgb(var(--primary-6));
  font-size: 14px;
  transition: all 0.2s ease;
}
.arco-upload-list-reupload-icon:active,
.arco-upload-list-reupload-icon:hover {
  color: rgb(var(--primary-7));
}
.arco-upload-list-reupload-icon:focus-visible {
  box-shadow: inset 0 0 0 2px var(--color-primary-light-3);
}
.arco-upload-list-status {
  position: relative;
  cursor: pointer;
  line-height: 12px;
}
.arco-upload-list-status:hover .arco-progress-circle-mask {
  stroke: rgba(var(--gray-10), 0.2);
}
.arco-upload-list-status:hover .arco-progress-circle-path {
  stroke: rgb(var(--primary-7));
}
.arco-upload-list-item-done .arco-upload-list-file-icon {
  color: rgb(var(--success-6));
}
.arco-upload-list-item {
  box-sizing: border-box;
  padding-right: 24px;
  margin-top: 12px;
  position: relative;
}
.arco-upload-list-item-operation {
  font-size: 12px;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-2);
}
.arco-upload-list-item-operation
  .arco-upload-list-remove-icon-hover:focus-visible:before {
  box-shadow: 0 0 0 2px rgb(var(--primary-6));
}
.arco-upload-list-item-operation .arco-upload-list-remove-icon {
  font-size: inherit;
}
.arco-upload-list-item-text {
  font-size: 14px;
  display: flex;
  align-items: center;
  border-radius: var(--border-radius-small);
  width: 100%;
  box-sizing: border-box;
  background-color: var(--color-fill-1);
  padding: 8px 10px 8px 12px;
  flex-wrap: nowrap;
}
.arco-upload-list-item-text-content {
  flex: 1;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  justify-content: space-between;
  flex-wrap: nowrap;
  transition: background-color 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-upload-list-item-text-thumbnail {
  height: 40px;
  width: 40px;
  margin-right: 12px;
  flex-shrink: 0;
}
.arco-upload-list-item-text-thumbnail img {
  width: 100%;
  height: 100%;
}
.arco-upload-list-item-text-name {
  white-space: nowrap;
  display: flex;
  overflow: hidden;
  flex-shrink: 1;
  flex-grow: 1;
  align-items: center;
  color: var(--color-text-1);
  font-size: 14px;
  text-overflow: ellipsis;
  line-height: 1.4286;
  margin-right: 10px;
}
.arco-upload-list-item-text-name-link {
  cursor: pointer;
  text-decoration: none;
  overflow: hidden;
  color: rgb(var(--link-6));
  text-overflow: ellipsis;
}
.arco-upload-list-item-text-name-text {
  overflow: hidden;
  text-overflow: ellipsis;
}
.arco-upload-list-item-error .arco-upload-list-status,
.arco-upload-list-item-done .arco-upload-list-status {
  display: none;
}
.arco-upload-list-type-text
  .arco-upload-list-item-error
  .arco-upload-list-item-text-name-link,
.arco-upload-list-type-text
  .arco-upload-list-item-error
  .arco-upload-list-item-text-name {
  color: rgb(var(--danger-6));
}
.arco-upload-list.arco-upload-list-type-picture-card {
  display: inline;
  vertical-align: top;
}
.arco-upload-list.arco-upload-list-type-picture-card .arco-upload-list-status {
  top: 50%;
  transform: translateY(-50%);
  margin-left: 0;
}
.arco-upload-list-type-picture-card .arco-upload-list-item {
  display: inline-block;
  vertical-align: top;
  margin-top: 0;
  padding-right: 0;
  margin-right: 8px;
  margin-bottom: 8px;
  overflow: hidden;
  transition: all 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-upload-list-type-picture-card
  .arco-upload-list-item-error
  .arco-upload-list-item-picture-mask {
  opacity: 1;
}
.arco-upload-list-item-picture {
  width: 80px;
  height: 80px;
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius-small);
  box-sizing: border-box;
  text-align: center;
  vertical-align: top;
  background-color: var(--color-fill-2);
}
.arco-upload-list-item-picture img {
  width: 100%;
  height: 100%;
}
.arco-upload-list-item-picture-mask {
  cursor: pointer;
  position: absolute;
  text-align: center;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  color: var(--color-white);
  font-size: 16px;
  line-height: 80px;
  opacity: 0;
  transition: opacity 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-upload-list-item-picture-operation {
  font-size: 14px;
}
.arco-upload-list-item-picture-operation .arco-upload-list-reupload-icon {
  color: var(--color-white);
}
.arco-upload-list-item-picture-operation
  .arco-upload-list-reupload-icon
  + .arco-upload-list-remove-icon,
.arco-upload-list-item-picture-operation
  .arco-upload-list-preview-icon
  + .arco-upload-list-remove-icon {
  margin-left: 20px;
}
.arco-upload-list-item-picture-operation
  .arco-upload-list-reupload-icon:focus-visible,
.arco-upload-list-item-picture-operation
  .arco-upload-list-preview-icon:focus-visible,
.arco-upload-list-item-picture-operation
  .arco-upload-list-remove-icon:focus-visible {
  border-radius: 2px;
  box-shadow: 0 0 0 2px var(--color-primary-light-3);
}
.arco-upload-list-item-picture-error-tip
  .arco-upload-list-item-picture-operation {
  opacity: 0;
  width: 0;
  height: 0;
}
.arco-upload-list-item-picture-error-tip .arco-upload-list-error-icon {
  font-size: 26px;
  color: var(--color-white);
}
.arco-upload-list-item-picture-mask:hover,
.arco-upload-list-item-picture-mask:focus-within,
.arco-upload-list-item-picture-mask:hover
  .arco-upload-list-item-picture-operation,
.arco-upload-list-item-picture-mask:focus-within
  .arco-upload-list-item-picture-operation {
  opacity: 1;
  display: block;
}
.arco-upload-list-item-picture-mask:hover
  .arco-upload-list-item-picture-error-tip,
.arco-upload-list-item-picture-mask:focus-within
  .arco-upload-list-item-picture-error-tip {
  display: none;
}
.arco-upload-list-type-picture-list .arco-upload-list-item-text {
  padding-top: 8px;
  padding-bottom: 8px;
}
.arco-upload-list-type-picture-list
  .arco-upload-list-item-error
  .arco-upload-list-item-text {
  background-color: var(--color-danger-light-1);
}
.arco-upload-list-type-picture-list
  .arco-upload-list-item-error
  .arco-upload-list-item-text-name-link,
.arco-upload-list-type-picture-list
  .arco-upload-list-item-error
  .arco-upload-list-item-text-name {
  color: rgb(var(--danger-6));
}
.arco-upload-slide-up-enter {
  opacity: 0;
}
.arco-upload-slide-up-enter-active {
  opacity: 1;
  transition: opacity 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-upload-slide-up-exit {
  opacity: 1;
}
.arco-upload-slide-up-exit-active {
  opacity: 0;
  overflow: hidden;
  margin: 0;
  transition: opacity 0.1s cubic-bezier(0, 0, 1, 1),
    height 0.3s cubic-bezier(0.34, 0.69, 0.1, 1) 0.1s,
    margin 0.3s cubic-bezier(0.34, 0.69, 0.1, 1) 0.1s;
}
.arco-upload-list-item.arco-upload-slide-inline-enter {
  opacity: 0;
}
.arco-upload-list-item.arco-upload-slide-inline-enter-active {
  opacity: 1;
  transition: opacity 0.2s cubic-bezier(0, 0, 1, 1);
}
.arco-upload-list-item.arco-upload-slide-inline-exit {
  opacity: 1;
}
.arco-upload-list-item.arco-upload-slide-inline-exit-active {
  opacity: 0;
  overflow: hidden;
  margin: 0;
  transition: opacity 0.1s cubic-bezier(0, 0, 1, 1),
    width 0.3s cubic-bezier(0.34, 0.69, 0.1, 1) 0.1s,
    margin 0.3s cubic-bezier(0.34, 0.69, 0.1, 1) 0.1s;
}
.arco-upload-rtl,
.arco-upload-list-rtl {
  direction: rtl;
}
.arco-upload-list-rtl .arco-upload-list-file-icon {
  margin-right: 0;
  margin-left: 12px;
}
.arco-upload-list-rtl .arco-upload-list-error-icon {
  margin-left: 0;
  margin-right: 4px;
}
.arco-upload-list-rtl .arco-upload-list-item {
  padding-right: 0;
  padding-left: 24px;
}
.arco-upload-list-rtl .arco-upload-list-item-operation {
  right: initial;
  left: 0;
}
.arco-upload-list-rtl .arco-upload-list-item-text {
  padding-right: 12px;
  padding-left: 10px;
}
.arco-upload-list-rtl .arco-upload-list-item-text-thumbnail {
  margin-right: 0;
  margin-left: 12px;
}
.arco-upload-list-rtl .arco-upload-list-item-text-name {
  margin-right: 0;
  margin-left: 10px;
}
.arco-upload-list-rtl
  .arco-upload-list-item-picture-operation
  .arco-upload-list-reupload-icon
  + .arco-upload-list-remove-icon,
.arco-upload-list-rtl
  .arco-upload-list-item-picture-operation
  .arco-upload-list-preview-icon
  + .arco-upload-list-remove-icon {
  margin-left: 0;
  margin-right: 20px;
}
.arco-upload-list-rtl.arco-upload-list-type-picture-card
  .arco-upload-list-status {
  margin-left: initial;
  margin-right: 0;
}
.arco-upload-list-rtl.arco-upload-list-type-picture-card
  .arco-upload-list-item {
  margin-right: 0;
  padding-left: 0;
  margin-left: 8px;
}
._info-wrapper_1rqux_1 {
  display: flex;
}
._info-avatar_1rqux_4 .arco-avatar-trigger-icon-button {
  color: rgb(var(--arcoblue-6));
  right: 0;
  bottom: 0;
  width: 30px;
  height: 30px;
  font-size: 14px;
  box-sizing: border-box;
  border: 2px solid var(--color-white);
}
._info-content_1rqux_14 {
  flex: 1;
  width: 0;
  margin-left: 60px;
  padding-right: 60px;
}
._verified-tag_1rqux_20 {
  height: 20px;
  line-height: 20px;
  margin-top: -2px;
}
._edit-btn_1rqux_25 {
  margin-left: 12px;
}
