import {
  R as m,
  r as _,
  C as P,
  e as g,
  j as u,
  a as o,
  f as D,
  S as H,
} from './index.7dafa16d.js';
var N =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (N =
          Object.assign ||
          function (e) {
            for (var l, t = 1, a = arguments.length; t < a; t++) {
              l = arguments[t];
              for (var r in l)
                Object.prototype.hasOwnProperty.call(l, r) && (e[r] = l[r]);
            }
            return e;
          }),
        N.apply(this, arguments)
      );
    },
  J =
    (globalThis && globalThis.__rest) ||
    function (e, l) {
      var t = {};
      for (var a in e)
        Object.prototype.hasOwnProperty.call(e, a) &&
          l.indexOf(a) < 0 &&
          (t[a] = e[a]);
      if (e != null && typeof Object.getOwnPropertySymbols == 'function')
        for (var r = 0, a = Object.getOwnPropertySymbols(e); r < a.length; r++)
          l.indexOf(a[r]) < 0 &&
            Object.prototype.propertyIsEnumerable.call(e, a[r]) &&
            (t[a[r]] = e[a[r]]);
      return t;
    };
function K(e, l) {
  var t,
    a = e.className,
    r = e.title,
    c = e.avatar,
    f = e.description,
    d = e.actionList,
    h = J(e, ['className', 'title', 'avatar', 'description', 'actionList']),
    i = _.exports.useContext(P).getPrefixCls,
    s = i('card-meta'),
    p = g(s, a);
  return u('div', {
    ...N({}, h, { ref: l, className: p }),
    children: [
      r || f
        ? u('div', {
            className: s + '-content',
            children: [
              r && o('div', { className: s + '-title', children: r }),
              f && o('div', { className: s + '-description', children: f }),
            ],
          })
        : null,
      c || d
        ? u('div', {
            className: g(
              s + '-footer ',
              ((t = {}), (t[s + '-footer-only-actions'] = !c), t)
            ),
            children: [
              c ? o('div', { className: s + '-avatar', children: c }) : null,
              d,
            ],
          })
        : null,
    ],
  });
}
var R = m.forwardRef(K);
R.displayName = 'CardMeta';
var $ = R;
function Q(e, l) {
  var t,
    a = e.children,
    r = e.style,
    c = e.className,
    f = e.hoverable,
    d = _.exports.useContext(P).getPrefixCls,
    h = d('card-grid');
  return o('div', {
    ref: l,
    style: r,
    className: g(h, ((t = {}), (t[h + '-hoverable'] = f), t), c),
    children: a,
  });
}
var z = m.forwardRef(Q);
z.displayName = 'CardGrid';
var E = z,
  O =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (O =
          Object.assign ||
          function (e) {
            for (var l, t = 1, a = arguments.length; t < a; t++) {
              l = arguments[t];
              for (var r in l)
                Object.prototype.hasOwnProperty.call(l, r) && (e[r] = l[r]);
            }
            return e;
          }),
        O.apply(this, arguments)
      );
    },
  U =
    (globalThis && globalThis.__rest) ||
    function (e, l) {
      var t = {};
      for (var a in e)
        Object.prototype.hasOwnProperty.call(e, a) &&
          l.indexOf(a) < 0 &&
          (t[a] = e[a]);
      if (e != null && typeof Object.getOwnPropertySymbols == 'function')
        for (var r = 0, a = Object.getOwnPropertySymbols(e); r < a.length; r++)
          l.indexOf(a[r]) < 0 &&
            Object.prototype.propertyIsEnumerable.call(e, a[r]) &&
            (t[a[r]] = e[a[r]]);
      return t;
    },
  V = { size: 'default', bordered: !0 };
function W(e, l) {
  var t,
    a,
    r = _.exports.useContext(P),
    c = r.getPrefixCls,
    f = r.loadingElement,
    d = r.componentConfig,
    h = r.rtl,
    i = D(e, V, d == null ? void 0 : d.Card),
    s = i.className,
    p = i.children,
    G = i.bordered,
    j = i.loading,
    L = i.hoverable,
    I = i.size,
    y = i.title,
    C = i.extra,
    w = i.cover,
    x = i.actions,
    F = i.headerStyle,
    k = i.bodyStyle,
    q = U(i, [
      'className',
      'children',
      'bordered',
      'loading',
      'hoverable',
      'size',
      'title',
      'extra',
      'cover',
      'actions',
      'headerStyle',
      'bodyStyle',
    ]),
    n = c('card'),
    S =
      x && x.length
        ? o('div', {
            className: n + '-actions',
            children: o('div', {
              className: n + '-actions-right',
              children: x.map(function (v, B) {
                return o(
                  'span',
                  { className: n + '-actions-item', children: v },
                  'action-' + B
                );
              }),
            }),
          })
        : null,
    T = !1,
    M = !1,
    A = m.Children.map(p, function (v) {
      if (v && v.type) {
        if (v.type === E) T = !0;
        else if (v.type === $)
          return (M = !0), m.cloneElement(v, { actionList: S });
      }
      return v;
    });
  return u('div', {
    ...O({}, q, {
      ref: l,
      className: g(
        n,
        n + '-size-' + I,
        ((t = {}),
        (t[n + '-loading'] = j),
        (t[n + '-bordered'] = G),
        (t[n + '-hoverable'] = L),
        (t[n + '-contain-grid'] = T),
        (t[n + '-rtl'] = h),
        t),
        s
      ),
    }),
    children: [
      y || C
        ? u('div', {
            className: g(
              n + '-header',
              ((a = {}), (a[n + '-header-no-title'] = !y), a)
            ),
            style: F,
            children: [
              y && o('div', { className: n + '-header-title', children: y }),
              C && o('div', { className: n + '-header-extra', children: C }),
            ],
          })
        : null,
      w ? o('div', { className: n + '-cover', children: w }) : null,
      u('div', {
        className: n + '-body',
        style: k,
        children: [j ? f || o(H, {}) : A, M ? null : S],
      }),
    ],
  });
}
var X = m.forwardRef(W),
  b = X;
b.Meta = $;
b.Grid = E;
b.displayName = 'Card';
var Z = b;
export { Z as C };
