import {
  r as x,
  C as Je,
  f as Ir,
  o as at,
  e as ae,
  a as c,
  p as Cn,
  q as $t,
  s as pe,
  t as re,
  R as ke,
  v as Tt,
  j as ve,
  w as Dt,
  F as tr,
  x as wn,
  y as Sn,
  g as _n,
  _ as Nn,
  z as Tn,
  T as Rn,
  D as On,
  E as kn,
  G as Pn,
  H as Hr,
  J as ot,
  h as En,
  B as Rt,
  K as An,
  N as Fn,
  O as rr,
  P as $r,
  Q as In,
  U as $n,
  V as Dn,
  W as jn,
  X as Xr,
  Y as Hn,
  Z as Kn,
  $ as Or,
  a0 as kr,
  a1 as zn,
  a2 as Ot,
  S as Bn,
  a3 as Ln,
} from './index.7dafa16d.js';
import { T as Mn } from './b-tween.es.d368a2a1.js';
var Jr =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (Jr =
          Object.assign ||
          function (e) {
            for (var r, n = 1, t = arguments.length; n < t; n++) {
              r = arguments[n];
              for (var a in r)
                Object.prototype.hasOwnProperty.call(r, a) && (e[a] = r[a]);
            }
            return e;
          }),
        Jr.apply(this, arguments)
      );
    },
  Wn =
    (globalThis && globalThis.__read) ||
    function (e, r) {
      var n = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!n) return e;
      var t = n.call(e),
        a,
        o = [],
        l;
      try {
        for (; (r === void 0 || r-- > 0) && !(a = t.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (n = t.return) && n.call(t);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    },
  Vn = { type: 'radio' },
  Yn = { type: 'radio', mode: 'outline', direction: 'horizontal' },
  it = x.exports.createContext(Vn);
function jt(e) {
  var r,
    n = x.exports.useContext(Je),
    t = n.getPrefixCls,
    a = n.size,
    o = n.componentConfig,
    l = n.rtl,
    i = Ir(e, Yn, o == null ? void 0 : o['Radio.Group']),
    v = i.style,
    u = i.className,
    p = i.name,
    b = i.children,
    m = i.direction,
    S = i.type,
    d = i.mode,
    _ = i.options,
    P = i.disabled,
    w = Wn(at(void 0, { defaultValue: i.defaultValue, value: i.value }), 2),
    C = w[0],
    h = w[1],
    j = i.size || a,
    R = t('radio'),
    $ = ae(
      R + '-group',
      ((r = {}),
      (r[R + '-group-type-button'] = S !== 'radio'),
      (r[R + '-size-' + j] = !!j),
      (r[R + '-mode-' + d] = !!d),
      (r[R + '-group-disabled'] = P),
      (r[R + '-group-direction-vertical'] = m === 'vertical'),
      (r[R + '-group-rtl'] = l),
      r),
      u
    ),
    W = function (g, Z) {
      var O = i.onChange;
      g !== C && ('value' in i || h(g), O && O(g, Z));
    },
    A = {
      onChangeValue: W,
      type: S,
      value: C,
      disabled: P,
      group: !0,
      name: p,
    };
  return c(it.Provider, {
    value: A,
    children: c('div', {
      ...Jr({ className: $, role: 'radiogroup', style: v }, Cn(i), $t(i)),
      children:
        _ && pe(_)
          ? _.map(function (g, Z) {
              return re(g)
                ? c(
                    Xe,
                    {
                      disabled: P || g.disabled,
                      value: g.value,
                      children: g.label,
                    },
                    g.value
                  )
                : c(Xe, { value: g, disabled: P, children: g }, Z);
            })
          : b,
    }),
  });
}
jt.displayName = 'RadioGroup';
var yr =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (yr =
          Object.assign ||
          function (e) {
            for (var r, n = 1, t = arguments.length; n < t; n++) {
              r = arguments[n];
              for (var a in r)
                Object.prototype.hasOwnProperty.call(r, a) && (e[a] = r[a]);
            }
            return e;
          }),
        yr.apply(this, arguments)
      );
    },
  Gn =
    (globalThis && globalThis.__rest) ||
    function (e, r) {
      var n = {};
      for (var t in e)
        Object.prototype.hasOwnProperty.call(e, t) &&
          r.indexOf(t) < 0 &&
          (n[t] = e[t]);
      if (e != null && typeof Object.getOwnPropertySymbols == 'function')
        for (var a = 0, t = Object.getOwnPropertySymbols(e); a < t.length; a++)
          r.indexOf(t[a]) < 0 &&
            Object.prototype.propertyIsEnumerable.call(e, t[a]) &&
            (n[t[a]] = e[t[a]]);
      return n;
    },
  Un =
    (globalThis && globalThis.__read) ||
    function (e, r) {
      var n = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!n) return e;
      var t = n.call(e),
        a,
        o = [],
        l;
      try {
        for (; (r === void 0 || r-- > 0) && !(a = t.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (n = t.return) && n.call(t);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    };
function Xe(e) {
  var r,
    n = x.exports.useRef(null),
    t = x.exports.useContext(Je),
    a = t.getPrefixCls,
    o = t.componentConfig,
    l = t.rtl,
    i = Ir(e, {}, o == null ? void 0 : o.Radio),
    v = x.exports.useContext(it),
    u = a('radio'),
    p = yr({}, i);
  v.group &&
    ((p.checked = v.value === i.value),
    (p.disabled = 'disabled' in i ? i.disabled : v.disabled));
  var b = p.disabled,
    m = p.children,
    S = p.value,
    d = p.style,
    _ = p.className,
    P = Gn(p, ['disabled', 'children', 'value', 'style', 'className']),
    w = Un(at(!1, { value: p.checked, defaultValue: p.defaultChecked }), 2),
    C = w[0],
    h = w[1],
    j = ae(
      '' + u + (v.type === 'button' ? '-button' : ''),
      ((r = {}),
      (r[u + '-checked'] = C),
      (r[u + '-disabled'] = b),
      (r[u + '-rtl'] = l),
      r),
      _
    ),
    R = function (W) {
      var A = p.onChange,
        g = p.value;
      b ||
        (v.group
          ? v.onChangeValue && v.onChangeValue(g, W)
          : !('checked' in i) && !C && h(!0),
        !C && A && A(!0, W));
    },
    $ = ke.useCallback(
      function (W) {
        Tt(i.children) && (W.preventDefault(), n.current && n.current.click()),
          P.onClick && P.onClick(W);
      },
      [i.children, P.onClick]
    );
  return ve('label', {
    ...yr({}, Dt(P, ['checked', 'onChange']), {
      onClick: $,
      style: d,
      className: j,
    }),
    children: [
      c('input', {
        ...yr(
          { ref: n, disabled: b, value: S || '', type: 'radio' },
          v.name ? { name: v.name } : {},
          {
            checked: C,
            onChange: function (W) {
              W.persist(), R(W);
            },
            onClick: function (W) {
              W.stopPropagation();
            },
          }
        ),
      }),
      Tt(m)
        ? m({ checked: C })
        : v.type === 'radio'
        ? ve(tr, {
            children: [
              c(wn, {
                prefix: u,
                className: u + '-mask-wrapper',
                disabled: C || b,
                children: c('div', { className: u + '-mask' }),
              }),
              !Sn(m) && c('span', { className: u + '-text', children: m }),
            ],
          })
        : v.type === 'button' &&
          c('span', { className: u + '-button-inner', children: m }),
    ],
  });
}
Xe.__BYTE_RADIO = !0;
Xe.displayName = 'Radio';
Xe.Group = jt;
Xe.GroupContext = it;
function kt(e, r) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var t = Object.getOwnPropertySymbols(e);
    r &&
      (t = t.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      n.push.apply(n, t);
  }
  return n;
}
function Pt(e) {
  for (var r = 1; r < arguments.length; r++) {
    var n = arguments[r] != null ? arguments[r] : {};
    r % 2
      ? kt(Object(n), !0).forEach(function (t) {
          Nn(e, t, n[t]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n))
      : kt(Object(n)).forEach(function (t) {
          Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
        });
  }
  return e;
}
function qn(e, r) {
  var n = x.exports.useContext(_n),
    t = n.prefixCls,
    a = t === void 0 ? 'arco' : t,
    o = e.spin,
    l = e.className,
    i = Pt(
      Pt({ 'aria-hidden': !0, focusable: !1, ref: r }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-filter'),
      }
    );
  return (
    o && (i.className = ''.concat(i.className, ' ').concat(a, '-icon-loading')),
    delete i.spin,
    delete i.isIcon,
    c('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...i,
      children: c('path', {
        d: 'M30 42V22.549a1 1 0 0 1 .463-.844l10.074-6.41A1 1 0 0 0 41 14.45V8a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v6.451a1 1 0 0 0 .463.844l10.074 6.41a1 1 0 0 1 .463.844V37',
      }),
    })
  );
}
var lt = ke.forwardRef(qn);
lt.defaultProps = { isIcon: !0 };
lt.displayName = 'IconFilter';
var Xn = lt,
  Et = {
    table: 'table',
    header: {
      operations: function (e) {
        var r = e.selectionNode,
          n = e.expandNode;
        return [
          { name: 'expandNode', node: n },
          { name: 'selectionNode', node: r },
        ];
      },
      wrapper: 'div',
      thead: 'thead',
      row: 'tr',
      th: 'th',
      cell: 'div',
    },
    body: {
      operations: function (e) {
        var r = e.selectionNode,
          n = e.expandNode;
        return [
          { name: 'expandNode', node: n },
          { name: 'selectionNode', node: r },
        ];
      },
      wrapper: 'div',
      tbody: 'tbody',
      row: 'tr',
      td: 'td',
      cell: 'span',
    },
  };
function nr(e) {
  var r = x.exports.useMemo(
    function () {
      return re(e) ? Tn({}, Et, e) : Et;
    },
    [e]
  );
  return {
    getHeaderComponentOperations: r.header.operations,
    getBodyComponentOperations: r.body.operations,
    ComponentTable: r.table,
    ComponentHeaderWrapper: r.header.wrapper,
    ComponentThead: r.header.thead,
    ComponentHeaderRow: r.header.row,
    ComponentTh: r.header.th,
    ComponentHeaderCell: r.header.cell,
    ComponentBodyWrapper: r.body.wrapper,
    ComponentTbody: r.body.tbody,
    ComponentBodyRow: r.body.row,
    ComponentTd: r.body.td,
    ComponentBodyCell: r.body.cell,
  };
}
var Oe =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (Oe =
          Object.assign ||
          function (e) {
            for (var r, n = 1, t = arguments.length; n < t; n++) {
              r = arguments[n];
              for (var a in r)
                Object.prototype.hasOwnProperty.call(r, a) && (e[a] = r[a]);
            }
            return e;
          }),
        Oe.apply(this, arguments)
      );
    },
  Pr =
    (globalThis && globalThis.__read) ||
    function (e, r) {
      var n = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!n) return e;
      var t = n.call(e),
        a,
        o = [],
        l;
      try {
        for (; (r === void 0 || r-- > 0) && !(a = t.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (n = t.return) && n.call(t);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    },
  Jn =
    (globalThis && globalThis.__spreadArray) ||
    function (e, r, n) {
      if (n || arguments.length === 2)
        for (var t = 0, a = r.length, o; t < a; t++)
          (o || !(t in r)) &&
            (o || (o = Array.prototype.slice.call(r, 0, t)), (o[t] = r[t]));
      return e.concat(o || Array.prototype.slice.call(r));
    };
function Qn(e, r) {
  return e === 'ascend'
    ? r.Table.sortAscend
    : e === 'descend'
    ? r.Table.sortDescend
    : r.Table.cancelSort;
}
var Zn = { bottom: 0 };
function ea(e) {
  var r,
    n,
    t,
    a,
    o = e.onSort,
    l = e.onFilter,
    i = e.onHandleFilter,
    v = e.onHandleFilterReset,
    u = e.currentFilters,
    p = u === void 0 ? {} : u,
    b = e.currentSorter,
    m = e._key,
    S = e.dataIndex,
    d = e.title,
    _ = e.sorter,
    P = e.sortDirections,
    w = P === void 0 ? ['ascend', 'descend'] : P,
    C = e.filters,
    h = C === void 0 ? [] : C,
    j = e.columnFixedStyle,
    R = e.className,
    $ = e.cellStyle,
    W = e.headerCellStyle,
    A = e.rowSpan,
    g = e.colSpan,
    Z = e.headerCellProps,
    O = e.prefixCls,
    V = e.align,
    E = V === void 0 ? 'left' : V,
    H = e.components,
    L = e.filterIcon,
    F = e.filterDropdown,
    K = e.filterMultiple,
    z = K === void 0 ? !0 : K,
    T = e.ellipsis,
    D = e.filterDropdownProps,
    Y = e.onFilterDropdownVisibleChange,
    Q = e.column,
    B = e.showSorterTooltip,
    I = e.index,
    te = x.exports.useContext(Je),
    ue = te.locale,
    k = te.rtl,
    se = m || S || I,
    he = Pr(at([], { value: p[se] || [] }), 3),
    ne = he[0],
    me = he[1],
    ee = he[2],
    Ce = Pr(x.exports.useState(!1), 2),
    we = Ce[0],
    Ne = Ce[1],
    ye = Pr(x.exports.useState(!1), 2),
    Ae = ye[0],
    Pe = ye[1],
    Se = _ && pe(w) && w.length,
    X = Se ? ar() : void 0;
  x.exports.useEffect(
    function () {
      me(p[se] || []);
    },
    [p, se]
  ),
    x.exports.useEffect(
      function () {
        ne && ne !== ee && me(ne);
      },
      [we]
    );
  function ar() {
    var G = b && b.direction;
    if (!G || (b && b.field !== se)) return w[0];
    var de = w.indexOf(G);
    if (de < w.length) return w[de + 1];
  }
  function ur() {
    !ne || (i && i({ onFilter: l, filters: h, dataIndex: se }, ee), ze(!1));
  }
  function Ye() {
    v({ dataIndex: se }), ze(!1);
  }
  function ze(G) {
    Ne(G), Y && Y(G);
  }
  function Fe(G, de) {
    var oe = Jn([], Pr(ee), !1);
    if (z)
      de
        ? (oe = oe.concat(G))
        : oe.splice(
            oe.findIndex(function (je) {
              return je === G;
            }),
            1
          );
    else if (oe.length > 0)
      if (oe[0] !== G) oe = [G];
      else return;
    else oe = [G];
    me(oe);
  }
  function Ge(G) {
    me(G || ee),
      Ne(!1),
      i && i({ filters: h, onFilter: l, dataIndex: se }, G || ee);
  }
  function or() {
    return typeof F == 'function'
      ? F({
          filterKeys: ee,
          setFilterKeys: function (G, de) {
            me(G), de == null || de();
          },
          confirm: Ge,
        })
      : ve('div', {
          className: O + '-filters-popup',
          children: [
            c('div', {
              className: O + '-filters-list',
              children: h.map(function (G) {
                var de =
                  ee.findIndex(function (oe) {
                    return oe === G.value;
                  }) !== -1;
                return c(
                  'div',
                  {
                    className: O + '-filters-item',
                    children: z
                      ? c(ot, {
                          checked: de,
                          onChange: function (oe) {
                            return Fe(G.value, oe);
                          },
                          children: G.text,
                        })
                      : c(Xe, {
                          checked: de,
                          onChange: function (oe) {
                            return Fe(G.value, oe);
                          },
                          children: G.text,
                        }),
                  },
                  G.value
                );
              }),
            }),
            ve(En, {
              className: O + '-filters-btn',
              children: [
                c(Rt, {
                  onClick: Ye,
                  size: 'mini',
                  children: ue.Table.resetText,
                }),
                c(Rt, {
                  onClick: ur,
                  type: 'primary',
                  size: 'mini',
                  children: ue.Table.okText,
                }),
              ],
            }),
          ],
        });
  }
  var Be = function (G) {
      var de;
      return ae(
        O + '-sorter-icon',
        ((de = {}),
        (de[O + '-sorter-icon-active'] =
          b && b.direction === G && b.field === se),
        de)
      );
    },
    Qe = ae(
      O + '-filters',
      ((r = {}),
      (r[O + '-filters-open'] = we),
      (r[O + '-filters-active'] = ne && ne.length),
      r)
    ),
    Te = Oe({}, j);
  re($) && (Te = Oe(Oe({}, Te), $)),
    re(W) && (Te = Oe(Oe({}, Te), W)),
    E && E !== 'left' && (Te.textAlign = E);
  var Le = { style: Te, key: m || se };
  g && g > 1 && (Le.colSpan = g), A && A > 1 && (Le.rowSpan = A);
  var Ue = nr(H),
    J = Ue.ComponentTh,
    fe = Ue.ComponentHeaderCell,
    Ee = (pe(h) && h.length > 0) || typeof F == 'function',
    Re = T && typeof d == 'string' ? { title: d } : {},
    Ie = D && D.triggerProps,
    $e = ve(tr, {
      children: [
        Se
          ? c(Rn, {
              ...Oe({ content: Qn(X, ue), disabled: !B }, re(B) ? B : {}),
              children: ve('div', {
                className: O + '-cell-with-sorter',
                onMouseEnter: function () {
                  Pe(!0);
                },
                onMouseLeave: function () {
                  Pe(!1);
                },
                onClick: function () {
                  return o(X, se);
                },
                children: [
                  c('span', {
                    ...Oe({ className: O + '-th-item-title' }, Re),
                    children: d,
                  }),
                  Se &&
                    ve('div', {
                      className: ae(
                        O + '-sorter',
                        ((n = {}),
                        (n[O + '-sorter-direction-one'] = w.length === 1),
                        n)
                      ),
                      children: [
                        w.indexOf('ascend') !== -1 &&
                          c('div', {
                            className: Be('ascend'),
                            children: c(On, {}),
                          }),
                        w.indexOf('descend') !== -1 &&
                          c('div', {
                            className: Be('descend'),
                            children: c(kn, {}),
                          }),
                      ],
                    }),
                ],
              }),
            })
          : c('span', {
              ...Oe({ className: O + '-th-item-title' }, Re),
              children: d,
            }),
        Ee &&
          c(Pn, {
            ...Oe(
              {
                popup: or,
                trigger: 'click',
                classNames: 'slideDynamicOrigin',
                position: k ? 'bl' : 'br',
                popupAlign: Zn,
                popupVisible: we,
                onVisibleChange: ze,
              },
              Ie
            ),
            children: c('div', { className: Qe, children: L || c(Xn, {}) }),
          }),
      ],
    }),
    De = ae(
      O + '-th-item',
      ((t = {}),
      (t[O + '-cell-text-ellipsis'] = T),
      (t[O + '-cell-mouseenter'] = Ae),
      (t[O + '-cell-next-' + X] = Ae && X),
      (t[O + '-col-has-sorter'] = Se),
      (t[O + '-col-has-filter'] = Ee),
      t)
    );
  return (
    g !== 0 &&
    c(J, {
      ...Oe(
        {
          className: ae(
            O + '-th',
            ((a = {}),
            (a[O + '-col-sorted'] = b && b.direction && b.field === se),
            a),
            R
          ),
        },
        Le,
        Z
      ),
      children: Hr(fe)
        ? c(fe, { className: De, children: $e })
        : c(fe, { className: De, column: Q, children: $e }),
    })
  );
}
var dr = 'table_internal_selection_key',
  cr = 'table_internal_expand_key',
  He =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (He =
          Object.assign ||
          function (e) {
            for (var r, n = 1, t = arguments.length; n < t; n++) {
              r = arguments[n];
              for (var a in r)
                Object.prototype.hasOwnProperty.call(r, a) && (e[a] = r[a]);
            }
            return e;
          }),
        He.apply(this, arguments)
      );
    };
function ra(e) {
  var r = e.activeSorters,
    n = e.expandedRowRender,
    t = e.expandProps,
    a = t === void 0 ? {} : t,
    o = e.onSort,
    l = e.onHandleFilter,
    i = e.onHandleFilterReset,
    v = e.onHeaderRow,
    u = e.prefixCls,
    p = e.currentFilters,
    b = e.components,
    m = e.data,
    S = e.selectedRowKeys,
    d = e.rowSelection,
    _ = e.allSelectedRowKeys,
    P = _ === void 0 ? [] : _,
    w = e.groupColumns,
    C = e.stickyOffsets,
    h = e.groupStickyClassNames,
    j = e.showSorterTooltip,
    R = x.exports.useContext(Je).rtl,
    $ = nr(b),
    W = $.ComponentThead,
    A = $.ComponentHeaderRow,
    g = $.getHeaderComponentOperations,
    Z = d && (d.type === 'checkbox' || !('type' in d)),
    O = d && 'checkAll' in d ? d.checkAll : !0,
    V = d && d.type === 'radio',
    E = a.columnTitle,
    H = x.exports.useMemo(
      function () {
        var K = new Set(P);
        return S.filter(function (z) {
          return K.has(z);
        });
      },
      [S, P]
    ),
    L = w.length > 1 ? { rowSpan: w.length } : {},
    F = ae(u + '-th', u + '-operation');
  return c(W, {
    children: w.map(function (K, z) {
      var T = v && v(K, z),
        D =
          (Z || V) &&
          z === 0 &&
          c('th', {
            className: ae(F, u + '-' + (V ? 'radio' : 'checkbox')),
            children: ve('div', {
              className: u + '-th-item',
              children: [
                O && !V
                  ? c(ot, {
                      indeterminate: m && H.length > 0 && H.length !== P.length,
                      checked: m && H.length !== 0 && H.length === P.length,
                      disabled: !P.length,
                      onChange: e.onCheckAll,
                    })
                  : null,
                d && d.columnTitle,
              ],
            }),
          }),
        Y =
          n &&
          c('th', {
            className: ae(F, u + '-expand'),
            children: E && c('div', { className: u + '-th-item', children: E }),
          }),
        Q = h[z],
        B = g({ selectionNode: D, expandNode: Y });
      return c(A, {
        ...He({}, T, { key: z, className: u + '-tr' }),
        children: K.map(function (I, te) {
          var ue,
            k,
            se,
            he,
            ne,
            me = I.$$columnIndex,
            ee = 0;
          Array.isArray(me) && me.length === 2
            ? (ee = I.fixed === 'right' ? C[me[1]] : C[me[0]])
            : typeof me == 'number' && (ee = C[me] || 0);
          var Ce = Q[te];
          if (I.$$isOperation) {
            var we = I.node,
              Ne = !0;
            I.title === dr &&
              ((we =
                (k = B.find(function (X) {
                  return X.name === 'selectionNode';
                })) === null || k === void 0
                  ? void 0
                  : k.node),
              (Ne = !1)),
              I.title === cr &&
                ((we =
                  (se = B.find(function (X) {
                    return X.name === 'expandNode';
                  })) === null || se === void 0
                    ? void 0
                    : se.node),
                (Ne = !1));
            var ye = we;
            return ke.cloneElement(
              ye,
              He(He(He({ key: I.key || te }, ye.props), L), {
                className: ae(
                  Ne ? F : '',
                  (he = ye == null ? void 0 : ye.props) === null ||
                    he === void 0
                    ? void 0
                    : he.className,
                  Ce
                ),
                style: He(
                  He(
                    He(
                      {},
                      (ne = ye == null ? void 0 : ye.props) === null ||
                        ne === void 0
                        ? void 0
                        : ne.style
                    ),
                    I.fixed === 'left'
                      ? ((ue = {}), (ue[R ? 'right' : 'left'] = ee), ue)
                      : {}
                  ),
                  { width: I.width, minWidth: I.width }
                ),
              })
            );
          }
          var Ae = I.onHeaderCell && I.onHeaderCell(I, te),
            Pe = ae(Ce, I.className),
            Se = {};
          return (
            I.fixed === 'left' && (Se[R ? 'right' : 'left'] = ee),
            I.fixed === 'right' && (Se[R ? 'left' : 'right'] = ee),
            c(ea, {
              ...He(
                {
                  key: I.key,
                  index: te,
                  onSort: o,
                  onHandleFilter: l,
                  onHandleFilterReset: i,
                  currentSorter: r.find(function (X) {
                    return X.field === I.key;
                  }),
                  currentFilters: p,
                  _key: I.key || I.dataIndex || te,
                },
                I,
                {
                  column: I,
                  headerCellProps: Ae,
                  prefixCls: u,
                  components: b,
                  className: Pe,
                  columnFixedStyle: Se,
                  showSorterTooltip: j,
                }
              ),
            })
          );
        }),
      });
    }),
  });
}
var Qr =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (Qr =
          Object.assign ||
          function (e) {
            for (var r, n = 1, t = arguments.length; n < t; n++) {
              r = arguments[n];
              for (var a in r)
                Object.prototype.hasOwnProperty.call(r, a) && (e[a] = r[a]);
            }
            return e;
          }),
        Qr.apply(this, arguments)
      );
    },
  Dr =
    (globalThis && globalThis.__read) ||
    function (e, r) {
      var n = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!n) return e;
      var t = n.call(e),
        a,
        o = [],
        l;
      try {
        for (; (r === void 0 || r-- > 0) && !(a = t.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (n = t.return) && n.call(t);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    },
  jr =
    (globalThis && globalThis.__spreadArray) ||
    function (e, r, n) {
      if (n || arguments.length === 2)
        for (var t = 0, a = r.length, o; t < a; t++)
          (o || !(t in r)) &&
            (o || (o = Array.prototype.slice.call(r, 0, t)), (o[t] = r[t]));
      return e.concat(o || Array.prototype.slice.call(r));
    };
function hr(e) {
  return rr(e) ? e : Hr(e) && e.includes('px') ? +e.replace('px', '') : e;
}
function ta(e) {
  return e ? e.offsetHeight - e.clientHeight : 0;
}
function na(e) {
  return e ? e.offsetWidth - e.clientWidth : 0;
}
function Zr(e, r) {
  return pe(e[r]) && e[r].length;
}
function aa(e, r) {
  function n(t) {
    if (!t) return [];
    var a = [];
    return (
      t.forEach(function (o) {
        if (!re(o)) a.push(o);
        else {
          var l = Qr({}, o);
          l.__ORIGIN_DATA = o;
          var i = l[r];
          re(l) && i && pe(i) && (l[r] = n(i)), a.push(l);
        }
      }),
      a
    );
  }
  return n(e);
}
function le(e) {
  return re(e)
    ? e.__ORIGIN_DATA
    : !e || !pe(e)
    ? e
    : e.map(function (r) {
        return !re(r) || !('__ORIGIN_DATA' in r) ? r : r.__ORIGIN_DATA;
      });
}
function oa(e, r, n, t, a, o, l) {
  n === void 0 && (n = []), t === void 0 && (t = []);
  var i = new Set(n),
    v = new Set(t);
  function u(p) {
    r ? (i.add(a(p)), v.delete(a(p))) : i.delete(a(p)),
      pe(p[o]) &&
        p[o].forEach(function (b) {
          u(b);
        });
  }
  return (
    l ? (u(e), st(e, i, v, a, o)) : r ? i.add(a(e)) : i.delete(a(e)),
    { selectedRowKeys: jr([], Dr(i), !1), indeterminateKeys: jr([], Dr(v), !1) }
  );
}
function ia(e, r, n, t, a) {
  if ((r === void 0 && (r = []), !a))
    return { selectedRowKeys: r, indeterminateKeys: [] };
  var o = new Set(r),
    l = new Set([]);
  function i(v) {
    o.add(n(v)),
      l.delete(n(v)),
      pe(v[t]) &&
        v[t].forEach(function (u) {
          i(u);
        });
  }
  return (
    r.forEach(function (v) {
      var u = e.find(function (p) {
        return n(p) === v;
      });
      !An(u) && !Fn(u) && (i(u), st(u, o, l, n, t));
    }),
    { selectedRowKeys: jr([], Dr(o), !1), indeterminateKeys: jr([], Dr(l), !1) }
  );
}
function st(e, r, n, t, a) {
  if (e.__INTERNAL_PARENT) {
    var o = t(e.__INTERNAL_PARENT);
    if (pe(e.__INTERNAL_PARENT[a])) {
      var l = e.__INTERNAL_PARENT[a].length,
        i = 0,
        v = !1;
      e.__INTERNAL_PARENT[a].forEach(function (u) {
        r.has(t(u)) && (i += 1), n.has(t(u)) && (n.add(o), (v = !0));
      }),
        l === i
          ? (r.add(o), n.delete(o))
          : i > 0 && l > i
          ? (r.delete(o), n.add(o))
          : i === 0 && (r.delete(o), v || n.delete(o));
    }
    st(e.__INTERNAL_PARENT, r, n, t, a);
  }
}
function et(e) {
  return typeof e == 'function'
    ? e
    : typeof e == 'object' && typeof e.compare == 'function'
    ? e.compare
    : null;
}
function rt(e) {
  if (typeof e == 'object' && typeof e.multiple == 'number') return e.multiple;
}
var Ve =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (Ve =
          Object.assign ||
          function (e) {
            for (var r, n = 1, t = arguments.length; n < t; n++) {
              r = arguments[n];
              for (var a in r)
                Object.prototype.hasOwnProperty.call(r, a) && (e[a] = r[a]);
            }
            return e;
          }),
        Ve.apply(this, arguments)
      );
    },
  la =
    (globalThis && globalThis.__rest) ||
    function (e, r) {
      var n = {};
      for (var t in e)
        Object.prototype.hasOwnProperty.call(e, t) &&
          r.indexOf(t) < 0 &&
          (n[t] = e[t]);
      if (e != null && typeof Object.getOwnPropertySymbols == 'function')
        for (var a = 0, t = Object.getOwnPropertySymbols(e); a < t.length; a++)
          r.indexOf(t[a]) < 0 &&
            Object.prototype.propertyIsEnumerable.call(e, t[a]) &&
            (n[t[a]] = e[t[a]]);
      return n;
    };
function sa(e) {
  return e && !ke.isValidElement(e) && re(e);
}
function da(e) {
  var r,
    n,
    t = e.components,
    a = e.InnerComponentTd,
    o = e.column,
    l = e.columnIndex,
    i = e.prefixCls,
    v = e.stickyClassName,
    u = e.stickyOffset,
    p = e.currentSorter,
    b = e.virtualized,
    m = e.record,
    S = e.trIndex,
    d = e.level,
    _ = e.placeholder,
    P = e.indentSize,
    w = e.renderExpandIcon,
    C = e.rowKey,
    h = e.recordHaveChildren,
    j = e.haveTreeData,
    R = x.exports.useContext(Je).rtl,
    $ = nr(t).ComponentBodyCell,
    W = ae(
      i + '-td',
      v,
      ((r = {}),
      (r[i + '-col-sorted'] = p && p.direction && p.field === o.dataIndex),
      r),
      o.className
    ),
    A = {},
    g,
    Z,
    O = {};
  o.fixed === 'left' && (O[R ? 'right' : 'left'] = u),
    o.fixed === 'right' && (O[R ? 'left' : 'right'] = u),
    re(o.cellStyle) && (O = Ve(Ve({}, O), o.cellStyle)),
    re(o.bodyCellStyle) && (O = Ve(Ve({}, O), o.bodyCellStyle)),
    o.align && (O.textAlign = o.align),
    b &&
      o.width &&
      ((O.width = o.width), (O.minWidth = o.width), (O.maxWidth = o.width));
  var V = o.onCell ? o.onCell(m, S) : { onHandleSave: function () {} },
    E = V.onHandleSave,
    H = la(V, ['onHandleSave']),
    L = x.exports.useMemo(
      function () {
        return o.render && o.render($r(m, o.dataIndex), le(m), S);
      },
      [m, o, S]
    );
  if (
    (sa(L) &&
      ((A = L.props), (g = A.rowSpan), (Z = A.colSpan), (L = L.children)),
    g === 0 || Z === 0)
  )
    return null;
  var F = $r(m, o.dataIndex),
    K = o.render
      ? L
      : F === void 0 || (typeof F == 'string' && F.trim() === '') || F === null
      ? o.placeholder === void 0
        ? _
        : o.placeholder
      : F,
    z = o.ellipsis && typeof K == 'string' ? { title: K } : {},
    T = j && o.$$isFirstColumn,
    D = T && h,
    Y = T && d > 0 ? P * d : 0;
  T && !h && (Y += 16 + 4);
  var Q = ve(tr, {
    children: [
      D
        ? c('span', { className: i + '-cell-expand-icon', children: w(m, C) })
        : null,
      Hr($)
        ? c($, { className: i + '-cell-wrap-value', children: K })
        : c($, {
            ...Ve(
              {
                rowData: le(m),
                className: i + '-cell-wrap-value',
                column: o,
                onHandleSave: E,
              },
              H
            ),
            children: K,
          }),
    ],
  });
  return c(a, {
    ...Ve(
      { className: W, key: o.key || o.dataIndex || l, style: O },
      In(H, [
        'onClick',
        'onDoubleClick',
        'onContextMenu',
        'onMouseOver',
        'onMouseEnter',
        'onMouseLeave',
        'onMouseMove',
        'onMouseDown',
        'onMouseUp',
      ]),
      A
    ),
    children: ve('div', {
      ...Ve(
        {
          className: ae(
            i + '-cell',
            ((n = {}), (n[i + '-cell-text-ellipsis'] = o.ellipsis), n)
          ),
        },
        z
      ),
      children: [
        Y
          ? c('span', {
              className: i + '-cell-indent',
              style: { paddingLeft: Y },
            })
          : null,
        Q,
      ],
    }),
  });
}
var ca = x.exports.memo(da),
  be =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (be =
          Object.assign ||
          function (e) {
            for (var r, n = 1, t = arguments.length; n < t; n++) {
              r = arguments[n];
              for (var a in r)
                Object.prototype.hasOwnProperty.call(r, a) && (e[a] = r[a]);
            }
            return e;
          }),
        be.apply(this, arguments)
      );
    },
  ua =
    (globalThis && globalThis.__rest) ||
    function (e, r) {
      var n = {};
      for (var t in e)
        Object.prototype.hasOwnProperty.call(e, t) &&
          r.indexOf(t) < 0 &&
          (n[t] = e[t]);
      if (e != null && typeof Object.getOwnPropertySymbols == 'function')
        for (var a = 0, t = Object.getOwnPropertySymbols(e); a < t.length; a++)
          r.indexOf(t[a]) < 0 &&
            Object.prototype.propertyIsEnumerable.call(e, t[a]) &&
            (n[t[a]] = e[t[a]]);
      return n;
    };
function fa(e, r) {
  var n,
    t = e.expandedRowRender,
    a = e.onClickExpandBtn,
    o = e.columns,
    l = e.components,
    i = e.onCheck,
    v = e.onCheckRadio,
    u = e.prefixCls,
    p = e.selectedRowKeys,
    b = e.indeterminateKeys,
    m = e.rowClassName,
    S = e.onRow,
    d = e.rowSelection,
    _ = e.indentSize,
    P = _ === void 0 ? 16 : _,
    w = e.activeSorters,
    C = e.virtualized,
    h = e.stickyOffsets,
    j = e.stickyClassNames,
    R = e.getRowKey,
    $ = e.placeholder,
    W = e.expandProps,
    A = W === void 0 ? { strictTreeData: !0 } : W,
    g = e.data,
    Z = e.expandedRowKeys,
    O = e.childrenColumnName,
    V = e.record,
    E = e.index,
    H = e.type,
    L = e.shouldRowExpand,
    F = e.level,
    K = x.exports.useContext(Je).rtl,
    z = le(V),
    T = ua((S && S(z, E)) || {}, []),
    D = R(V),
    Y = H === 'radio' ? p.slice(0, 1) : p,
    Q = D || E,
    B = Y.indexOf(D) > -1,
    I = Z.indexOf(D) > -1,
    te = b.indexOf(D) > -1,
    ue = ae(
      u + '-tr',
      ((n = {}), (n[u + '-row-checked'] = B), (n[u + '-row-expanded'] = I), n),
      m && m(z, E)
    ),
    k = d && typeof d.checkboxProps == 'function' ? d.checkboxProps(z) : {},
    se = ae(u + '-td', u + '-operation'),
    he = function (J) {
      var fe;
      return ae(
        se,
        u + '-' + J,
        ((fe = {}),
        (fe[u + '-selection-col'] = (C && H === 'checkbox') || H === 'radio'),
        (fe[u + '-expand-icon-col'] = C && t),
        fe)
      );
    };
  function ne(J) {
    return A.strictTreeData ? pe(J[O]) && J[O].length : J[O] !== void 0;
  }
  function me() {
    return g.find(function (J) {
      return ne(J);
    });
  }
  var ee = L(V, E),
    Ce = ne(V),
    we = me() && !t,
    Ne = we && Ce,
    ye = A.expandRowByClick,
    Ae =
      ye && (ee || Ne)
        ? {
            onClick: function (J) {
              a(D), T && T.onClick && T.onClick(J);
            },
          }
        : {},
    Pe = nr(l),
    Se = Pe.ComponentBodyRow,
    X = Pe.ComponentTd,
    ar = Pe.getBodyComponentOperations,
    ur = C ? 'div' : Se,
    Ye = C ? 'div' : X,
    ze = be(be({ className: ue, key: Q }, T), Ae),
    Fe = Hr(Se) ? ze : be(be({}, ze), { record: V, index: E });
  function Ge(J, fe) {
    var Ee = A.icon,
      Re = !!~Z.indexOf(fe),
      Ie = {
        onClick: function ($e) {
          $e.stopPropagation(), a(fe);
        },
      };
    return typeof Ee == 'function'
      ? Ee(be({ expanded: Re, record: J }, Ie))
      : c('button', {
          ...be({}, Ie, { type: 'button' }),
          children: Re ? c($n, {}) : c(Dn, {}),
        });
  }
  var or =
      t &&
      c(Ye, { className: he('expand-icon-cell'), children: ee && Ge(V, D) }),
    Be = d && d.renderCell,
    Qe,
    Te = c(ot, {
      ...be(
        {
          value: D,
          onChange: function (J) {
            return i(J, V);
          },
          checked: B,
          indeterminate: te,
        },
        k
      ),
    }),
    Le = c(Xe, {
      ...be(
        {
          onChange: function () {
            return v(D, V);
          },
          value: D,
          checked: B,
        },
        k
      ),
    });
  H === 'checkbox' &&
    (Qe = c(Ye, {
      className: he('checkbox'),
      children: Be ? Be(Te, B, z) : Te,
    })),
    H === 'radio' &&
      (Qe = c(Ye, {
        className: he('radio'),
        children: Be ? Be(Le, B, z) : Le,
      }));
  var Ue = ar({ selectionNode: Qe, expandNode: or });
  return c(ur, {
    ...be({}, Fe, { ref: r }),
    children: o.map(function (J, fe) {
      var Ee,
        Re,
        Ie,
        $e,
        De,
        G = h[fe],
        de = j[fe];
      if (J.$$isOperation) {
        var oe = J.node,
          je = !0;
        J.title === dr &&
          ((oe =
            (Re = Ue.find(function (ce) {
              return ce.name === 'selectionNode';
            })) === null || Re === void 0
              ? void 0
              : Re.node),
          (je = !1)),
          J.title === cr &&
            ((oe =
              (Ie = Ue.find(function (ce) {
                return ce.name === 'expandNode';
              })) === null || Ie === void 0
                ? void 0
                : Ie.node),
            (je = !1));
        var _e = typeof oe == 'function' ? oe(V) : oe;
        return ke.cloneElement(
          _e,
          be(be({ key: J.key || fe }, _e.props), {
            className: ae(
              je ? se : '',
              ($e = _e == null ? void 0 : _e.props) === null || $e === void 0
                ? void 0
                : $e.className,
              de
            ),
            style: be(
              be(
                be(
                  {},
                  (De = _e == null ? void 0 : _e.props) === null ||
                    De === void 0
                    ? void 0
                    : De.style
                ),
                J.fixed === 'left'
                  ? ((Ee = {}), (Ee[K ? 'right' : 'left'] = G), Ee)
                  : {}
              ),
              { width: J.width, minWidth: J.width }
            ),
          })
        );
      }
      return c(
        ca,
        {
          prefixCls: u,
          virtualized: C,
          components: l,
          currentSorter: w.find(function (ce) {
            return ce.field === J.key;
          }),
          placeholder: $,
          indentSize: P,
          stickyClassName: de,
          stickyOffset: G,
          InnerComponentTd: Ye,
          column: J,
          columnIndex: fe,
          record: V,
          trIndex: E,
          level: F,
          haveTreeData: we,
          recordHaveChildren: Ce,
          rowKey: D,
          renderExpandIcon: Ge,
        },
        fe
      );
    }),
  });
}
var va = x.exports.forwardRef(fa),
  At = va,
  Ke =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (Ke =
          Object.assign ||
          function (e) {
            for (var r, n = 1, t = arguments.length; n < t; n++) {
              r = arguments[n];
              for (var a in r)
                Object.prototype.hasOwnProperty.call(r, a) && (e[a] = r[a]);
            }
            return e;
          }),
        Ke.apply(this, arguments)
      );
    },
  pa = x.exports.forwardRef(function (e, r) {
    var n = e.record,
      t = e.index,
      a = e.virtualized,
      o = e.tbodyProps,
      l = o.prefixCls,
      i = o.columns,
      v = o.indentSize,
      u = v === void 0 ? 16 : v,
      p = o.childrenColumnName,
      b = p === void 0 ? 'children' : p,
      m = o.expandProps,
      S = m === void 0 ? {} : m,
      d = o.rowSelection,
      _ = o.hasFixedColumn,
      P = o.tableViewWidth,
      w = o.getRowKey,
      C = o.expandedRowKeys,
      h = o.expandedRowRender,
      j;
    d && 'type' in d ? (j = d.type) : d && !('type' in d) && (j = 'checkbox');
    var R =
        h &&
        function (E, H) {
          return h(le(E), H);
        },
      $ = function (E) {
        return pe(E[b]) && E[b].length;
      },
      W = function (E, H) {
        return 'rowExpandable' in S && typeof S.rowExpandable == 'function'
          ? S.rowExpandable(E)
          : R && R(E, H) !== null;
      },
      A = function (E, H) {
        var L = [],
          F = Ke(Ke({}, o), { type: j, shouldRowExpand: W });
        L.push(
          c(At, {
            ...Ke({ ref: r, key: w(E) }, F, { record: E, level: 0, index: H }),
          })
        );
        var K = function (z, T, D) {
          D === void 0 && (D = 0),
            pe(z) &&
              z.length &&
              z.forEach(function (Y, Q) {
                C.indexOf(T) !== -1 &&
                  (L.push(
                    c(At, {
                      ...Ke({}, F, {
                        key: w(Y),
                        record: Y,
                        level: D + 1,
                        index: Q,
                      }),
                    })
                  ),
                  $(Y) && K(Y[b], w(Y), D + 1));
              });
        };
        return R || K(E[b], w(E)), L;
      },
      g = w(n),
      Z = W(n, t) && C.indexOf(g) !== -1,
      O = a ? 'div' : 'tr',
      V = a ? 'div' : 'td';
    return ve(tr, {
      children: [
        A(n, t),
        Z &&
          c(
            O,
            {
              className: ae(l + '-tr', l + '-expand-content'),
              children: c(V, {
                className: ae(l + '-td'),
                style: { paddingLeft: u },
                colSpan: i.length,
                children: _
                  ? c('div', {
                      className: l + '-expand-fixed-row',
                      style: { width: P },
                      children: R == null ? void 0 : R(n, t),
                    })
                  : R == null
                  ? void 0
                  : R(n, t),
              }),
            },
            g + '-expanded'
          ),
      ],
    });
  });
function ha(e) {
  var r = e.data,
    n = e.columns,
    t = e.prefixCls,
    a = e.components,
    o = e.noDataElement,
    l = e.scroll,
    i = e.tableViewWidth,
    v = e.virtualized,
    u = e.virtualListProps,
    p = e.getRowKey,
    b = e.saveVirtualListRef,
    m = function (h) {
      var j;
      (j = e.saveRef) === null || j === void 0 || j.call(e, h);
    },
    S = nr(a).ComponentTbody,
    d = {},
    _ = {};
  l &&
    (l.x &&
      (typeof l.x == 'number' || typeof l.x == 'string') &&
      (d = { width: l.x }),
    l.y &&
      (typeof l.y == 'number' || typeof l.y == 'string') &&
      (_ = { maxHeight: l.y }));
  var P = { className: t + '-no-data' };
  i &&
    ((P.className = t + '-no-data ' + t + '-expand-fixed-row'),
    (P.style = { width: i }));
  var w = c('tr', {
      className: ae(t + '-tr', t + '-empty-row'),
      children: c('td', {
        className: t + '-td',
        colSpan: n.length,
        children: c('div', { ...Ke({}, P), children: o }),
      }),
    }),
    C = function (h, j) {
      var R;
      return c(
        pa,
        { record: h, index: j, virtualized: v, tbodyProps: e },
        (R = p(h)) !== null && R !== void 0 ? R : j
      );
    };
  return v
    ? r.length > 0
      ? c(jn, {
          ...Ke(
            {
              data: r,
              height: _.maxHeight,
              isStaticItemHeight: !1,
              outerStyle: Ke(Ke({}, d), {
                minWidth: '100%',
                overflow: 'visible',
              }),
              innerStyle: { right: 'auto', minWidth: '100%' },
              className: t + '-body',
              ref: function (h) {
                b(h), m(h == null ? void 0 : h.dom);
              },
              itemKey: p,
            },
            u
          ),
          children: C,
        })
      : c('div', {
          className: t + '-body',
          ref: m,
          children: c('table', { children: c('tbody', { children: w }) }),
        })
    : c(S, { ref: m, children: r.length > 0 ? r.map(C) : w });
}
var Ht = x.exports.createContext({});
function ya(e) {
  var r = e.summary,
    n = e.data,
    t = e.prefixCls,
    a = e.columns,
    o = e.stickyOffsets,
    l = e.stickyClassNames;
  return ke.createElement(
    'tfoot',
    { className: t + '-tfoot' },
    ke.createElement(
      Ht.Provider,
      {
        value: {
          columns: a,
          stickyOffsets: o,
          stickyClassNames: l,
          prefixCls: t,
        },
      },
      r(le(n))
    )
  );
}
function Ur(e) {
  return typeof e == 'number' || typeof e == 'string' ? { width: e } : {};
}
function Er(e) {
  var r = x.exports.useRef(),
    n = e.prefixCls,
    t = e.columns,
    a = e.columnWidths,
    o = e.producer,
    l = e.expandedRowKeys,
    i = e.data,
    v = e.onSetColumnWidths;
  x.exports.useEffect(
    function () {
      if (o && r.current) {
        var p = Array.from(r.current.querySelectorAll('col') || []).filter(
            function (m) {
              return (
                !m.classList.contains(n + '-expand-icon-col') &&
                !m.classList.contains(n + '-selection-col')
              );
            }
          ),
          b = p.map(function (m) {
            var S = m.getBoundingClientRect().width;
            return S;
          });
        v(b);
      }
    },
    [o, v, n, l, i, t]
  );
  var u = 0;
  return c('colgroup', {
    ref: r,
    children: t.map(function (p, b) {
      var m;
      if (p.title === cr)
        return c(
          'col',
          { className: n + '-expand-icon-col', style: Ur(p.width) },
          cr
        );
      if (p.title === dr)
        return c(
          'col',
          { className: n + '-selection-col', style: Ur(p.width) },
          dr
        );
      var S;
      return (
        p.width ? (S = p.width) : !o && a && (S = a[u]),
        u++,
        c('col', { style: Ur(S) }, (m = p.key) !== null && m !== void 0 ? m : b)
      );
    }),
  });
}
var ma =
  (globalThis && globalThis.__read) ||
  function (e, r) {
    var n = typeof Symbol == 'function' && e[Symbol.iterator];
    if (!n) return e;
    var t = n.call(e),
      a,
      o = [],
      l;
    try {
      for (; (r === void 0 || r-- > 0) && !(a = t.next()).done; )
        o.push(a.value);
    } catch (i) {
      l = { error: i };
    } finally {
      try {
        a && !a.done && (n = t.return) && n.call(t);
      } finally {
        if (l) throw l.error;
      }
    }
    return o;
  };
function ga(e, r, n) {
  var t = e.defaultExpandedRowKeys,
    a = e.defaultExpandAllRows,
    o = e.expandedRowRender,
    l = e.onExpand,
    i = e.onExpandedRowsChange,
    v = e.childrenColumnName,
    u = v === void 0 ? 'children' : v,
    p = e.expandProps,
    b = ma(x.exports.useState(_()), 2),
    m = b[0],
    S = b[1],
    d = e.expandedRowKeys || m;
  function _() {
    var C = [];
    return (
      e.expandedRowKeys
        ? (C = e.expandedRowKeys)
        : t
        ? (C = t)
        : a &&
          (C = r
            .map(function (h, j) {
              var R = le(h);
              return p &&
                'rowExpandable' in p &&
                typeof p.rowExpandable == 'function'
                ? p.rowExpandable(R) && n(h)
                : typeof o == 'function'
                ? o(R, j) && n(h)
                : Zr(h, u) && n(h);
            })
            .filter(function (h) {
              return h;
            })),
      C
    );
  }
  function P(C) {
    var h = d.indexOf(C) === -1,
      j = h
        ? d.concat(C)
        : d.filter(function ($) {
            return C !== $;
          }),
      R = r
        .filter(function ($) {
          return j.indexOf(n($)) !== -1;
        })
        .map(function ($) {
          return n($);
        });
    S(R), w(C, h), i && i(R);
  }
  function w(C, h) {
    l &&
      l(
        le(
          r.find(function (j) {
            return n(j) === C;
          })
        ),
        h
      );
  }
  return [d, P];
}
var tt =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (tt =
          Object.assign ||
          function (e) {
            for (var r, n = 1, t = arguments.length; n < t; n++) {
              r = arguments[n];
              for (var a in r)
                Object.prototype.hasOwnProperty.call(r, a) && (e[a] = r[a]);
            }
            return e;
          }),
        tt.apply(this, arguments)
      );
    },
  Fr =
    (globalThis && globalThis.__read) ||
    function (e, r) {
      var n = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!n) return e;
      var t = n.call(e),
        a,
        o = [],
        l;
      try {
        for (; (r === void 0 || r-- > 0) && !(a = t.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (n = t.return) && n.call(t);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    },
  xa =
    (globalThis && globalThis.__spreadArray) ||
    function (e, r, n) {
      if (n || arguments.length === 2)
        for (var t = 0, a = r.length, o; t < a; t++)
          (o || !(t in r)) &&
            (o || (o = Array.prototype.slice.call(r, 0, t)), (o[t] = r[t]));
      return e.concat(o || Array.prototype.slice.call(r));
    };
function Ft(e) {
  return xa([], Fr(new Set(e)), !1);
}
function ba(e, r, n, t) {
  var a = e.rowSelection,
    o = e.childrenColumnName,
    l = a == null ? void 0 : a.selectedRowKeys,
    i = a == null ? void 0 : a.onSelectAll,
    v = a == null ? void 0 : a.onSelect,
    u = a == null ? void 0 : a.onChange,
    p = a == null ? void 0 : a.pureKeys,
    b =
      typeof (a == null ? void 0 : a.checkStrictly) == 'boolean'
        ? !a.checkStrictly
        : !1,
    m = a == null ? void 0 : a.preserveSelectedRowKeys;
  function S() {
    var T = [],
      D = [],
      Y = function (B) {
        pe(B) &&
          B.length &&
          B.forEach(function (I) {
            var te = t(I),
              ue =
                a && typeof a.checkboxProps == 'function'
                  ? a.checkboxProps(le(I))
                  : {};
            ue.disabled || T.push(te),
              Zr(I, e.childrenColumnName) && Y(I[e.childrenColumnName]);
          });
      };
    Y(r);
    var Q = function (B, I) {
      pe(B) &&
        B.length &&
        B.forEach(function (te) {
          if (
            (I && b && (te.__INTERNAL_PARENT = I),
            D.push(te),
            Zr(te, e.childrenColumnName))
          ) {
            var ue = tt({}, te);
            Q(te[e.childrenColumnName], ue);
          }
        });
    };
    return Q(n, void 0), { allSelectedRowKeys: T, flattenData: D };
  }
  var d = S(),
    _ = d.allSelectedRowKeys,
    P = d.flattenData,
    w = Fr(x.exports.useState([]), 2),
    C = w[0],
    h = w[1],
    j = Fr(x.exports.useState([]), 2),
    R = j[0],
    $ = j[1],
    W = ia(P, Ft(l || C), t, o, b),
    A = b && !l ? C : W.selectedRowKeys,
    g = b && !l ? R : W.indeterminateKeys,
    Z = Fr(x.exports.useState(p ? [] : E(A)), 2),
    O = Z[0],
    V = Z[1];
  function E(T, D) {
    var Y = D ? O.concat(P) : P,
      Q = new Map(
        Y.map(function (B) {
          return [t(B), B];
        })
      );
    return T.map(function (B) {
      return Q.get(B);
    }).filter(function (B) {
      return B;
    });
  }
  var H = new Set(
    P.map(function (T) {
      return t(T);
    })
  );
  function L(T) {
    return m
      ? T
      : T.filter(function (D) {
          return H.has(D);
        });
  }
  function F(T) {
    var D = [],
      Y = [];
    if (T) D = L(Ft(A.concat(_)));
    else {
      var Q = new Set(_);
      D = L(
        A.filter(function (I) {
          return !Q.has(I);
        })
      );
    }
    p || (Y = E(D, !0));
    var B = le(Y);
    h(D), V(Y), $([]), u && u(D, B), i && i(T, B);
  }
  function K(T, D) {
    var Y = oa(D, T, A, R, t, o, b),
      Q = Y.selectedRowKeys,
      B = Y.indeterminateKeys,
      I = L(Q),
      te = E(I, !0),
      ue = le(te);
    h(I), V(te), $(B), v && v(T, le(D), ue), u && u(I, ue);
  }
  function z(T, D) {
    var Y = [
        P.find(function (B) {
          return t(B) === T;
        }),
      ],
      Q = le(Y);
    h([T]), v && v(!0, le(D), Q), u && u([T], Q);
  }
  return {
    selectedRowKeys: A,
    indeterminateKeys: g,
    onCheckAll: F,
    onCheck: K,
    onCheckRadio: z,
    setSelectedRowKeys: h,
    allSelectedRowKeys: _,
    flattenData: P,
  };
}
var Ca =
    (globalThis && globalThis.__read) ||
    function (e, r) {
      var n = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!n) return e;
      var t = n.call(e),
        a,
        o = [],
        l;
      try {
        for (; (r === void 0 || r-- > 0) && !(a = t.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (n = t.return) && n.call(t);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    },
  wa =
    (globalThis && globalThis.__spreadArray) ||
    function (e, r, n) {
      if (n || arguments.length === 2)
        for (var t = 0, a = r.length, o; t < a; t++)
          (o || !(t in r)) &&
            (o || (o = Array.prototype.slice.call(r, 0, t)), (o[t] = r[t]));
      return e.concat(o || Array.prototype.slice.call(r));
    };
function Sa(e) {
  var r = e.map(function (a) {
      return hr(a.width);
    }),
    n = e.map(function (a) {
      return a.fixed;
    }),
    t = x.exports.useMemo(
      function () {
        return e.map(function (a) {
          var o = 0;
          return (
            a.fixed === 'left' &&
              e.some(function (l) {
                if (l.fixed === 'left') {
                  if (l.key === a.key) return !0;
                  var i = l.$$isOperation ? hr(l.width) || 40 : hr(l.width);
                  return (o += i), !1;
                }
                return !1;
              }),
            a.fixed === 'right' &&
              wa([], Ca(e), !1)
                .reverse()
                .some(function (l) {
                  if (l.fixed === 'right') {
                    if (l.key === a.key) return !0;
                    var i = l.$$isOperation ? hr(l.width) || 40 : hr(l.width);
                    return (o += i), !1;
                  }
                  return !1;
                }),
            o
          );
        });
      },
      [r.join('-'), n.join('-')]
    );
  return t;
}
function _a(e, r, n) {
  var t = r.map(function (i) {
    return i.fixed;
  });
  function a(i, v) {
    var u;
    return ae(
      ((u = {}),
      (u[n + '-col-fixed-left'] = i.fixed === 'left'),
      (u[n + '-col-fixed-right'] = i.fixed === 'right'),
      (u[n + '-col-fixed-left-last'] =
        i.fixed === 'left' && (re(r[v + 1]) ? r[v + 1].fixed !== 'left' : !0)),
      (u[n + '-col-fixed-right-first'] =
        i.fixed === 'right' &&
        (re(r[v - 1]) ? r[v - 1].fixed !== 'right' : !0)),
      u)
    );
  }
  var o = x.exports.useMemo(
      function () {
        return r.map(function (i, v) {
          return a(i, v);
        });
      },
      [t.join('-')]
    ),
    l = x.exports.useMemo(
      function () {
        return e.map(function (i) {
          return i.map(function (v, u) {
            var p = u,
              b = v.$$columnIndex;
            return (
              Array.isArray(b) && b.length === 2
                ? (p = v.fixed === 'right' ? b[0] : b[1])
                : typeof b == 'number' && (p = b),
              a(v, p)
            );
          });
        });
      },
      [
        e
          .map(function (i) {
            return (
              '|' +
              i
                .map(function (v) {
                  return v.fixed || 'undefined';
                })
                .join('-') +
              '|'
            );
          })
          .join('_'),
      ]
    );
  return [l, o];
}
var ge =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (ge =
          Object.assign ||
          function (e) {
            for (var r, n = 1, t = arguments.length; n < t; n++) {
              r = arguments[n];
              for (var a in r)
                Object.prototype.hasOwnProperty.call(r, a) && (e[a] = r[a]);
            }
            return e;
          }),
        ge.apply(this, arguments)
      );
    },
  Na =
    (globalThis && globalThis.__read) ||
    function (e, r) {
      var n = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!n) return e;
      var t = n.call(e),
        a,
        o = [],
        l;
      try {
        for (; (r === void 0 || r-- > 0) && !(a = t.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (n = t.return) && n.call(t);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    },
  Ta =
    (globalThis && globalThis.__spreadArray) ||
    function (e, r, n) {
      if (n || arguments.length === 2)
        for (var t = 0, a = r.length, o; t < a; t++)
          (o || !(t in r)) &&
            (o || (o = Array.prototype.slice.call(r, 0, t)), (o[t] = r[t]));
      return e.concat(o || Array.prototype.slice.call(r));
    };
function It(e, r) {
  var n = [];
  function t(a) {
    a &&
      a.length > 0 &&
      a.forEach(function (o) {
        o[r] ? t(o[r]) : n.push(ge(ge({}, o), { key: o.key || o.dataIndex }));
      });
  }
  return t(e), n;
}
function Kt(e, r) {
  var n = 0;
  return (
    e &&
      e.length > 0 &&
      e.forEach(function (t) {
        var a = Kt(t[r], r) + 1;
        n = Math.max(a, n);
      }),
    n
  );
}
function Ra(e) {
  var r = e.components,
    n = e.rowSelection,
    t = e.expandedRowRender,
    a = e.expandProps,
    o = a === void 0 ? {} : a,
    l = e.columns,
    i = e.childrenColumnName,
    v = x.exports.useMemo(
      function () {
        return l || [];
      },
      [l]
    ),
    u = x.exports.useMemo(
      function () {
        return It(v, i);
      },
      [v, i]
    ),
    p = (n && n.type === 'checkbox') || (n && !('type' in n)),
    b = n && n.type === 'radio',
    m = o.width,
    S = !!t,
    d = p || b,
    _ = nr(r),
    P = _.getHeaderComponentOperations,
    w = _.getBodyComponentOperations,
    C = x.exports.useMemo(
      function () {
        return P({
          selectionNode: d ? 'holder_node' : '',
          expandNode: S ? 'holder_node' : '',
        });
      },
      [d, S, P]
    ),
    h = x.exports.useMemo(
      function () {
        return w({
          selectionNode: d ? 'holder_node' : '',
          expandNode: S ? 'holder_node' : '',
        });
      },
      [d, S, w]
    ),
    j = n && n.fixed,
    R = n && n.columnWidth,
    $ = x.exports.useCallback(
      function (Z, O, V) {
        var E = {},
          H = [];
        Z.forEach(function (K, z) {
          var T = ge({}, K);
          (!('key' in K) || typeof K.key == 'undefined') &&
            (T.key = T.dataIndex || z),
            z === 0
              ? ((T.$$isFirstColumn = !0),
                T.fixed === 'left' && (E.fixed = T.fixed))
              : (T.$$isFirstColumn = !1),
            H.push(T);
        });
        var L = S && { key: cr, title: cr, width: m, $$isOperation: !0 },
          F = d && { key: dr, title: dr, width: R, $$isOperation: !0 };
        return (
          j && (E.fixed = 'left'),
          (typeof V != 'number' || V === 0) &&
            Ta([], Na(O), !1)
              .reverse()
              .forEach(function (K, z) {
                if (K.node) {
                  var T =
                    C.filter(function (D) {
                      return D.node;
                    }).length -
                    z -
                    1;
                  K.name === 'expandNode'
                    ? H.unshift(ge(ge(ge({}, L), E), { $$columnIndex: T }))
                    : K.name === 'selectionNode'
                    ? H.unshift(ge(ge(ge({}, F), E), { $$columnIndex: T }))
                    : H.unshift(
                        ge(ge(ge({}, K), E), {
                          title: K.name,
                          key: K.name,
                          $$isOperation: !0,
                          width: K.width || 40,
                          $$columnIndex: T,
                        })
                      );
                }
              }),
          H
        );
      },
      [m, S, d, R, j, C]
    ),
    W = x.exports.useMemo(
      function () {
        return $(u, h);
      },
      [u, $, h]
    ),
    A = x.exports.useMemo(
      function () {
        return Kt(v, i);
      },
      [v, i]
    ),
    g = x.exports.useMemo(
      function () {
        var Z = Array.isArray(C)
          ? C.filter(function (L) {
              return L.node;
            }).length
          : 0;
        if (A === 1) {
          var O = v.map(function (L, F) {
            return ge(ge({}, L), { $$columnIndex: F + Z });
          });
          return [$(O, C, 0)];
        }
        var V = Z,
          E = [],
          H = function (L, F) {
            F === void 0 && (F = 0),
              (E[F] = E[F] || []),
              L.forEach(function (K) {
                var z = ge({}, K);
                z[i]
                  ? ((z.colSpan = It(K[i], i).length),
                    (z.$$columnIndex = [V]),
                    E[F].push(z),
                    H(z[i], F + 1),
                    z.$$columnIndex.push(V - 1))
                  : ((z.rowSpan = A - F),
                    (z.$$columnIndex = V++),
                    E[F].push(z));
              }),
              (E[F] = $(E[F], C, F));
          };
        return H(v), E;
      },
      [v, i, A, $, C]
    );
  return [g, W];
}
var Ar =
    (globalThis && globalThis.__read) ||
    function (e, r) {
      var n = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!n) return e;
      var t = n.call(e),
        a,
        o = [],
        l;
      try {
        for (; (r === void 0 || r-- > 0) && !(a = t.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (n = t.return) && n.call(t);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    },
  qr =
    (globalThis && globalThis.__spreadArray) ||
    function (e, r, n) {
      if (n || arguments.length === 2)
        for (var t = 0, a = r.length, o; t < a; t++)
          (o || !(t in r)) &&
            (o || (o = Array.prototype.slice.call(r, 0, t)), (o[t] = r[t]));
      return e.concat(o || Array.prototype.slice.call(r));
    };
function Oa(e, r) {
  var n = Ar(x.exports.useState(r), 2),
    t = n[0],
    a = n[1],
    o = Ar(x.exports.useState({}), 2),
    l = o[0],
    i = o[1],
    v = x.exports.useRef(e),
    u = x.exports.useCallback(
      function (m) {
        var S = m.field,
          d = m.direction;
        return t.find(function (_) {
          return _.field === S;
        })
          ? d
            ? t.map(function (_) {
                return _.field === S ? m : _;
              })
            : t.filter(function (_) {
                return _.field !== S;
              })
          : d
          ? !rr(m.priority) ||
            t.find(function (_) {
              return !rr(_.priority);
            })
            ? [m]
            : qr(qr([], Ar(t), !1), [m], !1)
          : qr([], Ar(t), !1);
      },
      [t]
    ),
    p = x.exports.useCallback(function (m) {
      var S = m.filter(function (_) {
          return 'sortOrder' in _;
        }),
        d = [];
      return (
        S.forEach(function (_) {
          var P = rt(_.sorter),
            w = _.sortOrder,
            C = {
              field: _.key,
              direction: w,
              sorterFn: et(_.sorter),
              priority: P,
            };
          w
            ? rr(P)
              ? d.every(function (h) {
                  return rr(h.priority) || !h.direction;
                }) && d.push(C)
              : d.every(function (h) {
                  return !h.direction;
                })
              ? d.push(C)
              : (d = [C])
            : d.push(C);
        }),
        d
      );
    }, []),
    b = x.exports.useCallback(
      function (m, S) {
        var d = p(e);
        d.length || (a(S), i(m));
      },
      [e, p, a, i]
    );
  return (
    Xr(
      function () {
        var m = v.current,
          S = p(m),
          d = p(e),
          _ = S.map(function (w) {
            return w.field;
          }),
          P = d.filter(function (w) {
            var C = S.find(function (h) {
              var j = h.field,
                R = h.direction;
              return w.field === j && w.direction !== R;
            });
            return C ? !0 : !_.includes(w.field);
          });
        P && P.length && (a(d), i({})), (v.current = e);
      },
      [e, p, u, i, a]
    ),
    {
      currentSorter: l,
      activeSorters: t,
      getNextActiveSorters: u,
      updateStateSorters: b,
    }
  );
}
var ie =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (ie =
          Object.assign ||
          function (e) {
            for (var r, n = 1, t = arguments.length; n < t; n++) {
              r = arguments[n];
              for (var a in r)
                Object.prototype.hasOwnProperty.call(r, a) && (e[a] = r[a]);
            }
            return e;
          }),
        ie.apply(this, arguments)
      );
    },
  We =
    (globalThis && globalThis.__read) ||
    function (e, r) {
      var n = typeof Symbol == 'function' && e[Symbol.iterator];
      if (!n) return e;
      var t = n.call(e),
        a,
        o = [],
        l;
      try {
        for (; (r === void 0 || r-- > 0) && !(a = t.next()).done; )
          o.push(a.value);
      } catch (i) {
        l = { error: i };
      } finally {
        try {
          a && !a.done && (n = t.return) && n.call(t);
        } finally {
          if (l) throw l.error;
        }
      }
      return o;
    },
  ka =
    (globalThis && globalThis.__spreadArray) ||
    function (e, r, n) {
      if (n || arguments.length === 2)
        for (var t = 0, a = r.length, o; t < a; t++)
          (o || !(t in r)) &&
            (o || (o = Array.prototype.slice.call(r, 0, t)), (o[t] = r[t]));
      return e.concat(o || Array.prototype.slice.call(r));
    },
  Pa = [],
  Ea = [],
  Aa = {
    showHeader: !0,
    border: !0,
    hover: !0,
    rowKey: 'key',
    pagePosition: 'br',
    childrenColumnName: 'children',
    indentSize: 15,
    showSorterTooltip: !0,
  };
function Fa(e, r) {
  var n,
    t,
    a,
    o,
    l = x.exports.useContext(Je),
    i = l.getPrefixCls,
    v = l.loadingElement,
    u = l.size,
    p = l.tablePagination,
    b = l.renderEmpty,
    m = l.componentConfig,
    S = l.rtl,
    d = Ir(e, Aa, m == null ? void 0 : m.Table),
    _ = Ir(
      re(e == null ? void 0 : e.pagination)
        ? e == null
          ? void 0
          : e.pagination
        : {},
      re(
        (a = m == null ? void 0 : m.Table) === null || a === void 0
          ? void 0
          : a.pagination
      )
        ? (o = m == null ? void 0 : m.Table) === null || o === void 0
          ? void 0
          : o.pagination
        : {},
      p || {}
    ),
    P = d.style,
    w = d.className,
    C = d.components,
    h = d.border,
    j = d.borderCell,
    R = d.columns,
    $ = R === void 0 ? Ea : R,
    W = d.data,
    A = W === void 0 ? Pa : W,
    g = d.scroll,
    Z = d.noDataElement,
    O = d.showHeader,
    V = d.stripe,
    E = d.hover,
    H = d.pagination,
    L = d.onChange,
    F = d.pagePosition,
    K = d.childrenColumnName,
    z = d.indentSize,
    T = d.rowSelection,
    D = d.tableLayoutFixed,
    Y = d.footer,
    Q = d.virtualized,
    B = d.renderPagination,
    I = d.summary,
    te = d.rowKey,
    ue = x.exports.useMemo(
      function () {
        return aa(A, K);
      },
      [A, K]
    ),
    k = i('table'),
    se =
      d.size ||
      (['default', 'middle', 'small', 'mini'].indexOf(u) > -1 ? u : 'default'),
    he = x.exports.useRef(null),
    ne = x.exports.useRef(null),
    me = x.exports.useRef(null),
    ee = x.exports.useRef(null),
    Ce = x.exports.useRef(null),
    we = x.exports.useRef(null),
    Ne = x.exports.useRef(null),
    ye = x.exports.useRef(0),
    Ae = x.exports.useRef(!1),
    Pe = We(Ra(d), 2),
    Se = Pe[0],
    X = Pe[1],
    ar = Gt(),
    ur = ar.currentFilters,
    Ye = ar.defaultSorters,
    ze = We(x.exports.useState(1), 2),
    Fe = ze[0],
    Ge = ze[1],
    or = We(x.exports.useState(_.pageSize || _.defaultPageSize || 10), 2),
    Be = or[0],
    Qe = or[1],
    Te = We(x.exports.useState(ur), 2),
    Le = Te[0],
    Ue = Te[1],
    J = We(x.exports.useState(0), 2),
    fe = J[0],
    Ee = J[1],
    Re = We(x.exports.useState([]), 2),
    Ie = Re[0],
    $e = Re[1],
    De = Sa(X),
    G = We(_a(Se, X, k), 2),
    de = G[0],
    oe = G[1],
    je = Oa(X, Ye),
    _e = je.currentSorter,
    ce = je.activeSorters,
    Mt = je.getNextActiveSorters,
    Wt = je.updateStateSorters,
    Kr = nr(C),
    mr = Kr.ComponentTable,
    Vt = Kr.ComponentBodyWrapper,
    Yt = Kr.ComponentHeaderWrapper,
    zr = x.exports.useMemo(
      function () {
        return typeof te == 'function'
          ? function (s) {
              return te(le(s));
            }
          : function (s) {
              return s[te];
            };
      },
      [te]
    );
  function Gt() {
    var s = {},
      f = [];
    return (
      X.forEach(function (y) {
        var N = y.key;
        if (
          (y.defaultFilters && (s[N] = y.defaultFilters),
          y.filteredValue && (s[N] = y.filteredValue),
          'defaultSortOrder' in y || 'sortOrder' in y)
        ) {
          var M = rt(y.sorter),
            U = 'sortOrder' in y ? y.sortOrder : y.defaultSortOrder,
            q = { field: N, direction: U, sorterFn: et(y.sorter), priority: M };
          U
            ? rr(M)
              ? f.every(function (xe) {
                  return rr(xe.priority) || !xe.direction;
                }) && f.push(q)
              : f.every(function (xe) {
                  return !xe.direction;
                })
              ? f.push(q)
              : (f = [q])
            : f.push(q);
        }
      }),
      { currentFilters: s, defaultSorters: f }
    );
  }
  var gr = x.exports.useMemo(
      function () {
        var s = X.filter(function (y) {
            return 'filteredValue' in y;
          }),
          f = {};
        return (
          s.length &&
            s.forEach(function (y, N) {
              var M = y.key || y.dataIndex || N;
              M !== void 0 && (f[M] = y.filteredValue);
            }),
          f
        );
      },
      [X]
    ),
    Ze = x.exports.useMemo(
      function () {
        return Object.keys(gr).length ? gr : Le;
      },
      [Le, gr]
    );
  function Ut(s, f) {
    var y = ht(f);
    if (!!y) {
      var N = {
          direction: s,
          field: f,
          sorterFn: et(y.sorter),
          priority: rt(y.sorter),
        },
        M = Mt(N);
      Wt(N, M);
      var U = wr(N, M, Ze),
        q = vr(U);
      L &&
        L(lr(U), N, Ze, {
          currentData: le(q),
          currentAllData: le(U),
          action: 'sort',
        });
    }
  }
  function qt(s) {
    var f = function (N, M) {
        return function (U, q) {
          var xe = N(U, q);
          return M === 'descend' ? -xe : xe;
        };
      },
      y = ka([], We(s), !1);
    return (
      y.sort(function (N, M) {
        return M.priority - N.priority;
      }),
      function (N, M) {
        for (var U = 0, q = y.length; U < q; U++) {
          var xe = y[U],
            Rr = xe.sorterFn,
            Gr = xe.direction;
          if (typeof Rr == 'function') {
            var Nt = f(Rr, Gr)(N, M);
            if (Nt !== 0) return Nt;
          }
        }
        return 0;
      }
    );
  }
  function Xt(s, f) {
    var y,
      N = ie(ie({}, Ze), ((y = {}), (y[s.dataIndex] = f), y)),
      M = ie(ie({}, N), gr);
    if (pe(f) && f.length) {
      Ue(M);
      var U = wr(_e, ce, N),
        q = vr(U);
      L &&
        L(lr(U), ce.length === 1 ? ce[0] : ce, N, {
          currentData: le(q),
          currentAllData: le(U),
          action: 'filter',
        });
    } else pe(f) && !f.length && ct(s);
  }
  function ct(s) {
    var f = s.dataIndex,
      y = ie({}, Ze);
    delete y[f], Ue(y);
    var N = wr(_e, ce, y),
      M = vr(N);
    L &&
      L(lr(N), ce.length === 1 ? ce[0] : ce, y, {
        currentData: le(M),
        currentAllData: le(N),
        action: 'filter',
      });
  }
  var xr = !!X.find(function (s) {
      return s.fixed === 'left';
    }),
    br = !!X.find(function (s) {
      return s.fixed === 'right';
    }),
    Cr = xr || br;
  function wr(s, f, y) {
    var N = (ue || []).slice();
    Object.keys(y).forEach(function (U) {
      if (y[U] && y[U].length) {
        var q = ht(U);
        q &&
          typeof q.onFilter == 'function' &&
          (N = N.filter(function (xe) {
            return y[U].reduce(function (Rr, Gr) {
              return Rr || q.onFilter(Gr, xe);
            }, !1);
          }));
      }
    });
    var M = function (U) {
      return U.slice()
        .sort(qt(f))
        .map(function (q) {
          var xe;
          return pe(q[K])
            ? ie(ie({}, q), ((xe = {}), (xe[K] = M(q[K])), xe))
            : q;
        });
    };
    return (s.direction && typeof s.sorterFn == 'function') || f.length
      ? M(N)
      : N;
  }
  var ir = wr(_e, ce, Ze);
  function lr(s) {
    s === void 0 && (s = ir);
    var f = _.pageSize || Be || 10,
      y = se === 'middle' ? 'default' : se,
      N = 'top';
    F === 'tl' || F === 'bl' ? (N = 'bottom') : (N = 'top');
    var M = pe(s) ? s.length : 0,
      U = Math.ceil(M / f) < Fe ? 1 : Fe;
    U !== Fe && Ge(U);
    var q = {
      size: y,
      total: M,
      pageSize: f,
      current: U,
      selectProps: { triggerProps: { position: N } },
    };
    return (
      typeof H == 'object' &&
        H.selectProps &&
        (q.selectProps = ie(ie({}, q.selectProps), H.selectProps)),
      re(H) && (q = ie(ie({}, q), H)),
      re(_) && (q = ie(ie({}, q), _)),
      (q.onChange = ln),
      q
    );
  }
  var Sr = lr(),
    fr = vr();
  function vr(s, f) {
    s === void 0 && (s = ir), f === void 0 && (f = Sr);
    var y = f.current,
      N = y === void 0 ? 0 : y,
      M = f.pageSize,
      U = M === void 0 ? 10 : M;
    return H === !1 || (re(H) && A.length <= U)
      ? s
      : s.slice((N - 1) * U, N * U);
  }
  var ut = Hn(ft, 100),
    Me = !!(g && g.y),
    pr = I == null ? void 0 : I(le(ir)),
    Br = I && ke.isValidElement(pr) && pr.props.fixed,
    Jt = Me && Br;
  Kn(
    function () {
      ft(), Or(window, 'resize', ut);
      var s = he.current,
        f = ne.current,
        y = ee.current;
      f && Or(f, 'scroll', sr);
      var N = s && s.parentNode;
      return (
        s && N && Or(N, 'scroll', sr),
        y && Or(y, 'scroll', sr),
        function () {
          kr(window, 'resize', ut),
            f && kr(f, 'scroll', sr),
            N && kr(N, 'scroll', sr),
            y && kr(y, 'scroll', sr);
        }
      );
    },
    [xr, br, g == null ? void 0 : g.x, g == null ? void 0 : g.y, X.length, A]
  ),
    Xr(
      function () {
        var s = lr(A),
          f = s.total,
          y = s.pageSize,
          N = Math.ceil(f / y);
        N < Fe && Ge(1);
      },
      [A == null ? void 0 : A.length]
    ),
    Xr(
      function () {
        _r();
      },
      [A, xr, br, S]
    ),
    x.exports.useImperativeHandle(r, function () {
      return {
        getRootDomElement: Lr,
        scrollIntoView: function (s) {
          we.current && we.current.scrollTo({ key: s });
        },
        getRootDOMNode: Lr,
      };
    });
  function Lr() {
    return Ce.current;
  }
  function ft() {
    _r();
    var s = Lr();
    if (s && (Cr || (g && g.x))) {
      var f =
          s.querySelector('.' + k + '-body') ||
          s.querySelector('.' + k + '-content-inner'),
        y = f.getBoundingClientRect().width;
      Ee(y);
    }
  }
  var Qt = x.exports.useCallback(
    zn(function () {
      var s = Ce.current,
        f = Me ? ne.current : Ne.current && Ne.current.parentNode;
      if (f) {
        var y = S ? -f.scrollLeft : f.scrollLeft,
          N = y === 0,
          M =
            y + 1 >=
            f.children[0].getBoundingClientRect().width -
              f.getBoundingClientRect().width;
        N && M
          ? Nr(s.classList, k + '-scroll-position-both')
          : N
          ? Nr(s.classList, k + '-scroll-position-' + (S ? 'right' : 'left'))
          : M
          ? Nr(s.classList, k + '-scroll-position-' + (S ? 'left' : 'right'))
          : Nr(s.classList, k + '-scroll-position-middle');
      } else s && pt(s.classList);
    }, 100),
    [Ce.current, ne.current, Me, S]
  );
  function _r() {
    if (Cr || (g && re(g) && g.x)) {
      var s = Ce.current;
      s &&
        (xr && vt(s.classList, k + '-has-fixed-col-left'),
        br && vt(s.classList, k + '-has-fixed-col-right')),
        Qt();
    }
  }
  function vt(s, f) {
    s.contains(f) || s.add(f);
  }
  function pt(s) {
    s.remove(k + '-scroll-position-both'),
      s.remove(k + '-scroll-position-left'),
      s.remove(k + '-scroll-position-right'),
      s.remove(k + '-scroll-position-middle');
  }
  function Nr(s, f) {
    s.contains(f) || (pt(s), s.add(f));
  }
  var qe = ba(d, fr, ue, zr),
    Mr = qe.selectedRowKeys,
    Zt = qe.indeterminateKeys,
    en = qe.onCheckAll,
    rn = qe.onCheck,
    tn = qe.onCheckRadio,
    nn = qe.setSelectedRowKeys,
    an = qe.allSelectedRowKeys,
    on = qe.flattenData;
  function ht(s) {
    return X.find(function (f, y) {
      return typeof f.key != 'undefined'
        ? typeof f.key == 'number' && typeof s == 'string'
          ? f.key.toString() === s
          : f.key === s
        : typeof f.dataIndex != 'undefined'
        ? f.dataIndex === s
        : typeof s == 'number'
        ? y === s
        : !1;
    });
  }
  function ln(s, f) {
    Ge(s),
      Qe(f),
      s !== Fe && sn(),
      T &&
        !T.checkCrossPage &&
        Mr.length &&
        (nn([]), T.onChange && T.onChange([], []));
    var y = ie(ie({}, lr()), { current: s, pageSize: f });
    L &&
      L(y, ce.length === 1 ? ce[0] : ce, Ze, {
        currentData: le(vr(ir, y)),
        currentAllData: le(ir),
        action: 'paginate',
      }),
      _.onChange && _.onChange(s, f);
  }
  function sn() {
    var s = ne.current;
    if (!!s) {
      var f = ne.current.scrollTop,
        y = new Mn({
          from: { scrollTop: f },
          to: { scrollTop: 0 },
          easing: 'quintInOut',
          duration: 300,
          onUpdate: function (N) {
            ne.current && (ne.current.scrollTop = N.scrollTop);
          },
        });
      y.start();
    }
  }
  function sr(s) {
    var f = s.target,
      y = ne.current,
      N = he.current && he.current.parentNode,
      M = ee.current;
    f.scrollLeft !== ye.current &&
      (N && (N.scrollLeft = f.scrollLeft),
      y && (y.scrollLeft = f.scrollLeft),
      M && (M.scrollLeft = f.scrollLeft),
      _r()),
      (ye.current = s.target.scrollLeft);
  }
  function dn(s) {
    var f = s.target,
      y = Ne.current;
    f.scrollLeft !== ye.current && ((y.scrollLeft = f.scrollLeft), _r()),
      (ye.current = s.target.scrollLeft);
  }
  var yt = We(ga(d, on, zr), 2),
    mt = yt[0],
    cn = yt[1],
    gt = {},
    Wr = {};
  g &&
    (g.x &&
      (typeof g.x == 'number' || typeof g.x == 'string') &&
      (Wr = { width: g.x }),
    g.y &&
      (typeof g.y == 'number' || typeof g.y == 'string') &&
      (gt = { maxHeight: g.y }));
  function un() {
    var s = he.current && he.current.parentNode,
      f = ta(s);
    f &&
      f > 0 &&
      ((s.style.marginBottom = '-' + f + 'px'),
      (s.style.paddingBottom = '0px'),
      ee.current &&
        ((ee.current.style.marginBottom = '-' + f + 'px'),
        (ee.current.style.paddingBottom = '0px'))),
      setTimeout(function () {
        var y = ne.current,
          N = na(y);
        N
          ? ((Ae.current = !0),
            s && (s.style.overflowY = 'scroll'),
            ee.current && (ee.current.style.overflowY = 'scroll'))
          : s &&
            Ae.current &&
            ((Ae.current = !1),
            (s.style.overflowY = 'auto'),
            ee.current && (ee.current.style.overflowY = 'auto'));
      });
  }
  var xt = c(ra, {
    ...ie({}, d, {
      activeSorters: ce,
      currentSorter: _e,
      selectedRowKeys: Mr,
      currentFilters: Ze,
      onCheckAll: en,
      onSort: Ut,
      data: fr,
      onHandleFilter: Xt,
      onHandleFilterReset: ct,
      prefixCls: k,
      allSelectedRowKeys: an,
      groupColumns: Se,
      stickyOffsets: De,
      groupStickyClassNames: de,
    }),
  });
  function fn() {
    var s = re(g) && g.x === 'max-content';
    return Me || Q
      ? c(Yt, {
          className: k + '-header',
          children: ve(mr, {
            ref: he,
            style: s ? {} : Wr,
            children: [
              c(Er, {
                columns: X,
                prefixCls: k,
                producer: !1,
                columnWidths: s && g.y ? Ie : null,
              }),
              xt,
            ],
          }),
        })
      : xt;
  }
  var bt =
      pr &&
      c(ya, {
        prefixCls: k,
        summary: I,
        data: fr,
        columns: X,
        stickyOffsets: De,
        stickyClassNames: oe,
      }),
    Ct = c(ha, {
      ...ie({}, d, {
        saveRef: function (s) {
          return (me.current = s);
        },
        selectedRowKeys: Mr,
        indeterminateKeys: Zt,
        expandedRowKeys: mt,
        onCheck: rn,
        onCheckRadio: tn,
        onClickExpandBtn: cn,
        columns: X,
        data: fr,
        prefixCls: k,
        hasFixedColumn: Cr,
        tableViewWidth: fe,
        indentSize: z,
        noDataElement: Z || b('Table'),
        activeSorters: ce,
        currentSorter: _e,
        stickyOffsets: De,
        stickyClassNames: oe,
        getRowKey: zr,
        saveVirtualListRef: function (s) {
          Q && ((we.current = s), (ne.current = s == null ? void 0 : s.dom));
        },
      }),
    }),
    wt = !Q && !Jt ? ve(tr, { children: [Ct, bt] }) : Ct;
  function vn() {
    var s = re(g) && g.x === 'max-content' && !!g.y && pe(A) && A.length > 0;
    return c(Ln, {
      onResize: un,
      getTargetDOMNode: function () {
        return ne.current || me.current;
      },
      children:
        Me && !Q
          ? c(Vt, {
              ref: ne,
              className: k + '-body',
              style: gt,
              children: ve(mr, {
                style: Wr,
                children: [
                  c(Er, {
                    columns: X,
                    prefixCls: k,
                    producer: s,
                    onSetColumnWidths: $e,
                    expandedRowKeys: mt,
                    data: A,
                  }),
                  wt,
                ],
              }),
            })
          : wt,
    });
  }
  function pn() {
    var s = {};
    g && re(g) && g.x && (s = { width: g.x });
    var f = c('div', {
        className: k + '-tfoot',
        ref: ee,
        children: ve(mr, {
          style: s,
          children: [c(Er, { columns: X, prefixCls: k }), bt],
        }),
      }),
      y = pr && Me && Br === 'top',
      N = pr && Me && Br === 'bottom',
      M = ve(tr, { children: [O ? fn() : null, y && f, vn(), N && f] });
    return ve(tr, {
      children: [
        c('div', {
          className: k + '-container',
          children: c('div', {
            className: k + '-content-scroll',
            children: c('div', {
              className: k + '-content-inner',
              onScroll: Me ? void 0 : dn,
              children:
                Me || Q
                  ? M
                  : ve(mr, {
                      ref: Ne,
                      style: s,
                      children: [c(Er, { prefixCls: k, columns: X }), M],
                    }),
            }),
          }),
        }),
        typeof Y == 'function' &&
          c('div', { className: k + '-footer', children: Y(fr) }),
      ],
    });
  }
  if (!$.length) return null;
  var hn = re(h) ? h.wrapper : h,
    Vr = re(h) ? h.cell : j,
    yn = re(h) ? h.cell || h.headerCell : j,
    mn = re(h) ? h.cell || h.bodyCell : j,
    gn = ae(
      k,
      k + '-size-' + se,
      ((n = {}),
      (n[k + '-border'] = hn),
      (n[k + '-border-cell'] = Vr),
      (n[k + '-border-header-cell'] = !Vr && yn),
      (n[k + '-border-body-cell'] = !Vr && mn),
      (n[k + '-stripe'] = V),
      (n[k + '-hover'] = E),
      (n[k + '-type-radio'] = T && T.type === 'radio'),
      (n[k + '-layout-fixed'] =
        D ||
        (g && (g.x || g.y)) ||
        $.find(function (s) {
          return s.ellipsis;
        })),
      (n[k + '-fixed-column'] = Cr),
      (n[k + '-virtualized'] = Q),
      (n[k + '-rtl'] = S),
      n),
      w
    ),
    Yr = F === 'tl' || F === 'tr' || F === 'topCenter',
    xn = ae(
      k + '-pagination',
      ((t = {}),
      (t[k + '-pagination-left'] = F === 'tl' || F === 'bl'),
      (t[k + '-pagination-center'] = F === 'topCenter' || F === 'bottomCenter'),
      (t[k + '-pagination-top'] = Yr),
      t)
    ),
    Tr = d.loading;
  typeof Tr == 'boolean' && (Tr = { loading: Tr });
  var bn = typeof B == 'function',
    St = bn
      ? B(c(Ot, { ...ie({}, Sr) }))
      : c('div', { className: xn, children: c(Ot, { ...ie({}, Sr) }) }),
    _t = H !== !1 && (ir.length !== 0 || Sr.total > 0);
  return c('div', {
    ...ie({ ref: Ce, style: P, className: gn }, $t(d)),
    children: ve(Bn, {
      ...ie({ element: v }, Tr),
      children: [_t && Yr && St, pn(), _t && !Yr && St],
    }),
  });
}
var zt = x.exports.forwardRef(Fa);
zt.displayName = 'Table';
var Ia = zt,
  er =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (er =
          Object.assign ||
          function (e) {
            for (var r, n = 1, t = arguments.length; n < t; n++) {
              r = arguments[n];
              for (var a in r)
                Object.prototype.hasOwnProperty.call(r, a) && (e[a] = r[a]);
            }
            return e;
          }),
        er.apply(this, arguments)
      );
    },
  $a =
    (globalThis && globalThis.__rest) ||
    function (e, r) {
      var n = {};
      for (var t in e)
        Object.prototype.hasOwnProperty.call(e, t) &&
          r.indexOf(t) < 0 &&
          (n[t] = e[t]);
      if (e != null && typeof Object.getOwnPropertySymbols == 'function')
        for (var a = 0, t = Object.getOwnPropertySymbols(e); a < t.length; a++)
          r.indexOf(t[a]) < 0 &&
            Object.prototype.propertyIsEnumerable.call(e, t[a]) &&
            (n[t[a]] = e[t[a]]);
      return n;
    };
function Da(e) {
  var r = x.exports.useContext(Je).rtl,
    n = x.exports.useContext(Ht),
    t = n.columns,
    a = n.stickyOffsets,
    o = n.stickyClassNames,
    l = n.prefixCls,
    i = e.children,
    v = $a(e, ['children']),
    u = ke.Children.map(i, function (b) {
      return b.props.colSpan || 1;
    }),
    p = ke.Children.map(i, function (b, m) {
      var S,
        d,
        _,
        P,
        w = b,
        C =
          $r(w, 'type.__ARCO_TABLE_SUMMARY_CELL__') ||
          $r(w, 'props.$$ArcoTableSummaryCell'),
        h =
          (_ = w == null ? void 0 : w.props) === null || _ === void 0
            ? void 0
            : _.style,
        j =
          (P = w == null ? void 0 : w.props) === null || P === void 0
            ? void 0
            : P.className,
        R = u.slice(0, m).reduce(function (g, Z) {
          return g + Z;
        }, 0),
        $ = R,
        W =
          t[$].fixed === 'left'
            ? ((S = {}), (S[r ? 'right' : 'left'] = a[$]), S)
            : t[$].fixed === 'right'
            ? ((d = {}), (d[r ? 'left' : 'right'] = a[$]), d)
            : {},
        A = t[$].fixed === 'left' || t[$].fixed === 'right' ? o[$] : '';
      return C
        ? ke.cloneElement(
            w,
            er(er({}, w.props), {
              className: ae(l + '-td', A, j),
              style: er(er({}, h), W),
            })
          )
        : b;
    });
  return c('tr', { ...er({}, v), children: p });
}
var nt =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (nt =
          Object.assign ||
          function (e) {
            for (var r, n = 1, t = arguments.length; n < t; n++) {
              r = arguments[n];
              for (var a in r)
                Object.prototype.hasOwnProperty.call(r, a) && (e[a] = r[a]);
            }
            return e;
          }),
        nt.apply(this, arguments)
      );
    },
  ja =
    (globalThis && globalThis.__rest) ||
    function (e, r) {
      var n = {};
      for (var t in e)
        Object.prototype.hasOwnProperty.call(e, t) &&
          r.indexOf(t) < 0 &&
          (n[t] = e[t]);
      if (e != null && typeof Object.getOwnPropertySymbols == 'function')
        for (var a = 0, t = Object.getOwnPropertySymbols(e); a < t.length; a++)
          r.indexOf(t[a]) < 0 &&
            Object.prototype.propertyIsEnumerable.call(e, t[a]) &&
            (n[t[a]] = e[t[a]]);
      return n;
    };
function Bt(e) {
  var r = e.children,
    n = ja(e, ['children']);
  return ke.createElement('td', nt({}, Dt(n, ['$$ArcoTableSummaryCell'])), r);
}
Bt.__ARCO_TABLE_SUMMARY_CELL__ = !0;
function dt(e) {
  return e.children;
}
dt.Row = Da;
dt.Cell = Bt;
var Lt = Ia;
Lt.Summary = dt;
var za = Lt;
export { Xe as R, za as T };
