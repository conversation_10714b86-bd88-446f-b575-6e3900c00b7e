import { http, HttpResponse } from 'msw';

const profileHandlers = [
  // 基础信息
  http.get('/api/profile/basic', () => {
    return HttpResponse.json({
      status: 'success',
      data: {
        status: '已发布',
        publicTime: '2020-05-11 16:25:56',
        name: '月度计划',
        description: '计划详情',
        tags: ['计划进度', '高优先级'],
        link: 'https://arco.design',
        basicInfo: [
          {
            key: '内容类型',
            value: '产品文档',
          },
          {
            key: '内容组织',
            value: '产品部',
          },
          {
            key: '创建人',
            value: '张三',
          },
          {
            key: '内容来源',
            value: '内部资料',
          },
          {
            key: '创建时间',
            value: '2021-02-28 18:17:04',
          },
          {
            key: '内容类目',
            value: '业务文档',
          },
        ],
        units: [
          {
            name: '产品部',
            members: [
              {
                name: '小明',
                avatar:
                  '//p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp',
                job: '产品设计师',
                jobName: 'UI/UE',
              },
              {
                name: '小红',
                avatar:
                  '//p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp',
                job: '产品经理',
                jobName: 'PM',
              },
              {
                name: '小刚',
                avatar:
                  '//p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp',
                job: '后端开发工程师',
                jobName: 'Backend',
              },
              {
                name: '小强',
                avatar:
                  '//p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp',
                job: '前端开发工程师',
                jobName: 'Frontend',
              },
            ],
          },
          {
            name: '设计部',
            members: [
              {
                name: '小智',
                avatar:
                  '//p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp',
                job: '视觉设计师',
                jobName: 'UI',
              },
              {
                name: '小华',
                avatar:
                  '//p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp',
                job: '交互设计师',
                jobName: 'UE',
              },
            ],
          },
          {
            name: '市场部',
            members: [
              {
                name: '小月',
                avatar:
                  '//p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp',
                job: '市场营销专员',
                jobName: 'Marketing',
              },
              {
                name: '小海',
                avatar:
                  '//p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp',
                job: '销售专员',
                jobName: 'Sales',
              },
            ],
          },
        ],
      },
    });
  }),
];

export default profileHandlers;
