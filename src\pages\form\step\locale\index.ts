const i18n = {
  'en-US': {
    'menu.form': 'Form',
    'menu.form.step': 'Step Form',
    'stepForm.title': 'Create a channel form',
    'stepForm.next': 'Next',
    'stepForm.prev': 'Prev',
    'stepForm.title.basicInfo': 'Basic Information',
    'stepForm.desc.basicInfo': 'Create event channel',
    'stepForm.title.channel': 'Channel Information',
    'stepForm.desc.channel': 'Enter detailed channel content',
    'stepForm.title.created': 'Complete creation',
    'stepForm.desc.created': 'Created successfully',
    'stepForm.basicInfo.name': 'Event name',
    'stepForm.basicInfo.name.required': 'Please enter the event name',
    'stepForm.basicInfo.name.placeholder':
      'Enter Chinese characters, letters or numbers, up to 20 characters',
    'stepForm.basicInfo.channelType': 'Channel Type',
    'stepForm.basicInfo.channelType.required': 'Please select the channel type',
    'stepForm.basicInfo.time': 'Promotion time',
    'stepForm.basicInfo.time.required': 'Please select the promotion time',
    'stepForm.basicInfo.link': 'Promotion URL',
    'stepForm.basicInfo.link.placeholder':
      'Please enter the promotion page address',
    'stepForm.basicInfo.link.tips':
      'Such as Android or iOS download address, intermediate redirect URL, the URL must start with http:// or https://',
    'stepForm.channel.source': 'Advertising source',
    'stepForm.channel.source.required': 'Please enter the advertising source',
    'stepForm.channel.source.placeholder': 'Referral address: sohu, sina',
    'stepForm.channel.media': 'Advertising medium',
    'stepForm.channel.media.required': 'Please enter the advertising medium',
    'stepForm.channel.media.placeholder': 'Marketing media: cpc, bannner, edm',
    'stepForm.channel.keywords': 'Key words',
    'stepForm.channel.remind': 'Push reminder',
    'stepForm.channel.content': 'Advertising content',
    'stepForm.channel.content.required': 'Please enter the advertising content',
    'stepForm.channel.content.placeholder':
      'Please enter the description of the advertisement content, no more than 200 words',
    'stepForm.created.success.title': 'Created successfully',
    'stepForm.created.success.desc': 'Form created successfully',
    'stepForm.created.success.view': 'View form',
    'stepForm.created.success.again': 'Create again',
    'stepForm.created.extra.title': 'Channel form description',
    'stepForm.created.extra.desc':
      'Advertiser channel promotion supports the tracking of users who place ads on third-party advertisers to download App users, such as launching App download advertisements on Toutiao channels, and tracking users who activate App by downloading on channels. ',
    'stepForm.created.extra.detail': 'Details',
  },
  'zh-CN': {
    'menu.form': '表单页',
    'menu.form.step': '分布表单',
    'stepForm.title': '创建渠道表单',
    'stepForm.next': '下一步',
    'stepForm.prev': '上一步',
    'stepForm.title.basicInfo': '基本信息',
    'stepForm.desc.basicInfo': '创建活动渠道',
    'stepForm.title.channel': '输入渠道信息',
    'stepForm.desc.channel': '输入详细的渠道内容',
    'stepForm.title.created': '完成创建',
    'stepForm.desc.created': '创建成功',
    'stepForm.basicInfo.name': '活动名称',
    'stepForm.basicInfo.name.required': '请输入活动名称',
    'stepForm.basicInfo.name.placeholder': '输入汉字、字母或数字，最多20字符',
    'stepForm.basicInfo.channelType': '渠道类型',
    'stepForm.basicInfo.channelType.required': '请选择渠道类型',
    'stepForm.basicInfo.time': '推广时间',
    'stepForm.basicInfo.time.required': '请选择推广时间',
    'stepForm.basicInfo.link': '推广地址',
    'stepForm.basicInfo.link.placeholder': '请输入推广页面地址',
    'stepForm.basicInfo.link.tips':
      '如 Android 或 iOS 的下载地址、中间跳转URL，网址必须以 http:// 或 https:// 开头',
    'stepForm.channel.source': '广告来源',
    'stepForm.channel.source.required': '请输入广告来源',
    'stepForm.channel.source.placeholder': '引荐来源地址：sohu、sina',
    'stepForm.channel.media': '广告媒介',
    'stepForm.channel.media.required': '请输入广告媒介',
    'stepForm.channel.media.placeholder': '营销媒介：cpc、bannner、edm',
    'stepForm.channel.keywords': '关键词',
    'stepForm.channel.remind': '推送提醒',
    'stepForm.channel.content': '广告内容',
    'stepForm.channel.content.required': '请输入广告内容',
    'stepForm.channel.content.placeholder':
      '请输入广告内容介绍，最多不超过200字',
    'stepForm.created.success.title': '创建成功',
    'stepForm.created.success.desc': '表单创建成功',
    'stepForm.created.success.view': '查看表单',
    'stepForm.created.success.again': '再次创建',
    'stepForm.created.extra.title': '渠道表单说明',
    'stepForm.created.extra.desc':
      '广告商渠道推广支持追踪在第三方广告商投放广告下载App用户的场景，例如在今日头条渠道投放下载App广告，追踪通过在渠道下载激活App的用户。',
    'stepForm.created.extra.detail': '查看详情',
  },
};

export default i18n;
