import { http, HttpResponse } from 'msw';
import dayjs from 'dayjs';

const searchTableHandlers = [
  http.get('/api/list/policy', ({ request }) => {
    const url = new URL(request.url);
    const query = Object.fromEntries(url.searchParams);

    // 生成基础数据
    let list = new Array(100).fill(0).map(() => {
      const id = `${Math.floor(
        10000000 + Math.random() * 90000000
      )}-${Math.floor(1000 + Math.random() * 9000)}`;

      return {
        id,
        name: ['每日推荐视频集', '抖音短视频候选集', '国际新闻集合'][
          Math.floor(Math.random() * 3)
        ],
        contentType: Math.floor(Math.random() * 3),
        filterType: Math.floor(Math.random() * 2),
        count: Math.floor(Math.random() * 2000),
        createdTime: Math.floor(Math.random() * 60) + 1,
        status: Math.floor(Math.random() * 2),
      };
    });

    // 处理筛选条件
    if (query.contentType && query.contentType !== 'all') {
      list = list.filter(
        (item) => item.contentType === Number(query.contentType)
      );
    }

    if (query.filterType && query.filterType !== 'all') {
      list = list.filter(
        (item) => item.filterType === Number(query.filterType)
      );
    }

    if (query.status && query.status !== 'all') {
      list = list.filter((item) => item.status === Number(query.status));
    }

    if (query.name) {
      list = list.filter((item) => item.name.includes(query.name));
    }

    // 处理排序
    if (query.sorter) {
      const [field, order] = query.sorter.split(':');
      list = list.sort((a, b) => {
        if (order === 'ascend') {
          return a[field] - b[field];
        }
        return b[field] - a[field];
      });
    }

    // 处理分页
    const page = Number(query.page) || 1;
    const pageSize = Number(query.pageSize) || 10;

    const start = (page - 1) * pageSize;
    const end = start + pageSize;

    return HttpResponse.json({
      list: list.slice(start, end).map((item) => ({
        ...item,
        createdTime: dayjs()
          .subtract(item.createdTime, 'days')
          .format('YYYY-MM-DD HH:mm:ss'),
      })),
      total: list.length,
    });
  }),
];

export default searchTableHandlers;
