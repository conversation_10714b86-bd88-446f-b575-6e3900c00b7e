import { R as u, r as m, g as d, a as i, _ as O } from './index.7dafa16d.js';
function s(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (a) {
        return Object.getOwnPropertyDescriptor(e, a).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function f(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? s(Object(t), !0).forEach(function (r) {
          O(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : s(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function v(e, n) {
  var t = m.exports.useContext(d),
    r = t.prefixCls,
    a = r === void 0 ? 'arco' : r,
    p = e.spin,
    l = e.className,
    o = f(
      f({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(l ? l + ' ' : '')
          .concat(a, '-icon ')
          .concat(a, '-icon-face-smile-fill'),
      }
    );
  return (
    p && (o.className = ''.concat(o.className, ' ').concat(a, '-icon-loading')),
    delete o.spin,
    delete o.isIcon,
    i('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...o,
      children: i('path', {
        fill: 'currentColor',
        fillRule: 'evenodd',
        stroke: 'none',
        d: 'M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm7.321-26.873a2.625 2.625 0 1 1 0 5.25 2.625 2.625 0 0 1 0-5.25Zm-14.646 0a2.625 2.625 0 1 1 0 5.25 2.625 2.625 0 0 1 0-5.25Zm-.355 9.953a1.91 1.91 0 0 1 2.694.177 6.66 6.66 0 0 0 5.026 2.279c1.918 0 3.7-.81 4.961-2.206a1.91 1.91 0 0 1 2.834 2.558 10.476 10.476 0 0 1-7.795 3.466 10.477 10.477 0 0 1-7.897-3.58 1.91 1.91 0 0 1 .177-2.694Z',
        clipRule: 'evenodd',
      }),
    })
  );
}
var c = u.forwardRef(v);
c.defaultProps = { isIcon: !0 };
c.displayName = 'IconFaceSmileFill';
var j = c;
export { j as I };
