<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_161_33222)">
<rect x="0.0283203" y="3.10522" width="12.9766" height="18.5206" rx="2.03077" fill="url(#paint0_linear_161_33222)"/>
<mask id="mask0_161_33222" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="3" y="0" width="21" height="24">
<path d="M5.41988 0.119385L15.1885 0.119384C15.8615 0.119384 16.5092 0.376475 16.9991 0.838099L22.4105 5.93776C22.9398 6.43659 23.2399 7.13171 23.2399 7.85905L23.2399 21.8994C23.2399 22.9929 22.3534 23.8794 21.2599 23.8794L5.41988 23.8794C4.32636 23.8794 3.43988 22.9929 3.43988 21.8994L3.43988 2.09938C3.43988 1.00586 4.32636 0.119385 5.41988 0.119385Z" fill="#4D72D3"/>
</mask>
<g mask="url(#mask0_161_33222)">
<g filter="url(#filter0_dii_161_33222)">
<path d="M5.41988 0.119385L15.1885 0.119384C15.8615 0.119384 16.5092 0.376475 16.9991 0.838099L22.4105 5.93776C22.9398 6.43659 23.2399 7.13171 23.2399 7.85905L23.2399 21.8994C23.2399 22.9929 22.3534 23.8794 21.2599 23.8794L5.41988 23.8794C4.32636 23.8794 3.43988 22.9929 3.43988 21.8994L3.43988 2.09938C3.43988 1.00586 4.32636 0.119385 5.41988 0.119385Z" fill="url(#paint1_linear_161_33222)"/>
</g>
<g filter="url(#filter1_dii_161_33222)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.2799 6.05945L23.2399 6.05945L23.2399 0.119451L17.2999 0.119452L17.2999 4.07945C17.2999 5.17297 18.1864 6.05945 19.2799 6.05945Z" fill="white"/>
</g>
</g>
<g filter="url(#filter2_dii_161_33222)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.2546 7.37437C11.5777 7.76209 11.5253 8.33833 11.1376 8.66144L8.71308 10.6818C8.51387 10.8479 8.25351 10.9214 7.99689 10.8842C7.74026 10.847 7.51153 10.7025 7.36769 10.4867L6.55952 9.27448C6.27956 8.85454 6.39304 8.28716 6.81298 8.0072C7.23292 7.72724 7.80029 7.84072 8.08025 8.26065L8.32429 8.62671L9.96751 7.25737C10.3552 6.93426 10.9315 6.98665 11.2546 7.37437Z" fill="white"/>
</g>
<g filter="url(#filter3_dii_161_33222)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.8708 9.57522C12.8708 9.07052 13.28 8.66138 13.7847 8.66138L19.4418 8.66138C19.9465 8.66138 20.3557 9.07052 20.3557 9.57522C20.3557 10.0799 19.9465 10.4891 19.4418 10.4891L13.7847 10.4891C13.28 10.4891 12.8708 10.0799 12.8708 9.57522Z" fill="white"/>
</g>
<g filter="url(#filter4_dii_161_33222)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.2549 15.2324C11.2549 14.7277 11.664 14.3186 12.1687 14.3186L19.4422 14.3186C19.9469 14.3186 20.356 14.7277 20.356 15.2324C20.356 15.7372 19.9469 16.1463 19.4422 16.1463L12.1687 16.1463C11.664 16.1463 11.2549 15.7372 11.2549 15.2324Z" fill="white"/>
</g>
<g filter="url(#filter5_dii_161_33222)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.21375 15.2324C7.21375 14.7277 7.60556 14.3186 8.08889 14.3186L8.16629 14.3186C8.64962 14.3186 9.04144 14.7277 9.04144 15.2324C9.04144 15.7372 8.64962 16.1463 8.16629 16.1463L8.08889 16.1463C7.60556 16.1463 7.21375 15.7372 7.21375 15.2324Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_dii_161_33222" x="-5.56012" y="-4.88062" width="37.8" height="41.76" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.697798 0 0 0 0 0.346806 0 0 0 0 0.945833 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_161_33222"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_161_33222" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.131148"/>
<feGaussianBlur stdDeviation="0.0327869"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.968627 0 0 0 0 0.6 0 0 0 0 0.984314 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_161_33222"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.131148"/>
<feGaussianBlur stdDeviation="0.131148"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_161_33222" result="effect3_innerShadow_161_33222"/>
</filter>
<filter id="filter1_dii_161_33222" x="11.2854" y="-2.61439" width="17.9691" height="17.9691" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.28067"/>
<feGaussianBlur stdDeviation="3.00728"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.0450001 0 0 0 0 0.45 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_161_33222"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_161_33222" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.18712"/>
<feGaussianBlur stdDeviation="1.36695"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.690196 0 0 0 0 0.776941 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_161_33222"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.406154"/>
<feGaussianBlur stdDeviation="0.546779"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_161_33222" result="effect3_innerShadow_161_33222"/>
</filter>
<filter id="filter2_dii_161_33222" x="0.391376" y="4.31164" width="17.0896" height="15.8773" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.28067"/>
<feGaussianBlur stdDeviation="3.00728"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00314236 0 0 0 0 0.0782449 0 0 0 0 0.754167 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_161_33222"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_161_33222" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.21846"/>
<feGaussianBlur stdDeviation="1.36695"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.57 0 0 0 0 0.6308 0 0 0 0 0.95 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_161_33222"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.820168"/>
<feGaussianBlur stdDeviation="0.546779"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_161_33222" result="effect3_innerShadow_161_33222"/>
</filter>
<filter id="filter3_dii_161_33222" x="6.85628" y="5.92748" width="19.514" height="13.8568" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.28067"/>
<feGaussianBlur stdDeviation="3.00728"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00314236 0 0 0 0 0.0782449 0 0 0 0 0.754167 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_161_33222"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_161_33222" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.21846"/>
<feGaussianBlur stdDeviation="1.36695"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.57 0 0 0 0 0.6308 0 0 0 0 0.95 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_161_33222"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.820168"/>
<feGaussianBlur stdDeviation="0.546779"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_161_33222" result="effect3_innerShadow_161_33222"/>
</filter>
<filter id="filter4_dii_161_33222" x="5.24031" y="11.5847" width="21.1303" height="13.8568" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.28067"/>
<feGaussianBlur stdDeviation="3.00728"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00314236 0 0 0 0 0.0782449 0 0 0 0 0.754167 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_161_33222"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_161_33222" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.21846"/>
<feGaussianBlur stdDeviation="1.36695"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.57 0 0 0 0 0.6308 0 0 0 0 0.95 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_161_33222"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.820168"/>
<feGaussianBlur stdDeviation="0.546779"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_161_33222" result="effect3_innerShadow_161_33222"/>
</filter>
<filter id="filter5_dii_161_33222" x="3.15221" y="13.1001" width="9.95078" height="10.3883" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.28067"/>
<feGaussianBlur stdDeviation="2.03077"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.0450001 0 0 0 0 0.45 0 0 0 0.65 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_161_33222"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_161_33222" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.21846"/>
<feGaussianBlur stdDeviation="1.36695"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.57 0 0 0 0 0.6308 0 0 0 0 0.95 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_161_33222"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.820168"/>
<feGaussianBlur stdDeviation="0.546779"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_161_33222" result="effect3_innerShadow_161_33222"/>
</filter>
<linearGradient id="paint0_linear_161_33222" x1="1.97655" y1="3.10522" x2="1.97655" y2="16.0647" gradientUnits="userSpaceOnUse">
<stop stop-color="#E14BFE"/>
<stop offset="1" stop-color="#B84FD1"/>
</linearGradient>
<linearGradient id="paint1_linear_161_33222" x1="3.43988" y1="0.119385" x2="3.43988" y2="23.8794" gradientUnits="userSpaceOnUse">
<stop stop-color="#E982FE"/>
<stop offset="1" stop-color="#B353FF"/>
</linearGradient>
<clipPath id="clip0_161_33222">
<rect width="24" height="24" fill="white" transform="matrix(1 -8.74228e-08 -8.74228e-08 -1 0 24)"/>
</clipPath>
</defs>
</svg>
