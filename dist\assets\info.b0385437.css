.arco-cascader .arco-cascader-view {
  color: var(--color-text-1);
  background-color: var(--color-fill-2);
  border: 1px solid transparent;
}
.arco-cascader:hover .arco-cascader-view {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-cascader.arco-cascader-focused .arco-cascader-view {
  color: var(--color-text-1);
  background-color: var(--color-bg-2);
  border-color: rgb(var(--primary-6));
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-cascader .arco-cascader-suffix-icon,
.arco-cascader .arco-cascader-loading-icon,
.arco-cascader .arco-cascader-search-icon,
.arco-cascader .arco-cascader-clear-icon,
.arco-cascader .arco-cascader-arrow-icon,
.arco-cascader .arco-cascader-expand-icon {
  color: var(--color-text-2);
}
.arco-cascader-error .arco-cascader-view {
  background-color: var(--color-danger-light-1);
  border: 1px solid transparent;
}
.arco-cascader-error:hover .arco-cascader-view {
  background-color: var(--color-danger-light-2);
  border-color: transparent;
}
.arco-cascader-error.arco-cascader-focused .arco-cascader-view {
  color: var(--color-text-1);
  background-color: var(--color-bg-2);
  border-color: rgb(var(--danger-6));
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-cascader-warning .arco-cascader-view {
  background-color: var(--color-warning-light-1);
  border: 1px solid transparent;
}
.arco-cascader-warning:hover .arco-cascader-view {
  background-color: var(--color-warning-light-2);
  border-color: transparent;
}
.arco-cascader-warning.arco-cascader-focused .arco-cascader-view {
  color: var(--color-text-1);
  background-color: var(--color-bg-2);
  border-color: rgb(var(--warning-6));
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-cascader-disabled .arco-cascader-view {
  color: var(--color-text-4);
  background-color: var(--color-fill-2);
  border: 1px solid transparent;
}
.arco-cascader-disabled:hover .arco-cascader-view {
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-cascader-disabled .arco-cascader-suffix-icon,
.arco-cascader-disabled .arco-cascader-loading-icon,
.arco-cascader-disabled .arco-cascader-search-icon,
.arco-cascader-disabled .arco-cascader-clear-icon,
.arco-cascader-disabled .arco-cascader-arrow-icon,
.arco-cascader-disabled .arco-cascader-expand-icon {
  color: var(--color-text-4);
}
.arco-cascader-no-border .arco-cascader-view {
  border: none !important;
  background: none !important;
}
.arco-cascader-size-mini.arco-cascader-multiple .arco-cascader-view {
  height: auto;
  font-size: 12px;
  padding: 0 3px;
  line-height: 0;
}
.arco-cascader-size-mini.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-cascader-size-mini.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  box-sizing: border-box;
  padding-left: 4px;
}
.arco-cascader-size-mini.arco-cascader-multiple .arco-cascader-suffix {
  padding-right: 4px;
}
.arco-cascader-size-mini.arco-cascader-multiple input {
  font-size: 12px;
}
.arco-cascader-size-mini.arco-cascader-single .arco-cascader-view {
  height: 24px;
  line-height: 22px;
  font-size: 12px;
  padding: 0 7px;
}
.arco-cascader-size-mini.arco-cascader-single input {
  font-size: 12px;
}
.arco-cascader-size-mini.arco-cascader-multiple
  .arco-cascader-view-with-prefix {
  padding-left: 7px;
}
.arco-cascader-size-small.arco-cascader-multiple .arco-cascader-view {
  height: auto;
  font-size: 14px;
  padding: 0 3px;
  line-height: 0;
}
.arco-cascader-size-small.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-cascader-size-small.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  box-sizing: border-box;
  padding-left: 8px;
}
.arco-cascader-size-small.arco-cascader-multiple .arco-cascader-suffix {
  padding-right: 8px;
}
.arco-cascader-size-small.arco-cascader-multiple input {
  font-size: 14px;
}
.arco-cascader-size-small.arco-cascader-single .arco-cascader-view {
  height: 28px;
  line-height: 26px;
  font-size: 14px;
  padding: 0 11px;
}
.arco-cascader-size-small.arco-cascader-single input {
  font-size: 14px;
}
.arco-cascader-size-small.arco-cascader-multiple
  .arco-cascader-view-with-prefix {
  padding-left: 11px;
}
.arco-cascader-size-default.arco-cascader-multiple .arco-cascader-view {
  height: auto;
  font-size: 14px;
  padding: 0 3px;
  line-height: 0;
}
.arco-cascader-size-default.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-cascader-size-default.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  box-sizing: border-box;
  padding-left: 8px;
}
.arco-cascader-size-default.arco-cascader-multiple .arco-cascader-suffix {
  padding-right: 8px;
}
.arco-cascader-size-default.arco-cascader-multiple input {
  font-size: 14px;
}
.arco-cascader-size-default.arco-cascader-single .arco-cascader-view {
  height: 32px;
  line-height: 30px;
  font-size: 14px;
  padding: 0 11px;
}
.arco-cascader-size-default.arco-cascader-single input {
  font-size: 14px;
}
.arco-cascader-size-default.arco-cascader-multiple
  .arco-cascader-view-with-prefix {
  padding-left: 11px;
}
.arco-cascader-size-large.arco-cascader-multiple .arco-cascader-view {
  height: auto;
  font-size: 16px;
  padding: 0 3px;
  line-height: 0;
}
.arco-cascader-size-large.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-cascader-size-large.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  box-sizing: border-box;
  padding-left: 12px;
}
.arco-cascader-size-large.arco-cascader-multiple .arco-cascader-suffix {
  padding-right: 12px;
}
.arco-cascader-size-large.arco-cascader-multiple input {
  font-size: 16px;
}
.arco-cascader-size-large.arco-cascader-single .arco-cascader-view {
  height: 36px;
  line-height: 34px;
  font-size: 16px;
  padding: 0 15px;
}
.arco-cascader-size-large.arco-cascader-single input {
  font-size: 16px;
}
.arco-cascader-size-large.arco-cascader-multiple
  .arco-cascader-view-with-prefix {
  padding-left: 15px;
}
.arco-cascader {
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  cursor: pointer;
}
.arco-cascader-view {
  display: flex;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  border-radius: var(--border-radius-small);
  outline: none;
  user-select: none;
  text-align: left;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1), padding 0s linear;
}
.arco-cascader-view input {
  color: inherit;
  cursor: inherit;
}
.arco-cascader-view input::placeholder {
  color: var(--color-text-3);
}
.arco-cascader-view input[disabled] {
  pointer-events: none;
}
.arco-cascader-multiple,
.arco-cascader-show-search {
  cursor: text;
}
.arco-cascader-disabled {
  cursor: not-allowed;
}
.arco-cascader-disabled .arco-cascader-view input::placeholder {
  color: var(--color-text-4);
}
.arco-cascader-single .arco-cascader-view-input {
  box-sizing: border-box;
  width: 100%;
  padding: 0;
  border: none;
  outline: none;
  background: transparent;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-cascader-single .arco-cascader-view-selector {
  position: relative;
  display: inline-flex;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
}
.arco-cascader-single .arco-cascader-view-selector .arco-cascader-view-input {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}
.arco-cascader-single
  .arco-cascader-view-selector
  .arco-cascader-view-value-mirror {
  opacity: 0;
}
.arco-cascader-single .arco-cascader-view-value,
.arco-cascader-single .arco-cascader-view-value-mirror {
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-cascader-single .arco-cascader-view-value:after,
.arco-cascader-single .arco-cascader-view-value-mirror:after {
  content: '.';
  font-size: 0;
  line-height: 0;
  visibility: hidden;
}
.arco-cascader-single .arco-cascader-view .arco-cascader-hidden {
  opacity: 0;
  position: absolute;
  z-index: -1;
}
.arco-cascader-multiple {
  vertical-align: top;
}
.arco-cascader-multiple .arco-cascader-view {
  padding: 0 4px;
  line-height: 0;
}
.arco-cascader-multiple .arco-cascader-view-with-prefix {
  padding-left: 12px;
}
.arco-cascader-multiple .arco-input-tag {
  flex: 1;
  padding: 0;
  border: none !important;
  background: none !important;
  box-shadow: none !important;
  overflow: hidden;
}
.arco-cascader-multiple .arco-tag {
  max-width: 100%;
}
.arco-cascader-multiple:not(.arco-cascader-focused)
  .arco-input-tag
  input:not(:first-child)[value=''] {
  opacity: 0;
  position: absolute;
  z-index: -1;
}
.arco-cascader-prefix {
  display: flex;
  align-items: center;
  margin-right: 12px;
  white-space: nowrap;
  color: var(--color-text-2);
}
.arco-cascader-suffix {
  display: flex;
  align-items: center;
  margin-left: 4px;
}
.arco-cascader-suffix-icon,
.arco-cascader-search-icon,
.arco-cascader-loading-icon,
.arco-cascader-expand-icon,
.arco-cascader-clear-icon {
  font-size: 12px;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-cascader-arrow-icon {
  font-size: 12px;
}
.arco-cascader-open .arco-cascader-arrow-icon svg {
  transform: rotate(180deg);
}
.arco-cascader .arco-cascader-clear-icon {
  display: none;
  cursor: pointer;
}
.arco-cascader .arco-cascader-clear-icon > svg {
  position: relative;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-cascader:hover .arco-cascader-clear-icon {
  display: block;
}
.arco-cascader:hover .arco-cascader-clear-icon ~ * {
  display: none;
}
.arco-cascader-wrapper {
  display: inline-flex;
  align-items: stretch;
  width: 100%;
}
.arco-cascader-wrapper .arco-cascader {
  min-width: 0;
}
.arco-cascader-wrapper
  .arco-cascader:not(.arco-cascader-focused):not(:first-child)
  .arco-cascader-view {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.arco-cascader-wrapper
  .arco-cascader:not(.arco-cascader-focused):not(:last-child)
  .arco-cascader-view {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.arco-cascader-addbefore {
  display: flex;
  align-items: center;
  padding: 0 12px;
  color: var(--color-text-1);
  background-color: var(--color-fill-2);
  white-space: nowrap;
  border: 1px solid transparent;
}
.arco-cascader-addbefore {
  border-right: 1px solid var(--color-border-2);
  border-top-left-radius: var(--border-radius-small);
  border-bottom-left-radius: var(--border-radius-small);
}
.arco-cascader-rtl.arco-cascader-size-mini.arco-cascader-multiple
  .arco-cascader-view-with-prefix {
  padding-left: 0;
  padding-right: 7px;
}
.arco-cascader-rtl.arco-cascader-size-mini.arco-cascader-multiple
  .arco-cascader-suffix {
  padding-right: 0;
  padding-left: 4px;
}
.arco-cascader-rtl.arco-cascader-size-mini.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-cascader-rtl.arco-cascader-size-mini.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  padding-left: 0;
  padding-right: 4px;
}
.arco-cascader-rtl.arco-cascader-size-small.arco-cascader-multiple
  .arco-cascader-view-with-prefix {
  padding-left: 0;
  padding-right: 11px;
}
.arco-cascader-rtl.arco-cascader-size-small.arco-cascader-multiple
  .arco-cascader-suffix {
  padding-right: 0;
  padding-left: 8px;
}
.arco-cascader-rtl.arco-cascader-size-small.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-cascader-rtl.arco-cascader-size-small.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  padding-left: 0;
  padding-right: 8px;
}
.arco-cascader-rtl.arco-cascader-size-default.arco-cascader-multiple
  .arco-cascader-view-with-prefix {
  padding-left: 0;
  padding-right: 11px;
}
.arco-cascader-rtl.arco-cascader-size-default.arco-cascader-multiple
  .arco-cascader-suffix {
  padding-right: 0;
  padding-left: 8px;
}
.arco-cascader-rtl.arco-cascader-size-default.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-cascader-rtl.arco-cascader-size-default.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  padding-left: 0;
  padding-right: 8px;
}
.arco-cascader-rtl.arco-cascader-size-large.arco-cascader-multiple
  .arco-cascader-view-with-prefix {
  padding-left: 0;
  padding-right: 15px;
}
.arco-cascader-rtl.arco-cascader-size-large.arco-cascader-multiple
  .arco-cascader-suffix {
  padding-right: 0;
  padding-left: 12px;
}
.arco-cascader-rtl.arco-cascader-size-large.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-cascader-rtl.arco-cascader-size-large.arco-cascader-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  padding-left: 0;
  padding-right: 12px;
}
.arco-cascader-wrapper-rtl .arco-cascader-addbefore {
  border-right: unset;
  border-left: 1px solid var(--color-border-2);
}
.arco-cascader-rtl .arco-cascader-view {
  text-align: right;
}
.arco-cascader-rtl .arco-cascader-multiple .arco-cascader-view-with-prefix {
  padding-left: 0;
  padding-right: 12px;
}
.arco-cascader-rtl .arco-cascader-prefix {
  margin-right: 0;
  margin-left: 12px;
}
.arco-cascader-rtl .arco-cascader-suffix {
  margin-left: 0;
  margin-right: 4px;
}
.arco-cascader-popup {
  top: 4px;
  box-sizing: border-box;
  border: 1px solid var(--color-fill-3);
  border-radius: var(--border-radius-medium);
  background-color: var(--color-bg-popup);
  box-shadow: 0 4px 10px #0000001a;
  overflow: hidden;
}
.arco-cascader-popup-trigger-hover .arco-cascader-list-item {
  transition: font-weight 0s;
}
.arco-cascader-popup .arco-cascader-popup-inner {
  width: 100%;
  white-space: nowrap;
  list-style: none;
  height: 200px;
}
.arco-cascader-highlight {
  font-weight: 500;
}
.arco-cascader-list-column {
  position: relative;
  vertical-align: top;
  display: inline-block;
  background-color: var(--color-bg-popup);
  height: 100%;
}
.arco-cascader-list-column-virtual {
  width: 120px;
}
.arco-cascader-list-column:not(:last-of-type) {
  border-right: 1px solid var(--color-fill-3);
}
.arco-cascader-list-wrapper {
  position: relative;
  white-space: nowrap;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  padding: 4px 0;
  flex-direction: column;
}
.arco-cascader-list-wrapper-with-footer {
  padding-bottom: 0;
}
.arco-cascader-list-empty {
  height: 100%;
  display: flex;
  align-items: center;
}
.arco-cascader-list {
  padding: 0;
  margin: 0;
  list-style: none;
  box-sizing: border-box;
  overflow-y: auto;
  flex: 1;
}
.arco-cascader-list-item,
.arco-cascader-list-search-item {
  position: relative;
  height: 36px;
  line-height: 36px;
  min-width: 100px;
  font-size: 14px;
  color: var(--color-text-1);
  box-sizing: border-box;
  display: flex;
  cursor: pointer;
  background-color: transparent;
}
.arco-cascader-list-item-label,
.arco-cascader-list-search-item-label {
  flex-grow: 1;
  padding-left: 12px;
  padding-right: 34px;
}
.arco-cascader-list-item .arco-icon-right,
.arco-cascader-list-search-item .arco-icon-right,
.arco-cascader-list-item .arco-icon-check,
.arco-cascader-list-search-item .arco-icon-check {
  position: absolute;
  color: var(--color-text-2);
  top: 50%;
  font-size: 12px;
  transform: translateY(-50%);
  right: 10px;
}
.arco-cascader-list-item .arco-icon-check,
.arco-cascader-list-search-item .arco-icon-check {
  color: rgb(var(--primary-6));
}
.arco-cascader-list-item .arco-icon-loading,
.arco-cascader-list-search-item .arco-icon-loading {
  position: absolute;
  margin-top: -6px;
  top: 50%;
  font-size: 12px;
  right: 10px;
  color: rgb(var(--primary-6));
}
.arco-cascader-list-item:hover,
.arco-cascader-list-search-item-hover {
  color: var(--color-text-1);
  background-color: var(--color-fill-2);
}
.arco-cascader-list-item:hover .arco-checkbox input,
.arco-cascader-list-search-item-hover .arco-checkbox input {
  display: none;
}
.arco-cascader-list-item:hover
  .arco-checkbox:not(.arco-checkbox-disabled):not(.arco-checkbox-checked):hover
  .arco-checkbox-icon-hover:before,
.arco-cascader-list-search-item-hover
  .arco-checkbox:not(.arco-checkbox-disabled):not(.arco-checkbox-checked):hover
  .arco-checkbox-icon-hover:before {
  background-color: var(--color-fill-3);
}
.arco-cascader-list-item-disabled,
.arco-cascader-list-search-item-disabled,
.arco-cascader-list-item-disabled:hover,
.arco-cascader-list-search-item-disabled:hover {
  cursor: not-allowed;
  background-color: var(--color-fill-2);
  color: var(--color-text-4);
}
.arco-cascader-list-item-disabled .arco-icon-right,
.arco-cascader-list-search-item-disabled .arco-icon-right,
.arco-cascader-list-item-disabled:hover .arco-icon-right,
.arco-cascader-list-search-item-disabled:hover .arco-icon-right {
  color: inherit;
}
.arco-cascader-list-item-disabled .arco-icon-check,
.arco-cascader-list-search-item-disabled .arco-icon-check,
.arco-cascader-list-item-disabled:hover .arco-icon-check,
.arco-cascader-list-search-item-disabled:hover .arco-icon-check {
  color: var(--color-primary-light-3);
}
.arco-cascader-list-item-active {
  transition: all 0.2s cubic-bezier(0, 0, 1, 1);
  background-color: var(--color-fill-2);
  color: var(--color-text-1);
  font-weight: 500;
}
.arco-cascader-list-item-active:hover {
  background-color: var(--color-fill-2);
  font-weight: 500;
  color: var(--color-text-1);
}
.arco-cascader-list-item-active.arco-cascader-list-item-disabled,
.arco-cascader-list-item-active.arco-cascader-list-item-disabled:hover {
  background-color: var(--color-fill-2);
  font-weight: 500;
  color: var(--color-text-4);
}
.arco-cascader-list-multiple .arco-cascader-list-item-label {
  padding-left: 0;
}
.arco-cascader-list-multiple .arco-cascader-list-item,
.arco-cascader-list-multiple .arco-cascader-list-search-item {
  padding-left: 12px;
}
.arco-cascader-list-multiple .arco-cascader-list-item .arco-checkbox,
.arco-cascader-list-multiple .arco-cascader-list-search-item .arco-checkbox {
  padding-left: 0;
  margin-right: 8px;
}
.arco-cascader-list-search.arco-cascader-list-multiple
  .arco-cascader-list-item-label {
  padding-right: 12px;
}
.arco-cascader-list-footer {
  height: 36px;
  line-height: 36px;
  padding-left: 12px;
  border-top: 1px solid var(--color-fill-3);
  box-sizing: border-box;
}
.cascaderSlide-enter-active,
.cascaderSlide-appear-active {
  transition: margin 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-cascader-list-column-rtl {
  direction: rtl;
}
.arco-cascader-list-column-rtl:not(:last-of-type) {
  border-left: 1px solid var(--color-fill-3);
  border-right: none;
}
.arco-cascader-list-rtl .arco-cascader-list-item-label,
.arco-cascader-list-rtl .arco-cascader-list-search-item-label {
  padding-left: 34px;
  padding-right: 12px;
}
.arco-cascader-list-rtl .arco-cascader-list-item .arco-icon-left,
.arco-cascader-list-rtl .arco-cascader-list-search-item .arco-icon-left,
.arco-cascader-list-rtl .arco-cascader-list-item .arco-icon-check,
.arco-cascader-list-rtl .arco-cascader-list-search-item .arco-icon-check {
  position: absolute;
  color: var(--color-text-2);
  top: 50%;
  font-size: 12px;
  transform: translateY(-50%);
  right: initial;
  left: 10px;
}
.arco-cascader-list-rtl .arco-cascader-list-footer {
  padding-left: 0;
  padding-right: 12px;
}
.arco-cascader-list-rtl.arco-cascader-multiple .arco-cascader-list-item-label {
  padding-right: 0;
}
.arco-cascader-list-rtl.arco-cascader-multiple .arco-cascader-list-item,
.arco-cascader-list-rtl.arco-cascader-multiple .arco-cascader-list-search-item {
  padding-right: 12px;
}
.arco-cascader-list-rtl.arco-cascader-multiple
  .arco-cascader-list-item
  .arco-checkbox,
.arco-cascader-list-rtl.arco-cascader-multiple
  .arco-cascader-list-search-item
  .arco-checkbox {
  padding-right: 0;
  margin-left: 8px;
}
.arco-cascader-list-rtl.arco-cascader-multiple.arco-cascader-list-search
  .arco-cascader-list-item-label {
  padding-left: 12px;
  padding-right: 0;
}
