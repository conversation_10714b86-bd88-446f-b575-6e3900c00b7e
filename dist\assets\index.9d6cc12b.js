import { R as u, r as b, g as d, a as i, _ as O } from './index.7dafa16d.js';
function l(e, n) {
  var r = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var t = Object.getOwnPropertySymbols(e);
    n &&
      (t = t.filter(function (o) {
        return Object.getOwnPropertyDescriptor(e, o).enumerable;
      })),
      r.push.apply(r, t);
  }
  return r;
}
function p(e) {
  for (var n = 1; n < arguments.length; n++) {
    var r = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? l(Object(r), !0).forEach(function (t) {
          O(e, t, r[t]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r))
      : l(Object(r)).forEach(function (t) {
          Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(r, t));
        });
  }
  return e;
}
function g(e, n) {
  var r = b.exports.useContext(d),
    t = r.prefixCls,
    o = t === void 0 ? 'arco' : t,
    f = e.spin,
    s = e.className,
    a = p(
      p({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(s ? s + ' ' : '')
          .concat(o, '-icon ')
          .concat(o, '-icon-double-right'),
      }
    );
  return (
    f && (a.className = ''.concat(a.className, ' ').concat(o, '-icon-loading')),
    delete a.spin,
    delete a.isIcon,
    i('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...a,
      children: i('path', {
        d: 'm11.143 38.1 14.142-14.142L11.143 9.816M22.456 38.1l14.142-14.142L22.456 9.816',
      }),
    })
  );
}
var c = u.forwardRef(g);
c.defaultProps = { isIcon: !0 };
c.displayName = 'IconDoubleRight';
var h = c;
export { h as I };
