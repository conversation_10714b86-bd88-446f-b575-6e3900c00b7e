import {
  u as y,
  r as s,
  l as E,
  j as i,
  a as e,
  h as m,
  B as u,
  V as D,
  c as A,
} from './index.7dafa16d.js';
import { C as B } from './index.519f9d90.js';
import { T as F } from './index.503eee4e.js';
import { P } from './index.1446e701.js';
import { i as w, S as z } from './form.b1e2bba4.js';
import { g as j, s as I } from './constants.9707109f.js';
import { I as L } from './index.5ef0222d.js';
import './b-tween.es.d368a2a1.js';
import './index.7b192bca.js';
import './pad.af73d6a9.js';
import './index.9d6cc12b.js';
const { Title: N } = A,
  H = [
    '\u56FE\u6587',
    '\u6A2A\u7248\u77ED\u89C6\u9891',
    '\u7AD6\u7248\u77ED\u89C6\u9891',
  ],
  Q = ['\u89C4\u5219\u7B5B\u9009', '\u4EBA\u5DE5'],
  U = ['\u5DF2\u4E0A\u7EBF', '\u672A\u4E0A\u7EBF'];
function X() {
  const o = y(w),
    h = async (a, r) => {
      console.log(a, r);
    },
    d = s.exports.useMemo(() => j(o, h), [o]),
    [g, f] = s.exports.useState([]),
    [t, n] = s.exports.useState({
      sizeCanChange: !0,
      showTotal: !0,
      pageSize: 10,
      current: 1,
      pageSizeChangeResetCurrent: !0,
    }),
    [S, c] = s.exports.useState(!0),
    [l, C] = s.exports.useState({});
  s.exports.useEffect(() => {
    T();
  }, [t.current, t.pageSize, JSON.stringify(l)]);
  function T() {
    const { current: a, pageSize: r } = t;
    c(!0),
      E.get('/api/list', { params: { page: a, pageSize: r, ...l } }).then(
        (p) => {
          f(p.data.list),
            n({ ...t, current: a, pageSize: r, total: p.data.total }),
            c(!1);
        }
      );
  }
  function b({ current: a, pageSize: r }) {
    n({ ...t, current: a, pageSize: r });
  }
  function x(a) {
    n({ ...t, current: 1 }), C(a);
  }
  return i(B, {
    children: [
      e(N, { heading: 6, children: o['menu.list.searchTable'] }),
      e(z, { onSearch: x }),
      e(P, {
        requiredPermissions: [
          { resource: 'menu.list.searchTable', actions: ['write'] },
        ],
        children: i('div', {
          className: I['button-group'],
          children: [
            i(m, {
              children: [
                e(u, {
                  type: 'primary',
                  icon: e(D, {}),
                  children: o['searchTable.operations.add'],
                }),
                e(u, { children: o['searchTable.operations.upload'] }),
              ],
            }),
            e(m, {
              children: e(u, {
                icon: e(L, {}),
                children: o['searchTable.operation.download'],
              }),
            }),
          ],
        }),
      }),
      e(F, {
        rowKey: 'id',
        loading: S,
        onChange: b,
        pagination: t,
        columns: d,
        data: g,
      }),
    ],
  });
}
export { H as ContentType, Q as FilterType, U as Status, X as default };
