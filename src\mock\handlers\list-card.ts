import { http, HttpResponse } from 'msw';
import dayjs from 'dayjs';

const qualityCategory = ['视频类', '图文类', '纯文本'];
const qualityName = ['历史导入', '内容版权', '敏感内容', '商业品牌'];

const serviceName = [
  '漏斗分析',
  '用户分布',
  '资源分发',
  '用户画像分析',
  '事件分析',
];

const serviceDescriptions = [
  '用户行为分析之漏斗分析模型是企业实现精细化运营、进行用户行为分析的重要数据分析模型。 ',
  '快速诊断用户人群，地域细分情况，了解数据分布的集中度，以及主要的数据分布的区间段是什么。',
  '移动端动态化资源分发解决方案。提供稳定大流量服务支持、灵活定制的分发圈选规则，通过离线化预加载。  ',
  '用户画像就是将典型用户信息标签化，根据用户特征、业务场景和用户行为等信息，构建一个标签化的用户模型。 ',
  '事件分析即跟踪用户的交互行为事件，简单理解就是当用户触发了网页或者 app 上的功能时，记录下来的用户行为。',
];

const listCardHandlers = [
  http.get('/api/list/quality-inspection', ({ request }) => {
    const { page = 1, pageSize = 10 } = Object.fromEntries(
      new URL(request.url).searchParams
    );

    const list = new Array(pageSize).fill(null).map((_, index) => {
      const id = index + 1 + (Number(page) - 1) * Number(pageSize);

      return {
        id,
        name: qualityName[Math.floor(Math.random() * qualityName.length)],
        category:
          qualityCategory[Math.floor(Math.random() * qualityCategory.length)],
        reviewStatus: Math.round(Math.random() * 2),
        createdTime: dayjs()
          .subtract(Math.floor(Math.random() * 60), 'days')
          .format('YYYY-MM-DD HH:mm:ss'),
      };
    });

    return HttpResponse.json({
      list,
      total: 50,
    });
  }),

  http.get('/api/list/quality-inspection/:id', ({ params }) => {
    const { id } = params;
    return HttpResponse.json({
      id,
      name: qualityName[Math.floor(Math.random() * qualityName.length)],
      description:
        '根据历史数据，对用户的操作行为进行分析，建立用户画像，帮助产品和运营更好地了解用户，提升产品体验。',
      category:
        qualityCategory[Math.floor(Math.random() * qualityCategory.length)],
      createdTime: dayjs()
        .subtract(Math.floor(Math.random() * 60), 'days')
        .format('YYYY-MM-DD HH:mm:ss'),
      deadline: dayjs()
        .add(Math.floor(Math.random() * 30) + 1, 'days')
        .format('YYYY-MM-DD HH:mm:ss'),
      participants: new Array(Math.floor(Math.random() * 5) + 1)
        .fill(null)
        .map(() => ({
          name: ['张三', '李四', '王五', '赵六', '钱七'][
            Math.floor(Math.random() * 5)
          ],
          avatar: '',
        })),
      status: Math.floor(Math.random() * 3),
    });
  }),

  http.get('/api/list/service', ({ request }) => {
    const { page = 1, pageSize = 10 } = Object.fromEntries(
      new URL(request.url).searchParams
    );

    const list = new Array(pageSize).fill(null).map((_, index) => {
      const id = index + 1 + (Number(page) - 1) * Number(pageSize);
      const nameIndex = Math.floor(Math.random() * serviceName.length);

      return {
        id,
        icon: 'code',
        title: serviceName[nameIndex],
        description: serviceDescriptions[nameIndex],
        status: Math.round(Math.random()),
        created_at: dayjs()
          .subtract(Math.floor(Math.random() * 60), 'days')
          .format('YYYY-MM-DD HH:mm:ss'),
      };
    });

    return HttpResponse.json({
      list,
      total: 24,
    });
  }),
];

export default listCardHandlers;
