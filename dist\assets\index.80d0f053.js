import {
  u as o,
  a as e,
  j as u,
  m as s,
  B as i,
  c as a,
  L as l,
  aM as n,
} from './index.7dafa16d.js';
const d = {
    'en-US': {
      'menu.result': 'Result',
      'menu.result.error': 'Error',
      'error.result.title': 'Submit Error',
      'error.result.subTitle':
        'Please check the modified information and try again',
      'error.result.goBack': 'GoBack',
      'error.result.retry': 'Retry',
      'error.detailTitle': 'Details of Error',
      'error.detailLine.record':
        'The current domain name has not been registered, please check the registration process: ',
      'error.detailLine.record.link': 'Registration Process',
      'error.detailLine.auth':
        'Your user group does not have the authority to perform this operation;',
      'error.detailLine.auth.link': 'Request for access',
    },
    'zh-CN': {
      'menu.result': '\u7ED3\u679C\u9875',
      'menu.result.error': '\u5931\u8D25\u9875',
      'error.result.title': '\u63D0\u4EA4\u5931\u8D25',
      'error.result.subTitle':
        '\u8BF7\u6838\u5BF9\u4FEE\u6539\u4FE1\u606F\u540E\uFF0C\u518D\u91CD\u8BD5',
      'error.result.goBack': '\u56DE\u5230\u9996\u9875',
      'error.result.retry': '\u8FD4\u56DE\u4FEE\u6539',
      'error.detailTitle': '\u9519\u8BEF\u8BE6\u60C5',
      'error.detailLine.record':
        '\u5F53\u524D\u57DF\u540D\u672A\u5907\u6848\uFF0C\u5907\u6848\u6D41\u7A0B\u8BF7\u67E5\u770B\uFF1A',
      'error.detailLine.record.link': '\u5907\u6848\u6D41\u7A0B',
      'error.detailLine.auth':
        '\u4F60\u7684\u7528\u6237\u7EC4\u4E0D\u5177\u6709\u8FDB\u884C\u6B64\u64CD\u4F5C\u7684\u6743\u9650\uFF1B',
      'error.detailLine.auth.link': '\u7533\u8BF7\u6743\u9650',
    },
  },
  c = '_wrapper_i1abw_1',
  h = '_result_i1abw_7';
var t = {
  wrapper: c,
  result: h,
  'details-wrapper': '_details-wrapper_i1abw_10',
};
function F() {
  const r = o(d);
  return e('div', {
    children: u('div', {
      className: t.wrapper,
      children: [
        e(s, {
          className: t.result,
          status: 'error',
          title: r['error.result.title'],
          subTitle: r['error.result.subTitle'],
          extra: [
            e(
              i,
              {
                type: 'secondary',
                style: { marginRight: 16 },
                children: r['error.result.goBack'],
              },
              'again'
            ),
            e(
              i,
              { type: 'primary', children: r['error.result.retry'] },
              'back'
            ),
          ],
        }),
        u('div', {
          className: t['details-wrapper'],
          children: [
            e(a.Title, {
              heading: 6,
              style: { marginTop: 0 },
              children: r['error.detailTitle'],
            }),
            e(a.Paragraph, {
              style: { marginBottom: 0 },
              children: u('ol', {
                children: [
                  u('li', {
                    children: [
                      r['error.detailLine.record'],
                      u(l, {
                        children: [e(n, {}), r['error.detailLine.record.link']],
                      }),
                    ],
                  }),
                  u('li', {
                    children: [
                      r['error.detailLine.auth'],
                      e(l, { children: r['error.detailLine.auth.link'] }),
                    ],
                  }),
                ],
              }),
            }),
          ],
        }),
      ],
    }),
  });
}
export { F as default };
