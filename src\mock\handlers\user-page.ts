import { http, HttpResponse } from 'msw';

const userPageHandlers = [
  // 用户信息
  http.get('/api/user/info', () => {
    return HttpResponse.json({
      status: 'success',
      data: {
        name: '王立群',
        avatar:
          '//p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/3ee5f13fb09879ecb5185e440cef6eb9.png~tplv-uwbnlip3yd-webp.webp',
        email: '<EMAIL>',
        job: '前端开发工程师',
        jobName: 'frontend',
        organization: '字节跳动',
        organizationName: 'ByteDance',
        location: '北京',
        locationName: 'Beijing',
        introduction: '王力群并非是一个真实存在的人。',
        personalWebsite: 'https://www.arco.design',
        verified: true,
        phoneNumber: /1[3-9]\d{9}/,
        accountId: /[a-z]{8}/,
        registrationTime: '2023-05-20 12:10:00',
      },
    });
  }),

  // 用户设置
  http.post('/api/user/setting', async ({ request }) => {
    const data = await request.json();
    return HttpResponse.json({
      status: 'success',
      data,
    });
  }),
];

export default userPageHandlers;
