const e = {
  'en-US': {
    'menu.user': 'Personal Center',
    'menu.user.setting': 'User Setting',
    'userSetting.menu.title.info': 'Personal Information',
    'userSetting.menu.title.account': 'Account Setting',
    'userSetting.menu.title.password': 'Password',
    'userSetting.menu.title.message': 'Message Notification',
    'userSetting.menu.title.result': 'Result',
    'userSetting.menu.title.data': 'Export Data',
    'userSetting.saveSuccess': 'Save Success',
    'userSetting.title.basicInfo': 'Basic Information',
    'userSetting.title.security': 'Security Settings',
    'userSetting.label.avatar': 'Avatar',
    'userSetting.label.name': 'User Name',
    'userSetting.label.accountId': 'Account ID',
    'userSetting.label.verified': 'Whether Verified',
    'userSetting.value.verified': 'verified',
    'userSetting.value.notVerified': 'not verified',
    'userSetting.label.phoneNumber': 'Phone Number',
    'userSetting.label.registrationTime': 'Registration time',
    'userSetting.btn.edit': 'Edit',
    'userSetting.save': 'Save',
    'userSetting.reset': 'Reset',
    'userSetting.info.email': 'Email',
    'userSetting.info.email.placeholder':
      'Please enter your email address, <NAME_EMAIL>',
    'userSetting.info.nickName': 'Nick name',
    'userSetting.info.nickName.placeholder': 'Please enter your nickname',
    'userSetting.info.area': 'Country / Region',
    'userSetting.info.area.placeholder': 'Please select a country/region',
    'userSetting.info.location': 'Your location',
    'userSetting.info.address': 'Specific address',
    'userSetting.info.address.placeholder': 'Please enter your address',
    'userSetting.info.profile': 'Personal profile',
    'userSetting.info.profile.placeholder':
      'Please enter your profile, no more than 200 words.',
    'userSetting.security.password': 'Login Password',
    'userSetting.security.password.tips':
      'Has been set. The password has at least 6 characters, supports numbers, letters and special characters except spaces, and must contain both numbers and uppercase and lowercase letters. ',
    'userSetting.security.question': 'Secure question',
    'userSetting.security.question.placeholder':
      'You have not set a secret security question, which can effectively protect the security of your account.',
    'userSetting.security.phone': 'Secure phone',
    'userSetting.security.phone.tips': 'Bound:',
    'userSetting.security.email': 'Secure email',
    'userSetting.security.email.placeholder':
      'You have not set up an email address yet. The bound email address can be used to retrieve your password, receive notifications, etc.',
    'userSetting.verified.enterprise': 'Enterprise real-name certification',
    'userSetting.verified.records': 'Certification records',
    'userSetting.verified.label.accountType': 'Account Type',
    'userSetting.verified.label.isVerified': 'Authentication status',
    'userSetting.verified.label.verifiedTime': 'Authentication time',
    'userSetting.verified.label.legalPersonName': 'Legal Person name',
    'userSetting.verified.label.certificateType':
      'Type of legal person certificate',
    'userSetting.verified.label.certificationNumber':
      'Legal person certification number',
    'userSetting.verified.label.enterpriseName': 'Enterprise Name',
    'userSetting.verified.label.enterpriseCertificateType':
      'Enterprise certificate type',
    'userSetting.verified.label.organizationCode': 'Organization Code',
    'userSetting.verified.authType': 'Authentication type',
    'userSetting.verified.authContent': 'Authentication content',
    'userSetting.verified.authStatus': 'Current status',
    'userSetting.verified.createdTime': 'Created time',
    'userSetting.verified.operation': 'Operation',
    'userSetting.verified.operation.view': 'View',
    'userSetting.verified.operation.revoke': 'Revoke',
    'userSetting.verified.status.success': 'passed',
    'userSetting.verified.status.waiting': 'under review',
  },
  'zh-CN': {
    'menu.user': '\u4E2A\u4EBA\u4E2D\u5FC3',
    'menu.user.setting': '\u7528\u6237\u8BBE\u7F6E',
    'userSetting.menu.title.info': '\u4E2A\u4EBA\u4FE1\u606F',
    'userSetting.menu.title.account': '\u8D26\u53F7\u8BBE\u7F6E',
    'userSetting.menu.title.password': '\u5BC6\u7801',
    'userSetting.menu.title.message': '\u6D88\u606F\u901A\u77E5',
    'userSetting.menu.title.result': '\u7ED3\u679C\u9875',
    'userSetting.menu.title.data': '\u5BFC\u51FA\u6570\u636E',
    'userSetting.saveSuccess': '\u4FDD\u5B58\u6210\u529F',
    'userSetting.title.basicInfo': '\u57FA\u672C\u4FE1\u606F',
    'userSetting.title.security': '\u5B89\u5168\u8BBE\u7F6E',
    'userSetting.label.avatar': '\u5934\u50CF',
    'userSetting.label.name': '\u7528\u6237\u540D',
    'userSetting.label.accountId': '\u8D26\u53F7ID',
    'userSetting.label.verified': '\u5B9E\u540D\u8BA4\u8BC1',
    'userSetting.value.verified': '\u5DF2\u8BA4\u8BC1',
    'userSetting.value.notVerified': '\u672A\u8BA4\u8BC1',
    'userSetting.label.phoneNumber': '\u624B\u673A\u53F7\u7801',
    'userSetting.label.registrationTime': '\u6CE8\u518C\u65F6\u95F4',
    'userSetting.btn.edit': '\u4FEE\u6539',
    'userSetting.btn.set': '\u8BBE\u7F6E',
    'userSetting.save': '\u4FDD\u5B58',
    'userSetting.reset': '\u91CD\u7F6E',
    'userSetting.info.email': '\u90AE\u7BB1',
    'userSetting.info.email.placeholder':
      '\u8BF7\u8F93\u5165\u90AE\u7BB1\u5730\u5740\uFF0C\<EMAIL>',
    'userSetting.info.nickName': '\u6635\u79F0',
    'userSetting.info.nickName.placeholder':
      '\u8BF7\u8F93\u5165\u60A8\u7684\u6635\u79F0',
    'userSetting.info.area': '\u56FD\u5BB6/\u5730\u533A',
    'userSetting.info.area.placeholder':
      '\u8BF7\u9009\u62E9\u56FD\u5BB6/\u5730\u533A',
    'userSetting.info.location': '\u6240\u5728\u533A\u57DF',
    'userSetting.info.address': '\u5177\u4F53\u5730\u5740',
    'userSetting.info.address.placeholder':
      '\u8BF7\u8F93\u5165\u60A8\u7684\u5730\u5740',
    'userSetting.info.profile': '\u4E2A\u4EBA\u7B80\u4ECB',
    'userSetting.info.profile.placeholder':
      '\u8BF7\u8F93\u5165\u60A8\u7684\u4E2A\u4EBA\u7B80\u4ECB\uFF0C\u6700\u591A\u4E0D\u8D85\u8FC7200\u5B57\u3002',
    'userSetting.security.password': '\u767B\u9646\u5BC6\u7801',
    'userSetting.security.password.tips':
      '\u5DF2\u8BBE\u7F6E\u3002\u5BC6\u7801\u81F3\u5C116\u4F4D\u5B57\u7B26\uFF0C\u652F\u6301\u6570\u5B57\u3001\u5B57\u6BCD\u548C\u9664\u7A7A\u683C\u5916\u7684\u7279\u6B8A\u5B57\u7B26\uFF0C\u4E14\u5FC5\u987B\u540C\u65F6\u5305\u542B\u6570\u5B57\u548C\u5927\u5C0F\u5199\u5B57\u6BCD\u3002',
    'userSetting.security.question': '\u5BC6\u4FDD\u95EE\u9898',
    'userSetting.security.question.placeholder':
      '\u60A8\u6682\u672A\u8BBE\u7F6E\u5BC6\u4FDD\u95EE\u9898\uFF0C\u5BC6\u4FDD\u95EE\u9898\u53EF\u4EE5\u6709\u6548\u7684\u4FDD\u62A4\u8D26\u53F7\u7684\u5B89\u5168\u3002',
    'userSetting.security.phone': '\u5B89\u5168\u624B\u673A',
    'userSetting.security.phone.tips': '\u5DF2\u7ED1\u5B9A\uFF1A',
    'userSetting.security.email': '\u5B89\u5168\u90AE\u7BB1',
    'userSetting.security.email.placeholder':
      '\u60A8\u6682\u672A\u8BBE\u7F6E\u90AE\u7BB1\uFF0C\u7ED1\u5B9A\u90AE\u7BB1\u53EF\u4EE5\u7528\u6765\u627E\u56DE\u5BC6\u7801\u3001\u63A5\u6536\u901A\u77E5\u7B49\u3002',
    'userSetting.verified.enterprise': '\u4F01\u4E1A\u5B9E\u540D\u8BA4\u8BC1',
    'userSetting.verified.label.accountType': '\u8D26\u53F7\u7C7B\u578B',
    'userSetting.verified.label.isVerified': '\u8BA4\u8BC1\u72B6\u6001',
    'userSetting.verified.label.verifiedTime': '\u8BA4\u8BC1\u65F6\u95F4',
    'userSetting.verified.label.legalPersonName': '\u6CD5\u4EBA\u59D3\u540D',
    'userSetting.verified.label.certificateType':
      '\u6CD5\u4EBA\u8BC1\u4EF6\u7C7B\u578B',
    'userSetting.verified.label.certificationNumber':
      '\u6CD5\u4EBA\u8BA4\u8BC1\u53F7\u7801',
    'userSetting.verified.label.enterpriseName': '\u4F01\u4E1A\u540D\u79F0',
    'userSetting.verified.label.enterpriseCertificateType':
      '\u4F01\u4E1A\u8BC1\u4EF6\u7C7B\u578B',
    'userSetting.verified.label.organizationCode':
      '\u7EC4\u7EC7\u673A\u6784\u4EE3\u7801',
    'userSetting.verified.records': '\u8BA4\u8BC1\u8BB0\u5F55',
    'userSetting.verified.authType': '\u8BA4\u8BC1\u7C7B\u578B',
    'userSetting.verified.authContent': '\u8BA4\u8BC1\u5185\u5BB9',
    'userSetting.verified.authStatus': '\u5F53\u524D\u72B6\u6001',
    'userSetting.verified.createdTime': '\u521B\u5EFA\u65F6\u95F4',
    'userSetting.verified.operation': '\u64CD\u4F5C',
    'userSetting.verified.operation.view': '\u67E5\u770B',
    'userSetting.verified.operation.revoke': '\u64A4\u56DE',
    'userSetting.verified.status.success': '\u5DF2\u901A\u8FC7',
    'userSetting.verified.status.waiting': '\u5BA1\u6838\u4E2D',
  },
};
var u = e;
export { u as l };
