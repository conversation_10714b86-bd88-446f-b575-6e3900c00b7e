import {
  R as y,
  r as _,
  g as b,
  a as n,
  _ as w,
  n as u,
  j as m,
  h as j,
  c as p,
} from './index.7dafa16d.js';
function g(e, s) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var a = Object.getOwnPropertySymbols(e);
    s &&
      (a = a.filter(function (r) {
        return Object.getOwnPropertyDescriptor(e, r).enumerable;
      })),
      t.push.apply(t, a);
  }
  return t;
}
function v(e) {
  for (var s = 1; s < arguments.length; s++) {
    var t = arguments[s] != null ? arguments[s] : {};
    s % 2
      ? g(Object(t), !0).forEach(function (a) {
          w(e, a, t[a]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : g(Object(t)).forEach(function (a) {
          Object.defineProperty(e, a, Object.getOwnPropertyDescriptor(t, a));
        });
  }
  return e;
}
function x(e, s) {
  var t = _.exports.useContext(b),
    a = t.prefixCls,
    r = a === void 0 ? 'arco' : a,
    l = e.spin,
    i = e.className,
    c = v(
      v({ 'aria-hidden': !0, focusable: !1, ref: s }, e),
      {},
      {
        className: ''
          .concat(i ? i + ' ' : '')
          .concat(r, '-icon ')
          .concat(r, '-icon-star'),
      }
    );
  return (
    l && (c.className = ''.concat(c.className, ' ').concat(r, '-icon-loading')),
    delete c.spin,
    delete c.isIcon,
    n('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...c,
      children: n('path', {
        d: 'M22.552 6.908a.5.5 0 0 1 .896 0l5.02 10.17a.5.5 0 0 0 .376.274l11.224 1.631a.5.5 0 0 1 .277.853l-8.122 7.916a.5.5 0 0 0-.143.443l1.917 11.178a.5.5 0 0 1-.726.527l-10.038-5.278a.5.5 0 0 0-.466 0L12.73 39.9a.5.5 0 0 1-.726-.527l1.918-11.178a.5.5 0 0 0-.144-.443l-8.122-7.916a.5.5 0 0 1 .278-.853l11.223-1.63a.5.5 0 0 0 .376-.274l5.02-10.17Z',
      }),
    })
  );
}
var f = y.forwardRef(x);
f.defaultProps = { isIcon: !0 };
f.displayName = 'IconStar';
var C = f;
function O(e, s) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var a = Object.getOwnPropertySymbols(e);
    s &&
      (a = a.filter(function (r) {
        return Object.getOwnPropertyDescriptor(e, r).enumerable;
      })),
      t.push.apply(t, a);
  }
  return t;
}
function h(e) {
  for (var s = 1; s < arguments.length; s++) {
    var t = arguments[s] != null ? arguments[s] : {};
    s % 2
      ? O(Object(t), !0).forEach(function (a) {
          w(e, a, t[a]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : O(Object(t)).forEach(function (a) {
          Object.defineProperty(e, a, Object.getOwnPropertyDescriptor(t, a));
        });
  }
  return e;
}
function N(e, s) {
  var t = _.exports.useContext(b),
    a = t.prefixCls,
    r = a === void 0 ? 'arco' : a,
    l = e.spin,
    i = e.className,
    c = h(
      h({ 'aria-hidden': !0, focusable: !1, ref: s }, e),
      {},
      {
        className: ''
          .concat(i ? i + ' ' : '')
          .concat(r, '-icon ')
          .concat(r, '-icon-command'),
      }
    );
  return (
    l && (c.className = ''.concat(c.className, ' ').concat(r, '-icon-loading')),
    delete c.spin,
    delete c.isIcon,
    n('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...c,
      children: n('path', {
        d: 'M29 19v-6a6 6 0 1 1 6 6h-6Zm0 0v10m0-10H19m10 10v6a6 6 0 1 0 6-6h-6Zm0 0H19m0-10v10m0-10v-6a6 6 0 1 0-6 6h6Zm0 10v6a6 6 0 1 1-6-6h6Z',
      }),
    })
  );
}
var d = y.forwardRef(N);
d.defaultProps = { isIcon: !0 };
d.displayName = 'IconCommand';
var P = d,
  o = {
    'message-item': '_message-item_wn64e_1',
    'message-item-footer': '_message-item-footer_wn64e_7',
    'message-item-actions': '_message-item-actions_wn64e_12',
    'message-item-actions-item': '_message-item-actions-item_wn64e_16',
    'message-item-collected': '_message-item-collected_wn64e_34',
    'message-item-actions-collect': '_message-item-actions-collect_wn64e_34',
  };
function I(e) {
  const { data: s = {} } = e,
    t = u(o['message-item'], { [o['message-item-collected']]: s.isCollect });
  return n('div', {
    className: t,
    children: m(j, {
      size: 4,
      direction: 'vertical',
      style: { width: '100%' },
      children: [
        n(p.Text, { type: 'warning', children: s.username }),
        n(p.Text, { children: s.content }),
        m('div', {
          className: o['message-item-footer'],
          children: [
            n('div', {
              className: o['message-item-time'],
              children: n(p.Text, { type: 'secondary', children: s.time }),
            }),
            m('div', {
              className: o['message-item-actions'],
              children: [
                n('div', {
                  className: o['message-item-actions-item'],
                  children: n(P, {}),
                }),
                n('div', {
                  className: u(
                    o['message-item-actions-item'],
                    o['message-item-actions-collect']
                  ),
                  children: n(C, {}),
                }),
              ],
            }),
          ],
        }),
      ],
    }),
  });
}
var D = Object.freeze(
  Object.defineProperty({ __proto__: null, default: I }, Symbol.toStringTag, {
    value: 'Module',
  })
);
export { I as M, D as i, o as s };
