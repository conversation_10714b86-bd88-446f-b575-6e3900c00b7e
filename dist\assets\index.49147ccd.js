import { u as r, a as e, m as s, B as n } from './index.7dafa16d.js';
const c = {
    'en-US': {
      'menu.exception': 'Exception page',
      'menu.exception.500': '500',
      'exception.result.500.description': 'Internal server error',
      'exception.result.500.back': 'Back',
    },
    'zh-CN': {
      'menu.exception': '\u5F02\u5E38\u9875',
      'menu.exception.500': '500',
      'exception.result.500.description':
        '\u62B1\u6B49\uFF0C\u670D\u52A1\u5668\u51FA\u4E86\u70B9\u95EE\u9898\uFF5E',
      'exception.result.500.back': '\u8FD4\u56DE',
    },
  },
  p = '_wrapper_vup5c_1',
  a = '_result_vup5c_6';
var u = { wrapper: p, result: a };
function o() {
  const t = r(c);
  return e('div', {
    className: u.wrapper,
    children: e(s, {
      className: u.result,
      status: '500',
      subTitle: t['exception.result.500.description'],
      extra: e(
        n,
        { type: 'primary', children: t['exception.result.500.back'] },
        'back'
      ),
    }),
  });
}
export { o as default };
