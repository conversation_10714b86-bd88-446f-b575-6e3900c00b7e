import { http, HttpResponse } from 'msw';

const mockLine = (name) => {
  const result = new Array(12).fill(0).map(() => ({
    y: Math.floor(Math.random() * 80) + 20, // 20-100
  }));
  return result.map((item, index) => ({
    ...item,
    x: index,
    name,
  }));
};

const mockPie = () => {
  return new Array(3).fill(0).map((_, index) => ({
    name: ['纯文本', '图文类', '视频类'][index],
    count: Math.floor(Math.random() * 80) + 20, // 20-100
  }));
};

const getTimeLine = (name) => {
  const timeArr = new Array(12).fill(0).map((_, index) => {
    const time = index * 2;
    return time < 9 ? `0${time}:00` : `${time}:00`;
  });
  return new Array(12).fill(0).map((_, index) => ({
    name,
    time: timeArr[index],
    count: Math.floor(Math.random() * 4000) + 1000, // 1000-5000
    rate: Math.floor(Math.random() * 100), // 0-100
  }));
};

const dataAnalysisHandlers = [
  http.get('/api/data-analysis/overview', ({ request }) => {
    const url = new URL(request.url);
    const type = url.searchParams.get('type');

    let chartData;
    if (type === 'pie') {
      chartData = mockPie();
    } else if (type === 'line') {
      chartData = [...mockLine('类目1'), ...mockLine('类目2')];
    } else {
      chartData = mockLine('类目1');
    }

    return HttpResponse.json({
      count: Math.floor(Math.random() * 9000) + 1000, // 1000-10000
      increment: Math.random() > 0.5,
      diff: Math.floor(Math.random() * 900) + 100, // 100-1000
      chartType: type,
      chartData,
    });
  }),

  http.get('/api/data-analysis/content-publishing', () => {
    return HttpResponse.json([
      ...getTimeLine('纯文本'),
      ...getTimeLine('视频类'),
      ...getTimeLine('图文类'),
    ]);
  }),

  http.get('/api/data-analysis/author-list', () => {
    const authors = [
      '用魔法打败魔法',
      '王多鱼',
      'Christopher',
      '叫我小李好了',
      '陈皮话梅糖',
      '碳烤小肥羊',
    ];

    const list = new Array(8).fill(0).map((_, index) => {
      const id = index + 1;
      const time = new Array(12).fill(0).map((_, timeIndex) => {
        const t = timeIndex * 2;
        return t < 9 ? `0${t}:00` : `${t}:00`;
      })[id % 12];

      return {
        id,
        author: authors[Math.floor(Math.random() * authors.length)],
        time,
        contentCount: Math.floor(Math.random() * 4000) + 1000, // 1000-5000
        clickCount: Math.floor(Math.random() * 25000) + 5000, // 5000-30000
      };
    });

    return HttpResponse.json({
      list,
    });
  }),
];

export default dataAnalysisHandlers;
