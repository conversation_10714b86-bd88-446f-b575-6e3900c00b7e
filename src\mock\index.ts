import { isSSR } from '@/utils/is';
import setupMsw from '@/utils/setupMsw';

// 导入所有 handlers
import userHandlers from './handlers/user';
import messageBoxHandlers from './handlers/message-box';
import dataAnalysisHandlers from './handlers/data-analysis';
import monitorHandlers from './handlers/monitor';
import workplaceHandlers from './handlers/workplace';
import listCardHandlers from './handlers/list-card';
import searchTableHandlers from './handlers/search-table';
import formHandlers from './handlers/form';
import visualizationHandlers from './handlers/visualization';
import profileHandlers from './handlers/profile';
import userPageHandlers from './handlers/user-page';
import resultHandlers from './handlers/result';

// 合并所有 handlers
const handlers = [
  ...userHandlers,
  ...messageBoxHandlers,
  ...dataAnalysisHandlers,
  ...monitorHandlers,
  ...workplaceHandlers,
  ...listCardHandlers,
  ...searchTableHandlers,
  ...formHandlers,
  ...visualizationHandlers,
  ...profileHandlers,
  ...userPageHandlers,
  ...resultHandlers,
];

// 设置 MSW
export const initMsw = async () => {
  if (!isSSR) {
    await setupMsw({
      handlers,
    });
    console.log('[MSW] All handlers registered');
  }
};

export default handlers;
