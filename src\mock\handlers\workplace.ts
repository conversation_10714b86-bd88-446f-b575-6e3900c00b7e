import { http, HttpResponse } from 'msw';

const workplaceHandlers = [
  http.get('/api/workplace/overview-content', () => {
    const year = new Date().getFullYear();
    const getLineData = () => {
      return new Array(12).fill(0).map((_item, index) => ({
        date: `${year}-${index + 1}`,
        count: Math.floor(Math.random() * 55000) + 20000, // 20000-75000
      }));
    };
    return HttpResponse.json({
      allContents: '373.5w+',
      liveContents: '368',
      increaseComments: '8874',
      growthRate: '2.8%',
      chartData: getLineData(),
    });
  }),

  http.get('/api/workplace/popular-contents', ({ request }) => {
    const url = new URL(request.url);
    const type = url.searchParams.get('type');

    const getList = () => {
      return new Array(5).fill(0).map((_item, index) => ({
        id: index,
        rank: index + 1,
        title: type === 'image' ? '图片内容' : '视频内容',
        pv: Math.floor(Math.random() * 900) + 100, // 100-1000
        increase: Math.floor(Math.random() * 50) + 5, // 5-55
      }));
    };

    return HttpResponse.json(getList());
  }),

  http.get('/api/workplace/content-percentage', () => {
    return HttpResponse.json([
      {
        type: '纯文本',
        count: 148564,
        percent: 29,
      },
      {
        type: '图文类',
        count: 334271,
        percent: 66,
      },
      {
        type: '视频类',
        count: 23863,
        percent: 5,
      },
    ]);
  }),

  http.get('/api/workplace/announcement', () => {
    return HttpResponse.json([
      {
        id: 1,
        title: '关于内容版权问题的通知',
        content:
          '鉴于发现用户在平台发布盗版视频内容，特此告知，请勿发布盗版内容，一经发现，将作下架处理。',
        datetime: '2023-10-10 10:00:00',
      },
      {
        id: 2,
        title: '系统功能升级通知',
        content: '新增多类模式，用户可以在对话框进行多类型内容发布',
        datetime: '2023-10-05 11:00:00',
      },
    ]);
  }),

  http.get('/api/workplace/shortcuts', () => {
    return HttpResponse.json([
      {
        id: 1,
        title: '内容创作',
        icon: 'icon-edit',
      },
      {
        id: 2,
        title: '内容管理',
        icon: 'icon-delete',
      },
      {
        id: 3,
        title: '内容审批',
        icon: 'icon-user',
      },
      {
        id: 4,
        title: '内容数据',
        icon: 'icon-file',
      },
      {
        id: 5,
        title: '消息中心',
        icon: 'icon-message',
      },
      {
        id: 6,
        title: '系统设置',
        icon: 'icon-settings',
      },
    ]);
  }),
];

export default workplaceHandlers;
