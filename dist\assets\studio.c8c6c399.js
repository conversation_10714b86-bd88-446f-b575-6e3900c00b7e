import {
  u as n,
  j as e,
  a5 as s,
  a as i,
  c as t,
  a6 as d,
  h as l,
  a7 as p,
} from './index.7dafa16d.js';
import { C as m } from './index.519f9d90.js';
import { l as h } from './index.662e0e1f.js';
import { s as o } from './index.module.1dd0e4f6.js';
function v(c) {
  const r = n(h),
    { userInfo: a } = c;
  return e(m, {
    children: [
      e(s.Row, {
        children: [
          i(s.Col, {
            span: 16,
            children: i(t.Title, {
              style: { marginTop: 0, marginBottom: 16 },
              heading: 6,
              children: r['monitor.title.studioPreview'],
            }),
          }),
          i(s.Col, {
            span: 8,
            style: { textAlign: 'right' },
            children: i(d, {}),
          }),
        ],
      }),
      e('div', {
        className: o['studio-wrapper'],
        children: [
          i('img', {
            src: 'http://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/c788fc704d32cf3b1136c7d45afc2669.png~tplv-uwbnlip3yd-webp.webp',
            className: o['studio-preview'],
          }),
          e('div', {
            className: o['studio-bar'],
            children: [
              a &&
                i('div', {
                  children: e(l, {
                    size: 12,
                    children: [
                      i(p, { size: 24, children: i('img', { src: a.avatar }) }),
                      e(t.Text, {
                        children: [a.name, r['monitor.studioPreview.studio']],
                      }),
                    ],
                  }),
                }),
              e(t.Text, {
                type: 'secondary',
                children: ['3,6000 ', r['monitor.studioPreview.watching']],
              }),
            ],
          }),
        ],
      }),
    ],
  });
}
export { v as default };
