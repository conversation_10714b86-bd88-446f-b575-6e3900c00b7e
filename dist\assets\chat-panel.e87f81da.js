import {
  u,
  r as l,
  j as t,
  a as e,
  c as g,
  h as o,
  i as r,
  k as n,
  B as c,
  S as y,
  l as x,
} from './index.7dafa16d.js';
import { l as S } from './index.662e0e1f.js';
import L from './index.16dc0c3f.js';
import { s } from './index.module.1dd0e4f6.js';
import { I as v } from './index.5ef0222d.js';
import { I } from './index.305c74fb.js';
import './item.3a5c9904.js';
function C() {
  const a = u(S),
    [h, d] = l.exports.useState([]),
    [p, i] = l.exports.useState(!1);
  function m() {
    i(!0),
      x
        .get('/api/chatList')
        .then((f) => {
          d(f.data || []);
        })
        .finally(() => {
          i(!1);
        });
  }
  return (
    l.exports.useEffect(() => {
      m();
    }, []),
    t('div', {
      className: s['chat-panel'],
      children: [
        t('div', {
          className: s['chat-panel-header'],
          children: [
            e(g.Title, {
              style: { marginTop: 0, marginBottom: 16 },
              heading: 6,
              children: a['monitor.title.chatPanel'],
            }),
            t(o, {
              size: 8,
              children: [
                e(r, {
                  style: { width: 80 },
                  defaultValue: 'all',
                  children: e(r.Option, {
                    value: 'all',
                    children: a['monitor.chat.options.all'],
                  }),
                }),
                e(n.Search, {
                  placeholder: a['monitor.chat.placeholder.searchCategory'],
                }),
                e(c, { type: 'text', iconOnly: !0, children: e(v, {}) }),
              ],
            }),
          ],
        }),
        e('div', {
          className: s['chat-panel-content'],
          children: e(y, {
            loading: p,
            style: { width: '100%' },
            children: e(L, { data: h }),
          }),
        }),
        e('div', {
          className: s['chat-panel-footer'],
          children: t(o, {
            size: 8,
            children: [
              e(n, { suffix: e(I, {}) }),
              e(c, { type: 'primary', children: a['monitor.chat.update'] }),
            ],
          }),
        }),
      ],
    })
  );
}
export { C as default };
