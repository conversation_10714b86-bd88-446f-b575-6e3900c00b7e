import { al as C, ax as z, K as Q, s as j, aw as q } from './index.7dafa16d.js';
var ht = { exports: {} };
(function (t, e) {
  (function (n, r) {
    t.exports = r();
  })(C, function () {
    var n = {
        LTS: 'h:mm:ss A',
        LT: 'h:mm A',
        L: 'MM/DD/YYYY',
        LL: 'MMMM D, YYYY',
        LLL: 'MMMM D, YYYY h:mm A',
        LLLL: 'dddd, MMMM D, YYYY h:mm A',
      },
      r =
        /(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,
      c = /\d/,
      o = /\d\d/,
      a = /\d\d?/,
      s = /\d*[^-_:/,()\s\d]+/,
      u = {},
      g = function (i) {
        return (i = +i) + (i > 68 ? 1900 : 2e3);
      },
      d = function (i) {
        return function (h) {
          this[i] = +h;
        };
      },
      O = [
        /[+-]\d\d:?(\d\d)?|Z/,
        function (i) {
          (this.zone || (this.zone = {})).offset = (function (h) {
            if (!h || h === 'Z') return 0;
            var v = h.match(/([+-]|\d\d)/g),
              l = 60 * v[1] + (+v[2] || 0);
            return l === 0 ? 0 : v[0] === '+' ? -l : l;
          })(i);
        },
      ],
      m = function (i) {
        var h = u[i];
        return h && (h.indexOf ? h : h.s.concat(h.f));
      },
      p = function (i, h) {
        var v,
          l = u.meridiem;
        if (l) {
          for (var x = 1; x <= 24; x += 1)
            if (i.indexOf(l(x, 0, h)) > -1) {
              v = x > 12;
              break;
            }
        } else v = i === (h ? 'pm' : 'PM');
        return v;
      },
      $ = {
        A: [
          s,
          function (i) {
            this.afternoon = p(i, !1);
          },
        ],
        a: [
          s,
          function (i) {
            this.afternoon = p(i, !0);
          },
        ],
        Q: [
          c,
          function (i) {
            this.month = 3 * (i - 1) + 1;
          },
        ],
        S: [
          c,
          function (i) {
            this.milliseconds = 100 * +i;
          },
        ],
        SS: [
          o,
          function (i) {
            this.milliseconds = 10 * +i;
          },
        ],
        SSS: [
          /\d{3}/,
          function (i) {
            this.milliseconds = +i;
          },
        ],
        s: [a, d('seconds')],
        ss: [a, d('seconds')],
        m: [a, d('minutes')],
        mm: [a, d('minutes')],
        H: [a, d('hours')],
        h: [a, d('hours')],
        HH: [a, d('hours')],
        hh: [a, d('hours')],
        D: [a, d('day')],
        DD: [o, d('day')],
        Do: [
          s,
          function (i) {
            var h = u.ordinal,
              v = i.match(/\d+/);
            if (((this.day = v[0]), h))
              for (var l = 1; l <= 31; l += 1)
                h(l).replace(/\[|\]/g, '') === i && (this.day = l);
          },
        ],
        w: [a, d('week')],
        ww: [o, d('week')],
        M: [a, d('month')],
        MM: [o, d('month')],
        MMM: [
          s,
          function (i) {
            var h = m('months'),
              v =
                (
                  m('monthsShort') ||
                  h.map(function (l) {
                    return l.slice(0, 3);
                  })
                ).indexOf(i) + 1;
            if (v < 1) throw new Error();
            this.month = v % 12 || v;
          },
        ],
        MMMM: [
          s,
          function (i) {
            var h = m('months').indexOf(i) + 1;
            if (h < 1) throw new Error();
            this.month = h % 12 || h;
          },
        ],
        Y: [/[+-]?\d+/, d('year')],
        YY: [
          o,
          function (i) {
            this.year = g(i);
          },
        ],
        YYYY: [/\d{4}/, d('year')],
        Z: O,
        ZZ: O,
      };
    function f(i) {
      var h, v;
      (h = i), (v = u && u.formats);
      for (
        var l = (i = h.replace(
            /(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,
            function (T, k, A) {
              var L = A && A.toUpperCase();
              return (
                k ||
                v[A] ||
                n[A] ||
                v[L].replace(
                  /(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,
                  function (H, U, W) {
                    return U || W.slice(1);
                  }
                )
              );
            }
          )).match(r),
          x = l.length,
          y = 0;
        y < x;
        y += 1
      ) {
        var S = l[y],
          w = $[S],
          M = w && w[0],
          D = w && w[1];
        l[y] = D ? { regex: M, parser: D } : S.replace(/^\[|\]$/g, '');
      }
      return function (T) {
        for (var k = {}, A = 0, L = 0; A < x; A += 1) {
          var H = l[A];
          if (typeof H == 'string') L += H.length;
          else {
            var U = H.regex,
              W = H.parser,
              G = T.slice(L),
              F = U.exec(G)[0];
            W.call(k, F), (T = T.replace(F, ''));
          }
        }
        return (
          (function (Z) {
            var N = Z.afternoon;
            if (N !== void 0) {
              var b = Z.hours;
              N ? b < 12 && (Z.hours += 12) : b === 12 && (Z.hours = 0),
                delete Z.afternoon;
            }
          })(k),
          k
        );
      };
    }
    return function (i, h, v) {
      (v.p.customParseFormat = !0),
        i && i.parseTwoDigitYear && (g = i.parseTwoDigitYear);
      var l = h.prototype,
        x = l.parse;
      l.parse = function (y) {
        var S = y.date,
          w = y.utc,
          M = y.args;
        this.$u = w;
        var D = M[1];
        if (typeof D == 'string') {
          var T = M[2] === !0,
            k = M[3] === !0,
            A = T || k,
            L = M[2];
          k && (L = M[2]),
            (u = this.$locale()),
            !T && L && (u = v.Ls[L]),
            (this.$d = (function (G, F, Z, N) {
              try {
                if (['x', 'X'].indexOf(F) > -1)
                  return new Date((F === 'X' ? 1e3 : 1) * G);
                var b = f(F)(G),
                  E = b.year,
                  P = b.month,
                  yt = b.day,
                  Ot = b.hours,
                  xt = b.minutes,
                  wt = b.seconds,
                  Dt = b.milliseconds,
                  st = b.zone,
                  ot = b.week,
                  R = new Date(),
                  K = yt || (E || P ? 1 : R.getDate()),
                  J = E || R.getFullYear(),
                  B = 0;
                (E && !P) || (B = P > 0 ? P - 1 : R.getMonth());
                var I,
                  V = Ot || 0,
                  tt = xt || 0,
                  et = wt || 0,
                  rt = Dt || 0;
                return st
                  ? new Date(
                      Date.UTC(J, B, K, V, tt, et, rt + 60 * st.offset * 1e3)
                    )
                  : Z
                  ? new Date(Date.UTC(J, B, K, V, tt, et, rt))
                  : ((I = new Date(J, B, K, V, tt, et, rt)),
                    ot && (I = N(I).week(ot).toDate()),
                    I);
              } catch {
                return new Date('');
              }
            })(S, D, w, v)),
            this.init(),
            L && L !== !0 && (this.$L = this.locale(L).$L),
            A && S != this.format(D) && (this.$d = new Date('')),
            (u = {});
        } else if (D instanceof Array)
          for (var H = D.length, U = 1; U <= H; U += 1) {
            M[1] = D[U - 1];
            var W = v.apply(this, M);
            if (W.isValid()) {
              (this.$d = W.$d), (this.$L = W.$L), this.init();
              break;
            }
            U === H && (this.$d = new Date(''));
          }
        else x.call(this, y);
      };
    };
  });
})(ht);
var Mt = ht.exports,
  dt = { exports: {} };
(function (t, e) {
  (function (n, r) {
    t.exports = r();
  })(C, function () {
    return function (n, r, c) {
      r.prototype.isBetween = function (o, a, s, u) {
        var g = c(o),
          d = c(a),
          O = (u = u || '()')[0] === '(',
          m = u[1] === ')';
        return (
          ((O ? this.isAfter(g, s) : !this.isBefore(g, s)) &&
            (m ? this.isBefore(d, s) : !this.isAfter(d, s))) ||
          ((O ? this.isBefore(g, s) : !this.isAfter(g, s)) &&
            (m ? this.isAfter(d, s) : !this.isBefore(d, s)))
        );
      };
    };
  });
})(dt);
var Yt = dt.exports,
  lt = { exports: {} };
(function (t, e) {
  (function (n, r) {
    t.exports = r();
  })(C, function () {
    var n = 'week',
      r = 'year';
    return function (c, o, a) {
      var s = o.prototype;
      (s.week = function (u) {
        if ((u === void 0 && (u = null), u !== null))
          return this.add(7 * (u - this.week()), 'day');
        var g = this.$locale().yearStart || 1;
        if (this.month() === 11 && this.date() > 25) {
          var d = a(this).startOf(r).add(1, r).date(g),
            O = a(this).endOf(n);
          if (d.isBefore(O)) return 1;
        }
        var m = a(this)
            .startOf(r)
            .date(g)
            .startOf(n)
            .subtract(1, 'millisecond'),
          p = this.diff(m, n, !0);
        return p < 0 ? a(this).startOf('week').week() : Math.ceil(p);
      }),
        (s.weeks = function (u) {
          return u === void 0 && (u = null), this.week(u);
        });
    };
  });
})(lt);
var Tt = lt.exports,
  vt = { exports: {} };
(function (t, e) {
  (function (n, r) {
    t.exports = r();
  })(C, function () {
    return function (n, r) {
      var c = r.prototype,
        o = c.format;
      c.format = function (a) {
        var s = this,
          u = this.$locale();
        if (!this.isValid()) return o.bind(this)(a);
        var g = this.$utils(),
          d = (a || 'YYYY-MM-DDTHH:mm:ssZ').replace(
            /\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,
            function (O) {
              switch (O) {
                case 'Q':
                  return Math.ceil((s.$M + 1) / 3);
                case 'Do':
                  return u.ordinal(s.$D);
                case 'gggg':
                  return s.weekYear();
                case 'GGGG':
                  return s.isoWeekYear();
                case 'wo':
                  return u.ordinal(s.week(), 'W');
                case 'w':
                case 'ww':
                  return g.s(s.week(), O === 'w' ? 1 : 2, '0');
                case 'W':
                case 'WW':
                  return g.s(s.isoWeek(), O === 'W' ? 1 : 2, '0');
                case 'k':
                case 'kk':
                  return g.s(
                    String(s.$H === 0 ? 24 : s.$H),
                    O === 'k' ? 1 : 2,
                    '0'
                  );
                case 'X':
                  return Math.floor(s.$d.getTime() / 1e3);
                case 'x':
                  return s.$d.getTime();
                case 'z':
                  return '[' + s.offsetName() + ']';
                case 'zzz':
                  return '[' + s.offsetName('long') + ']';
                default:
                  return O;
              }
            }
          );
        return o.bind(this)(d);
      };
    };
  });
})(vt);
var St = vt.exports,
  mt = { exports: {} };
(function (t, e) {
  (function (n, r) {
    t.exports = r();
  })(C, function () {
    return function (n, r) {
      r.prototype.weekYear = function () {
        var c = this.month(),
          o = this.week(),
          a = this.year();
        return o === 1 && c === 11 ? a + 1 : c === 0 && o >= 52 ? a - 1 : a;
      };
    };
  });
})(mt);
var kt = mt.exports,
  gt = { exports: {} };
(function (t, e) {
  (function (n, r) {
    t.exports = r();
  })(C, function () {
    var n = 'month',
      r = 'quarter';
    return function (c, o) {
      var a = o.prototype;
      a.quarter = function (g) {
        return this.$utils().u(g)
          ? Math.ceil((this.month() + 1) / 3)
          : this.month((this.month() % 3) + 3 * (g - 1));
      };
      var s = a.add;
      a.add = function (g, d) {
        return (
          (g = Number(g)),
          this.$utils().p(d) === r ? this.add(3 * g, n) : s.bind(this)(g, d)
        );
      };
      var u = a.startOf;
      a.startOf = function (g, d) {
        var O = this.$utils(),
          m = !!O.u(d) || d;
        if (O.p(g) === r) {
          var p = this.quarter() - 1;
          return m
            ? this.month(3 * p)
                .startOf(n)
                .startOf('day')
            : this.month(3 * p + 2)
                .endOf(n)
                .endOf('day');
        }
        return u.bind(this)(g, d);
      };
    };
  });
})(gt);
var bt = gt.exports,
  pt = { exports: {} };
(function (t, e) {
  (function (n, r) {
    t.exports = r();
  })(C, function () {
    var n = 'minute',
      r = /[+-]\d\d(?::?\d\d)?/g,
      c = /([+-]|\d\d)/g;
    return function (o, a, s) {
      var u = a.prototype;
      (s.utc = function (f) {
        var i = { date: f, utc: !0, args: arguments };
        return new a(i);
      }),
        (u.utc = function (f) {
          var i = s(this.toDate(), { locale: this.$L, utc: !0 });
          return f ? i.add(this.utcOffset(), n) : i;
        }),
        (u.local = function () {
          return s(this.toDate(), { locale: this.$L, utc: !1 });
        });
      var g = u.parse;
      u.parse = function (f) {
        f.utc && (this.$u = !0),
          this.$utils().u(f.$offset) || (this.$offset = f.$offset),
          g.call(this, f);
      };
      var d = u.init;
      u.init = function () {
        if (this.$u) {
          var f = this.$d;
          (this.$y = f.getUTCFullYear()),
            (this.$M = f.getUTCMonth()),
            (this.$D = f.getUTCDate()),
            (this.$W = f.getUTCDay()),
            (this.$H = f.getUTCHours()),
            (this.$m = f.getUTCMinutes()),
            (this.$s = f.getUTCSeconds()),
            (this.$ms = f.getUTCMilliseconds());
        } else d.call(this);
      };
      var O = u.utcOffset;
      u.utcOffset = function (f, i) {
        var h = this.$utils().u;
        if (h(f))
          return this.$u ? 0 : h(this.$offset) ? O.call(this) : this.$offset;
        if (
          typeof f == 'string' &&
          ((f = (function (y) {
            y === void 0 && (y = '');
            var S = y.match(r);
            if (!S) return null;
            var w = ('' + S[0]).match(c) || ['-', 0, 0],
              M = w[0],
              D = 60 * +w[1] + +w[2];
            return D === 0 ? 0 : M === '+' ? D : -D;
          })(f)),
          f === null)
        )
          return this;
        var v = Math.abs(f) <= 16 ? 60 * f : f,
          l = this;
        if (i) return (l.$offset = v), (l.$u = f === 0), l;
        if (f !== 0) {
          var x = this.$u
            ? this.toDate().getTimezoneOffset()
            : -1 * this.utcOffset();
          ((l = this.local().add(v + x, n)).$offset = v),
            (l.$x.$localOffset = x);
        } else l = this.utc();
        return l;
      };
      var m = u.format;
      (u.format = function (f) {
        var i = f || (this.$u ? 'YYYY-MM-DDTHH:mm:ss[Z]' : '');
        return m.call(this, i);
      }),
        (u.valueOf = function () {
          var f = this.$utils().u(this.$offset)
            ? 0
            : this.$offset +
              (this.$x.$localOffset || this.$d.getTimezoneOffset());
          return this.$d.valueOf() - 6e4 * f;
        }),
        (u.isUTC = function () {
          return !!this.$u;
        }),
        (u.toISOString = function () {
          return this.toDate().toISOString();
        }),
        (u.toString = function () {
          return this.toDate().toUTCString();
        });
      var p = u.toDate;
      u.toDate = function (f) {
        return f === 's' && this.$offset
          ? s(this.format('YYYY-MM-DD HH:mm:ss:SSS')).toDate()
          : p.call(this);
      };
      var $ = u.diff;
      u.diff = function (f, i, h) {
        if (f && this.$u === f.$u) return $.call(this, f, i, h);
        var v = this.local(),
          l = s(f).local();
        return $.call(v, l, i, h);
      };
    };
  });
})(pt);
var zt = pt.exports,
  $t = { exports: {} };
(function (t, e) {
  (function (n, r) {
    t.exports = r();
  })(C, function () {
    var n = { year: 0, month: 1, day: 2, hour: 3, minute: 4, second: 5 },
      r = {};
    return function (c, o, a) {
      var s,
        u = function (m, p, $) {
          $ === void 0 && ($ = {});
          var f = new Date(m),
            i = (function (h, v) {
              v === void 0 && (v = {});
              var l = v.timeZoneName || 'short',
                x = h + '|' + l,
                y = r[x];
              return (
                y ||
                  ((y = new Intl.DateTimeFormat('en-US', {
                    hour12: !1,
                    timeZone: h,
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    timeZoneName: l,
                  })),
                  (r[x] = y)),
                y
              );
            })(p, $);
          return i.formatToParts(f);
        },
        g = function (m, p) {
          for (var $ = u(m, p), f = [], i = 0; i < $.length; i += 1) {
            var h = $[i],
              v = h.type,
              l = h.value,
              x = n[v];
            x >= 0 && (f[x] = parseInt(l, 10));
          }
          var y = f[3],
            S = y === 24 ? 0 : y,
            w =
              f[0] +
              '-' +
              f[1] +
              '-' +
              f[2] +
              ' ' +
              S +
              ':' +
              f[4] +
              ':' +
              f[5] +
              ':000',
            M = +m;
          return (a.utc(w).valueOf() - (M -= M % 1e3)) / 6e4;
        },
        d = o.prototype;
      (d.tz = function (m, p) {
        m === void 0 && (m = s);
        var $,
          f = this.utcOffset(),
          i = this.toDate(),
          h = i.toLocaleString('en-US', { timeZone: m }),
          v = Math.round((i - new Date(h)) / 1e3 / 60),
          l = 15 * -Math.round(i.getTimezoneOffset() / 15) - v;
        if (!Number(l)) $ = this.utcOffset(0, p);
        else if (
          (($ = a(h, { locale: this.$L })
            .$set('millisecond', this.$ms)
            .utcOffset(l, !0)),
          p)
        ) {
          var x = $.utcOffset();
          $ = $.add(f - x, 'minute');
        }
        return ($.$x.$timezone = m), $;
      }),
        (d.offsetName = function (m) {
          var p = this.$x.$timezone || a.tz.guess(),
            $ = u(this.valueOf(), p, { timeZoneName: m }).find(function (f) {
              return f.type.toLowerCase() === 'timezonename';
            });
          return $ && $.value;
        });
      var O = d.startOf;
      (d.startOf = function (m, p) {
        if (!this.$x || !this.$x.$timezone) return O.call(this, m, p);
        var $ = a(this.format('YYYY-MM-DD HH:mm:ss:SSS'), { locale: this.$L });
        return O.call($, m, p).tz(this.$x.$timezone, !0);
      }),
        (a.tz = function (m, p, $) {
          var f = $ && p,
            i = $ || p || s,
            h = g(+a(), i);
          if (typeof m != 'string') return a(m).tz(i);
          var v = (function (S, w, M) {
              var D = S - 60 * w * 1e3,
                T = g(D, M);
              if (w === T) return [D, w];
              var k = g((D -= 60 * (T - w) * 1e3), M);
              return T === k
                ? [D, T]
                : [S - 60 * Math.min(T, k) * 1e3, Math.max(T, k)];
            })(a.utc(m, f).valueOf(), h, i),
            l = v[0],
            x = v[1],
            y = a(l).utcOffset(x);
          return (y.$x.$timezone = i), y;
        }),
        (a.tz.guess = function () {
          return Intl.DateTimeFormat().resolvedOptions().timeZone;
        }),
        (a.tz.setDefault = function (m) {
          s = m;
        });
    };
  });
})($t);
var Lt = $t.exports,
  X =
    (globalThis && globalThis.__assign) ||
    function () {
      return (
        (X =
          Object.assign ||
          function (t) {
            for (var e, n = 1, r = arguments.length; n < r; n++) {
              e = arguments[n];
              for (var c in e)
                Object.prototype.hasOwnProperty.call(e, c) && (t[c] = e[c]);
            }
            return t;
          }),
        X.apply(this, arguments)
      );
    },
  At =
    (globalThis && globalThis.__read) ||
    function (t, e) {
      var n = typeof Symbol == 'function' && t[Symbol.iterator];
      if (!n) return t;
      var r = n.call(t),
        c,
        o = [],
        a;
      try {
        for (; (e === void 0 || e-- > 0) && !(c = r.next()).done; )
          o.push(c.value);
      } catch (s) {
        a = { error: s };
      } finally {
        try {
          c && !c.done && (n = r.return) && n.call(r);
        } finally {
          if (a) throw a.error;
        }
      }
      return o;
    },
  Ht =
    (globalThis && globalThis.__spreadArray) ||
    function (t, e, n) {
      if (n || arguments.length === 2)
        for (var r = 0, c = e.length, o; r < c; r++)
          (o || !(r in e)) &&
            (o || (o = Array.prototype.slice.call(e, 0, r)), (o[r] = e[r]));
      return t.concat(o || Array.prototype.slice.call(e));
    },
  _ = z()._isAMomentObject;
_ && (z.extend = function () {});
var Ut = function (t, e, n) {
  n = function (o, a) {
    if (q(o)) return o.clone();
    var s = typeof a == 'object' ? a : {};
    return (s.date = o), (s.args = arguments), new e(s);
  };
  var r = e.prototype,
    c = r.$utils;
  (r.$utils = function () {
    var o = c();
    return (o.i = q), o;
  }),
    (n.isDayjs = q);
};
z.extend(Ut);
z.extend(Mt);
z.extend(Yt);
z.extend(Tt);
z.extend(St);
z.extend(kt);
z.extend(bt);
z.extend(zt);
z.extend(Lt);
var Y = z;
function ft(t, e) {
  var n = (t.day() - e + 7) % 7,
    r = t.clone().startOf('day').subtract(n, 'day');
  return r.valueOf();
}
function Ct(t, e, n) {
  return ft(t, n) === ft(e, n);
}
var nt = {
    add: function (t, e, n) {
      return _ ? t.clone().add(e, n) : t.add(e, n);
    },
    subtract: function (t, e, n) {
      return _ ? t.clone().subtract(e, n) : t.subtract(e, n);
    },
    startOf: function (t, e) {
      return _ ? t.clone().startOf(e) : t.startOf(e);
    },
    endOf: function (t, e) {
      return _ ? t.clone().endOf(e) : t.endOf(e);
    },
    set: function (t, e, n) {
      return _ ? t.clone().set(e, n) : t.set(e, n);
    },
    isSameWeek: function (t, e, n, r) {
      return _
        ? Ct(t, e, n)
        : t.locale(X(X({}, Y.Ls[r]), { weekStart: n })).isSame(e, 'week');
    },
  },
  Wt = { year: 0, month: 1, day: 2, hour: 3, minute: 4, second: 5 },
  ut = {},
  Zt = function (t, e) {
    var n = t + '|' + (e || 'short'),
      r = ut[n];
    return (
      r ||
        ((r = new Intl.DateTimeFormat('en-US', {
          hour12: !1,
          timeZone: t,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        })),
        (ut[n] = r)),
      r
    );
  },
  _t = function (t, e) {
    var n = new Date(t),
      r = Zt(e);
    return r.formatToParts(n);
  },
  at = function (t, e) {
    for (var n = _t(t, e), r = [], c = 0; c < n.length; c += 1) {
      var o = n[c],
        a = o.type,
        s = o.value,
        u = Wt[a];
      u >= 0 && (r[u] = parseInt(s, 10));
    }
    var g = r[3],
      d = g === 24 ? 0 : g,
      O = Date.UTC(r[0], r[1] - 1, r[2], d, r[4], r[5], 0),
      m = +t,
      p = m % 1e3;
    return (m -= p), (O - m) / (60 * 1e3);
  },
  Ft = function (t, e, n) {
    var r = t - e * 60 * 1e3,
      c = at(r, n);
    if (e === c) return [r, e];
    r -= (c - e) * 60 * 1e3;
    var o = at(r, n);
    return c === o ? [r, c] : [t - Math.min(c, o) * 60 * 1e3, Math.max(c, o)];
  };
function ct(t, e) {
  var n = at(t, e);
  return Ft(t, n, e)[1];
}
function Pt(t, e) {
  return Q(t) && !e ? Y() : it(Y(), t, e);
}
function it(t, e, n, r) {
  if (!t || (Q(e) && !n)) return t;
  var c = -t.toDate().getTimezoneOffset(),
    o = Q(e) ? (n ? ct(t.valueOf(), n) : c) : e,
    a = Math.abs(o) <= 16 ? o * 60 : o,
    s = r ? c - a : a - c,
    u = n ? ct(Y(t).valueOf() + s * 60 * 1e3, n) : o,
    g = s - (o - u),
    d = r || !Q(e) ? s : g;
  return Y(Y(t).valueOf() + d * 60 * 1e3);
}
function Bt(t, e, n) {
  return it(t, e, n, !0);
}
function It(t) {
  var e = ['H', 'h', 'm', 's', 'A', 'a'],
    n = '';
  return (
    e.some(function (r) {
      return t.indexOf(r) !== -1
        ? ((n = '' + r + t.split(' ' + r)[1]), !0)
        : !1;
    }),
    n || 'HH:mm:ss'
  );
}
function Qt(t, e, n, r) {
  if (!!t) {
    var c = function (a, s) {
        if (q(a)) return Y(a.valueOf());
        if (typeof a == 'string') {
          var u = Y(a, j(e) ? e[s] : e);
          return u.isValid() ? u : Y(a, 'YYYY-MM-DD');
        }
        return Y(a);
      },
      o = function (a, s) {
        return n !== void 0 || r ? it(c(a, s), n, r) : c(a, s);
      };
    return j(t)
      ? t.map(function (a, s) {
          return a ? o(a, s) : void 0;
        })
      : o(t, 0);
  }
}
function qt(t, e) {
  var n = t.year(),
    r = t.month(),
    c = t.date();
  if (e) {
    var o = e;
    return (
      (o = nt.set(o, 'year', n)),
      (o = nt.set(o, 'month', r)),
      (o = nt.set(o, 'date', c)),
      o
    );
  }
  return t;
}
function Xt(t) {
  if (!t || !t[0] || !t[1]) return t;
  var e = Ht([], At(t), !1);
  return (
    e.sort(function (n, r) {
      return n.valueOf() - r.valueOf();
    }),
    e
  );
}
function Et(t, e) {
  return e === void 0 && t === void 0
    ? !1
    : (e && !t) || (!e && t) || Y(e).valueOf() !== Y(t).valueOf();
}
function Rt(t, e) {
  return e === void 0 && t === void 0
    ? !1
    : (e && !t) ||
        (!e && t) ||
        (j(e) && j(t) && Y(e[0]).valueOf() !== Y(t[0]).valueOf()) ||
        Y(e[1]).valueOf() !== Y(t[1]).valueOf();
}
function Kt(t, e, n) {
  return typeof t == 'string' && Y(t, e).format(j(e) ? e[n] : e) === t;
}
function jt(t, e, n) {
  n === void 0 && (n = ' ');
  var r = String(t);
  if (!e) return r;
  var c = r.length < e ? '' + n + r : r;
  return c.length < e ? jt(c, e, n) : c;
}
export {
  Pt as a,
  it as b,
  qt as c,
  Y as d,
  Xt as e,
  It as f,
  Qt as g,
  Rt as h,
  Et as i,
  Kt as j,
  nt as m,
  jt as p,
  Bt as t,
};
