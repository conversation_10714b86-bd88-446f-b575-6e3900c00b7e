.arco-picker {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 4px 11px 4px 4px;
  line-height: 1.5715;
  border-radius: var(--border-radius-small);
  background-color: var(--color-fill-2);
  border: 1px solid transparent;
  box-sizing: border-box;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-picker-input {
  display: inline-flex;
  flex: 1;
}
.arco-picker input {
  text-align: left;
  padding: 0 0 0 8px;
  border: none;
  width: 100%;
  color: var(--color-text-1);
  background-color: transparent;
  line-height: 1.5715;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-picker input::placeholder {
  color: var(--color-text-3);
}
.arco-picker-input-placeholder input {
  color: var(--color-text-3);
}
.arco-picker-has-prefix {
  padding-left: 12px;
}
.arco-picker-prefix {
  color: var(--color-text-2);
  padding-right: 4px;
  font-size: 14px;
}
.arco-picker-suffix {
  width: 14px;
  margin-left: 4px;
  text-align: center;
}
.arco-picker-suffix-icon {
  color: var(--color-text-2);
}
.arco-picker .arco-picker-clear-icon {
  display: none;
  font-size: 12px;
  color: var(--color-text-2);
}
.arco-picker:hover {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-picker:not(.arco-picker-disabled):hover .arco-picker-clear-icon {
  display: inline-block;
}
.arco-picker:not(.arco-picker-disabled):hover
  .arco-picker-suffix
  .arco-picker-clear-icon
  + span {
  display: none;
}
.arco-picker-focused {
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-picker-focused,
.arco-picker-focused:hover {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--primary-6));
}
.arco-picker-focused .arco-picker-input-active input,
.arco-picker-focused:hover .arco-picker-input-active input {
  background: var(--color-primary-light-1);
}
.arco-picker-error:not(.arco-picker-disabled) {
  border-color: transparent;
  background-color: var(--color-danger-light-1);
}
.arco-picker-error:not(.arco-picker-disabled):hover {
  border-color: transparent;
  background-color: var(--color-danger-light-2);
}
.arco-picker-error.arco-picker-focused:not(.arco-picker-disabled),
.arco-picker-error.arco-picker-focused:not(.arco-picker-disabled):hover {
  border-color: rgb(var(--danger-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-picker-warning:not(.arco-picker-disabled) {
  border-color: transparent;
  background-color: var(--color-warning-light-1);
}
.arco-picker-warning:not(.arco-picker-disabled):hover {
  border-color: transparent;
  background-color: var(--color-warning-light-2);
}
.arco-picker-warning.arco-picker-focused:not(.arco-picker-disabled),
.arco-picker-warning.arco-picker-focused:not(.arco-picker-disabled):hover {
  border-color: rgb(var(--warning-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-picker input[disabled] {
  cursor: not-allowed;
  color: var(--color-text-4);
  -webkit-text-fill-color: var(--color-text-4);
}
.arco-picker input[disabled]::placeholder {
  color: var(--color-text-4);
}
.arco-picker-disabled,
.arco-picker-disabled:hover {
  color: var(--color-text-4);
  border-color: transparent;
  background-color: var(--color-fill-2);
  cursor: not-allowed;
}
.arco-picker-disabled input[disabled],
.arco-picker-disabled:hover input[disabled] {
  cursor: not-allowed;
  color: var(--color-text-4);
  -webkit-text-fill-color: var(--color-text-4);
}
.arco-picker-disabled input[disabled]::placeholder,
.arco-picker-disabled:hover input[disabled]::placeholder {
  color: var(--color-text-4);
}
.arco-picker-separator {
  min-width: 10px;
  padding: 0 8px;
  color: var(--color-text-3);
}
.arco-picker-disabled .arco-picker-separator,
.arco-picker-disabled .arco-picker-suffix-icon {
  color: var(--color-text-4);
}
.arco-picker-size-mini {
  height: 24px;
}
.arco-picker-size-mini input {
  font-size: 12px;
}
.arco-picker-size-small {
  height: 28px;
}
.arco-picker-size-small input {
  font-size: 14px;
}
.arco-picker-size-default {
  height: 32px;
}
.arco-picker-size-default input {
  font-size: 14px;
}
.arco-picker-size-large {
  height: 36px;
}
.arco-picker-size-large input {
  font-size: 14px;
}
.arco-picker-rtl {
  direction: rtl;
  padding: 4px 4px 4px 11px;
}
.arco-picker-rtl input {
  text-align: right;
  padding-left: 0;
  padding-right: 8px;
}
.arco-picker-rtl .arco-picker-suffix {
  margin-left: 0;
  margin-right: 4px;
}
.arco-timepicker {
  position: relative;
  display: flex;
  padding: 0;
  box-sizing: border-box;
}
.arco-timepicker-container {
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--color-neutral-3);
  background-color: var(--color-bg-popup);
  box-shadow: 0 2px 5px #0000001a;
  overflow: hidden;
}
.arco-timepicker-list {
  width: 64px;
  height: 224px;
  overflow: hidden;
  box-sizing: border-box;
  scrollbar-width: none;
}
.arco-timepicker-list::-webkit-scrollbar {
  width: 0;
}
.arco-timepicker-list:not(:last-child) {
  border-right: 1px solid var(--color-neutral-3);
}
.arco-timepicker-list:hover {
  overflow-y: auto;
}
.arco-timepicker-list ul {
  margin: 0;
  padding: 0;
  list-style: none;
  box-sizing: border-box;
}
.arco-timepicker-list ul:after {
  content: '';
  display: block;
  width: 100%;
  height: 192px;
}
.arco-timepicker-cell {
  padding: 8px / 2 0;
  text-align: center;
  color: var(--color-text-1);
  font-weight: 500;
  cursor: pointer;
}
.arco-timepicker-cell-inner {
  height: 24px;
  line-height: 24px;
  font-size: 14px;
}
.arco-timepicker-cell:not(.arco-timepicker-cell-selected):not(
    .arco-timepicker-cell-disabled
  ):hover
  .arco-timepicker-cell-inner {
  background-color: var(--color-fill-2);
}
.arco-timepicker-cell-selected .arco-timepicker-cell-inner {
  background-color: var(--color-fill-2);
  font-weight: 500;
}
.arco-timepicker-cell-disabled {
  color: var(--color-text-4);
  cursor: not-allowed;
}
.arco-timepicker-footer-extra-wrapper {
  border-top: 1px solid var(--color-neutral-3);
  padding: 8px;
  color: var(--color-text-1);
  font-size: 12px;
}
.arco-timepicker-footer-btn-wrapper {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid var(--color-neutral-3);
  padding: 8px;
}
.arco-picker {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 4px 11px 4px 4px;
  line-height: 1.5715;
  border-radius: var(--border-radius-small);
  background-color: var(--color-fill-2);
  border: 1px solid transparent;
  box-sizing: border-box;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-picker-input {
  display: inline-flex;
  flex: 1;
}
.arco-picker input {
  text-align: left;
  padding: 0 0 0 8px;
  border: none;
  width: 100%;
  color: var(--color-text-1);
  background-color: transparent;
  line-height: 1.5715;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-picker input::placeholder {
  color: var(--color-text-3);
}
.arco-picker-input-placeholder input {
  color: var(--color-text-3);
}
.arco-picker-has-prefix {
  padding-left: 12px;
}
.arco-picker-prefix {
  color: var(--color-text-2);
  padding-right: 4px;
  font-size: 14px;
}
.arco-picker-suffix {
  width: 14px;
  margin-left: 4px;
  text-align: center;
}
.arco-picker-suffix-icon {
  color: var(--color-text-2);
}
.arco-picker .arco-picker-clear-icon {
  display: none;
  font-size: 12px;
  color: var(--color-text-2);
}
.arco-picker:hover {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-picker:not(.arco-picker-disabled):hover .arco-picker-clear-icon {
  display: inline-block;
}
.arco-picker:not(.arco-picker-disabled):hover
  .arco-picker-suffix
  .arco-picker-clear-icon
  + span {
  display: none;
}
.arco-picker-focused {
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-picker-focused,
.arco-picker-focused:hover {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--primary-6));
}
.arco-picker-focused .arco-picker-input-active input,
.arco-picker-focused:hover .arco-picker-input-active input {
  background: var(--color-primary-light-1);
}
.arco-picker-error:not(.arco-picker-disabled) {
  border-color: transparent;
  background-color: var(--color-danger-light-1);
}
.arco-picker-error:not(.arco-picker-disabled):hover {
  border-color: transparent;
  background-color: var(--color-danger-light-2);
}
.arco-picker-error.arco-picker-focused:not(.arco-picker-disabled),
.arco-picker-error.arco-picker-focused:not(.arco-picker-disabled):hover {
  border-color: rgb(var(--danger-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-picker-warning:not(.arco-picker-disabled) {
  border-color: transparent;
  background-color: var(--color-warning-light-1);
}
.arco-picker-warning:not(.arco-picker-disabled):hover {
  border-color: transparent;
  background-color: var(--color-warning-light-2);
}
.arco-picker-warning.arco-picker-focused:not(.arco-picker-disabled),
.arco-picker-warning.arco-picker-focused:not(.arco-picker-disabled):hover {
  border-color: rgb(var(--warning-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-picker input[disabled] {
  cursor: not-allowed;
  color: var(--color-text-4);
  -webkit-text-fill-color: var(--color-text-4);
}
.arco-picker input[disabled]::placeholder {
  color: var(--color-text-4);
}
.arco-picker-disabled,
.arco-picker-disabled:hover {
  color: var(--color-text-4);
  border-color: transparent;
  background-color: var(--color-fill-2);
  cursor: not-allowed;
}
.arco-picker-disabled input[disabled],
.arco-picker-disabled:hover input[disabled] {
  cursor: not-allowed;
  color: var(--color-text-4);
  -webkit-text-fill-color: var(--color-text-4);
}
.arco-picker-disabled input[disabled]::placeholder,
.arco-picker-disabled:hover input[disabled]::placeholder {
  color: var(--color-text-4);
}
.arco-picker-separator {
  min-width: 10px;
  padding: 0 8px;
  color: var(--color-text-3);
}
.arco-picker-disabled .arco-picker-separator,
.arco-picker-disabled .arco-picker-suffix-icon {
  color: var(--color-text-4);
}
.arco-picker-size-mini {
  height: 24px;
}
.arco-picker-size-mini input {
  font-size: 12px;
}
.arco-picker-size-small {
  height: 28px;
}
.arco-picker-size-small input {
  font-size: 14px;
}
.arco-picker-size-default {
  height: 32px;
}
.arco-picker-size-default input {
  font-size: 14px;
}
.arco-picker-size-large {
  height: 36px;
}
.arco-picker-size-large input {
  font-size: 14px;
}
.arco-picker-rtl {
  direction: rtl;
  padding: 4px 4px 4px 11px;
}
.arco-picker-rtl input {
  text-align: right;
  padding-left: 0;
  padding-right: 8px;
}
.arco-picker-rtl .arco-picker-suffix {
  margin-left: 0;
  margin-right: 4px;
}
.arco-picker-container,
.arco-picker-range-container {
  border: 1px solid var(--color-neutral-3);
  box-shadow: 0 2px 5px #0000001a;
  border-radius: var(--border-radius-medium);
  background-color: var(--color-bg-popup);
  box-sizing: border-box;
  min-height: 60px;
  overflow: hidden;
}
.arco-picker-container-shortcuts-placement-left,
.arco-picker-range-container-shortcuts-placement-left {
  display: flex;
  align-items: flex-start;
}
.arco-picker-container-shortcuts-placement-left .arco-picker-shortcuts,
.arco-picker-range-container-shortcuts-placement-left .arco-picker-shortcuts {
  display: flex;
  flex-direction: column;
  padding: 5px 8px;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;
}
.arco-picker-container-shortcuts-placement-left .arco-picker-shortcuts > *,
.arco-picker-range-container-shortcuts-placement-left
  .arco-picker-shortcuts
  > * {
  margin: 5px 0;
}
.arco-picker-container-shortcuts-placement-left .arco-picker-panel-wrapper,
.arco-picker-range-container-shortcuts-placement-left
  .arco-picker-panel-wrapper,
.arco-picker-container-shortcuts-placement-left
  .arco-picker-range-panel-wrapper,
.arco-picker-range-container-shortcuts-placement-left
  .arco-picker-range-panel-wrapper {
  border-left: 1px solid var(--color-neutral-3);
}
.arco-picker-panel-only,
.arco-picker-range-panel-only {
  box-shadow: none;
}
.arco-picker-panel-only .arco-panel-date-inner,
.arco-picker-range-panel-only .arco-panel-date-inner,
.arco-picker-range-panel-only .arco-panel-date,
.arco-picker-range-panel-only .arco-panel-month,
.arco-picker-range-panel-only .arco-panel-year {
  width: 100%;
}
.arco-picker-header {
  display: flex;
  padding: 8px 16px;
  border-bottom: 1px solid var(--color-neutral-3);
}
.arco-picker-header-value {
  font-size: 14px;
  line-height: 24px;
  flex: 1;
  text-align: center;
  color: var(--color-text-1);
  font-weight: 500;
  box-sizing: border-box;
}
.arco-picker-header-icon {
  border-radius: 50%;
  text-align: center;
  font-size: 12px;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
  user-select: none;
  color: var(--color-text-2);
  background-color: var(--color-bg-popup);
  width: 24px;
  height: 24px;
  line-height: 24px;
  margin-left: 2px;
  margin-right: 2px;
  box-sizing: border-box;
}
.arco-picker-header-icon:not(.arco-picker-header-icon-hidden) {
  cursor: pointer;
}
.arco-picker-header-icon:not(.arco-picker-header-icon-hidden):hover {
  background-color: var(--color-fill-3);
}
.arco-picker-header-label {
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  transition: all 0.1s;
}
.arco-picker-header-label:hover {
  background-color: var(--color-fill-3);
}
.arco-picker-body {
  padding: 14px 16px;
}
.arco-picker-week-list {
  display: flex;
  width: 100%;
  box-sizing: border-box;
  padding: 14px 16px 0;
}
.arco-picker-week-list-item {
  color: var(--color-text-2);
  flex: 1;
  padding: 0;
  text-align: center;
  font-weight: 500;
  height: 32px;
  line-height: 32px;
}
.arco-picker-row {
  display: flex;
}
.arco-picker-cell {
  position: relative;
  flex: 1;
  cursor: pointer;
  padding: 2px 0;
}
.arco-picker-cell .arco-picker-date {
  display: flex;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  cursor: pointer;
  padding: 4px 0;
  justify-content: center;
}
.arco-picker-date-value {
  color: var(--color-text-4);
  font-size: 14px;
  min-width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 24px;
  font-weight: 500;
}
.arco-picker-cell-in-view .arco-picker-date-value {
  color: var(--color-text-1);
  font-weight: 500;
}
.arco-picker-cell:hover .arco-picker-date-value {
  background-color: var(--color-fill-3);
  color: var(--color-text-1);
}
.arco-picker-cell-today {
  position: relative;
}
.arco-picker-cell-today:after {
  content: '';
  display: block;
  position: absolute;
  bottom: -2px;
  left: 50%;
  margin-left: -2px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: rgb(var(--primary-6));
}
.arco-picker-cell-in-range .arco-picker-date {
  background-color: var(--color-primary-light-1);
}
.arco-picker-cell-range-start .arco-picker-date,
.arco-picker-cell-hover-range-start .arco-picker-date {
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
}
.arco-picker-cell-range-end .arco-picker-date,
.arco-picker-cell-hover-range-end .arco-picker-date {
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
}
.arco-picker-cell-range-start:hover .arco-picker-date-value,
.arco-picker-cell-range-end:hover .arco-picker-date-value {
  background-color: unset;
}
.arco-picker-cell-disabled .arco-picker-date {
  background-color: var(--color-fill-1);
  cursor: not-allowed;
}
.arco-picker-cell-disabled .arco-picker-date-value,
.arco-picker-cell-disabled:hover .arco-picker-date-value {
  color: var(--color-text-4);
  background-color: transparent;
}
.arco-picker-cell-selected .arco-picker-date-value,
.arco-picker-cell-selected:hover .arco-picker-date-value {
  color: var(--color-white);
  background-color: rgb(var(--primary-6));
  transition: background-color 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-picker-cell-hover-in-range .arco-picker-date,
.arco-picker-cell-hover-range-start:not(.arco-picker-cell-range-start):not(
    .arco-picker-cell-range-end
  )
  .arco-picker-date-value,
.arco-picker-cell-hover-range-end:not(.arco-picker-cell-range-start):not(
    .arco-picker-cell-range-end
  )
  .arco-picker-date-value {
  background-color: var(--color-primary-light-2);
}
.arco-picker-cell-range-edge-in-hover-range .arco-picker-date,
.arco-picker-cell-hover-range-edge-in-range .arco-picker-date {
  border-radius: 0;
}
.arco-picker-cell-hidden .arco-picker-date {
  display: none;
}
.arco-picker-footer {
  width: min-content;
  min-width: 100%;
}
.arco-picker-footer-btn-wrapper {
  border-top: 1px solid var(--color-neutral-3);
  padding: 3px 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
}
.arco-picker-footer-extra-wrapper {
  padding: 8px 24px;
  color: var(--color-text-1);
  border-top: 1px solid var(--color-neutral-3);
  box-sizing: border-box;
  font-size: 12px;
}
.arco-picker-footer-now-wrapper {
  border-top: 1px solid var(--color-neutral-3);
  box-sizing: border-box;
  height: 36px;
  line-height: 36px;
  text-align: center;
}
.arco-picker-btn-select-date,
.arco-picker-btn-select-time {
  margin-right: 8px;
}
.arco-picker-btn-confirm {
  margin: 5px 0;
}
.arco-picker-shortcuts {
  flex: 1;
}
.arco-picker-shortcuts > * {
  margin: 5px 10px 5px 0;
}
.arco-panel-date {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
}
.arco-panel-date-inner {
  width: 265px;
}
.arco-panel-date-inner .arco-picker-body {
  padding-top: 0;
}
.arco-panel-date-timepicker {
  display: flex;
  flex-direction: column;
}
.arco-panel-date-timepicker-title {
  width: 100%;
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid var(--color-neutral-3);
  color: var(--color-text-1);
}
.arco-panel-date-timepicker .arco-timepicker {
  width: 265px;
  height: 276px;
  padding: 0 6px;
  overflow: hidden;
}
.arco-panel-date-timepicker .arco-timepicker-list {
  width: 100%;
  height: 100%;
  padding: 0 4px;
  box-sizing: border-box;
}
.arco-panel-date-timepicker .arco-timepicker-list:not(:last-child) {
  border-right: 0;
}
.arco-panel-date-timepicker .arco-timepicker ul:after {
  height: 244px;
}
.arco-panel-date-timepicker .arco-timepicker-cell {
  width: 100%;
}
.arco-panel-date-holder {
  display: flex;
  width: 100%;
  border-top: 1px solid var(--color-neutral-3);
}
.arco-panel-date-holder-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 50px;
  box-sizing: border-box;
  cursor: pointer;
  color: var(--color-text-3);
  font-size: 16px;
  transition: color 0.2s;
}
.arco-panel-date-holder-btn:first-child {
  border-right: 1px solid var(--color-neutral-3);
}
.arco-panel-date-holder-btn:hover,
.arco-panel-date-holder-btn:active,
.arco-panel-date-holder-btn-active {
  color: var(--color-text-1);
}
.arco-panel-date-holder-btn-value {
  margin-left: 8px;
}
.arco-panel-date:first-child .arco-panel-date-holder {
  border-right: 1px solid var(--color-neutral-3);
}
.arco-panel-month,
.arco-panel-quarter,
.arco-panel-year {
  box-sizing: border-box;
  width: 265px;
}
.arco-panel-month .arco-picker-date,
.arco-panel-quarter .arco-picker-date,
.arco-panel-year .arco-picker-date {
  padding: 4px;
}
.arco-panel-month .arco-picker-date-value,
.arco-panel-quarter .arco-picker-date-value,
.arco-panel-year .arco-picker-date-value {
  border-radius: 24px;
  width: 100%;
}
.arco-panel-month
  .arco-picker-cell:not(.arco-picker-cell-selected):not(
    .arco-picker-cell-range-start
  ):not(.arco-picker-cell-range-end):not(.arco-picker-cell-disabled):not(
    .arco-picker-cell-week
  )
  .arco-picker-date-value:hover,
.arco-panel-quarter
  .arco-picker-cell:not(.arco-picker-cell-selected):not(
    .arco-picker-cell-range-start
  ):not(.arco-picker-cell-range-end):not(.arco-picker-cell-disabled):not(
    .arco-picker-cell-week
  )
  .arco-picker-date-value:hover,
.arco-panel-year
  .arco-picker-cell:not(.arco-picker-cell-selected):not(
    .arco-picker-cell-range-start
  ):not(.arco-picker-cell-range-end):not(.arco-picker-cell-disabled):not(
    .arco-picker-cell-week
  )
  .arco-picker-date-value:hover {
  border-radius: 24px;
}
.arco-panel-year {
  box-sizing: border-box;
  width: 265px;
}
.arco-panel-week {
  box-sizing: border-box;
}
.arco-panel-week-wrapper {
  display: flex;
}
.arco-panel-week-inner {
  width: 298px;
}
.arco-panel-week-inner .arco-picker-body {
  padding-top: 0;
}
.arco-panel-week .arco-picker-row-week {
  cursor: pointer;
}
.arco-panel-week .arco-picker-row-week .arco-picker-date-value {
  width: 100%;
  border-radius: 0;
}
.arco-panel-week .arco-picker-cell .arco-picker-date {
  border-radius: 0;
}
.arco-panel-week .arco-picker-cell:nth-child(2) .arco-picker-date {
  padding-left: 4px;
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
}
.arco-panel-week
  .arco-picker-cell:nth-child(2)
  .arco-picker-date
  .arco-picker-date-value {
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
}
.arco-panel-week .arco-picker-cell:nth-child(8) .arco-picker-date {
  padding-right: 4px;
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
}
.arco-panel-week
  .arco-picker-cell:nth-child(8)
  .arco-picker-date
  .arco-picker-date-value {
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
}
.arco-panel-week
  .arco-picker-row-week:hover
  .arco-picker-cell:not(.arco-picker-cell-week):not(
    .arco-picker-cell-selected
  ):not(.arco-picker-cell-range-start):not(.arco-picker-cell-range-end):not(
    .arco-picker-cell-in-range
  ):not(.arco-picker-cell-hover-in-range)
  .arco-picker-date-value {
  background-color: var(--color-fill-3);
}
.arco-panel-quarter {
  box-sizing: border-box;
  width: 265px;
}
.arco-picker-range-wrapper {
  display: flex;
}
.arco-datepicker-shortcuts-wrapper {
  width: 106px;
  height: 100%;
  max-height: 300px;
  overflow-y: auto;
  box-sizing: border-box;
  list-style: none;
  padding: 0;
  margin: 10px 0 0;
}
.arco-datepicker-shortcuts-wrapper > li {
  width: 100%;
  padding: 6px 16px;
  cursor: pointer;
  box-sizing: border-box;
}
.arco-datepicker-shortcuts-wrapper > li:hover {
  color: rgb(var(--primary-6));
}
.arco-picker-container-rtl,
.arco-picker-range-container-rtl {
  direction: rtl;
}
.arco-picker-container-rtl .arco-picker-cell-range-start .arco-picker-date,
.arco-picker-range-container-rtl
  .arco-picker-cell-range-start
  .arco-picker-date,
.arco-picker-container-rtl
  .arco-picker-cell-hover-range-start
  .arco-picker-date,
.arco-picker-range-container-rtl
  .arco-picker-cell-hover-range-start
  .arco-picker-date {
  border-radius: 0 24px 24px 0;
}
.arco-picker-container-rtl .arco-picker-cell-range-end .arco-picker-date,
.arco-picker-range-container-rtl .arco-picker-cell-range-end .arco-picker-date,
.arco-picker-container-rtl .arco-picker-cell-hover-range-end .arco-picker-date,
.arco-picker-range-container-rtl
  .arco-picker-cell-hover-range-end
  .arco-picker-date {
  border-radius: 24px 0 0 24px;
}
.arco-picker-container-rtl
  .arco-panel-week
  .arco-picker-cell:nth-child(2)
  .arco-picker-date,
.arco-picker-range-container-rtl
  .arco-panel-week
  .arco-picker-cell:nth-child(2)
  .arco-picker-date {
  padding-right: 4px;
  padding-left: 0;
  border-radius: 0 24px 24px 0;
}
.arco-picker-container-rtl
  .arco-panel-week
  .arco-picker-cell:nth-child(2)
  .arco-picker-date
  .arco-picker-date-value,
.arco-picker-range-container-rtl
  .arco-panel-week
  .arco-picker-cell:nth-child(2)
  .arco-picker-date
  .arco-picker-date-value {
  border-radius: 0 24px 24px 0;
}
.arco-picker-container-rtl
  .arco-panel-week
  .arco-picker-cell:nth-child(8)
  .arco-picker-date,
.arco-picker-range-container-rtl
  .arco-panel-week
  .arco-picker-cell:nth-child(8)
  .arco-picker-date {
  padding-left: 4px;
  padding-right: 0;
  border-radius: 24px 0 0 24px;
}
.arco-picker-container-rtl
  .arco-panel-week
  .arco-picker-cell:nth-child(8)
  .arco-picker-date
  .arco-picker-date-value,
.arco-picker-range-container-rtl
  .arco-panel-week
  .arco-picker-cell:nth-child(8)
  .arco-picker-date
  .arco-picker-date-value {
  border-radius: 24px 0 0 24px;
}
