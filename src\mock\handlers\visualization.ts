import { http, HttpResponse } from 'msw';
import dayjs from 'dayjs';
import { getRandomArray } from '@/utils/getRandomArray';

// 通用的数据分析处理程序（对应数据可视化模块）
const visualizationHandlers = [
  // 多维数据分析接口
  http.get('/api/multi-dimension/overview', () => {
    const year = new Date().getFullYear();
    const getLineData = (name: string, colorIndex: number) => {
      return {
        name,
        color: colorIndex,
        count: getRandomArray(12, 1000, 5000),
      };
    };
    return HttpResponse.json({
      status: 'success',
      data: {
        overviewData: [
          {
            type: '总内容量',
            count: 833454,
            percent: 100,
          },
          {
            type: '生活服务',
            count: 346292,
            percent: 41.55,
          },
          {
            type: '家政服务',
            count: 166057,
            percent: 19.92,
          },
          {
            type: '软装家居',
            count: 243284,
            percent: 29.19,
          },
          {
            type: '商业服务',
            count: 77821,
            percent: 9.34,
          },
        ],
        timelineData: [
          getLineData('总内容量', 0),
          getLineData('生活服务', 1),
          getLineData('家政服务', 2),
          getLineData('软装家居', 3),
          getLineData('商业服务', 4),
        ],
        timeArray: new Array(12).fill(0).map((_, index) => {
          return dayjs().month(index).year(year).format('YYYY-MM');
        }),
      },
    });
  }),

  // 多维度数据分析 - 流量概览
  http.get('/api/multi-dimension/traffic', () => {
    return HttpResponse.json({
      status: 'success',
      data: {
        allCount: 1735324,
        userCount: 1023856,
        userRate: 0.9,
        contentCount: 389197,
        contentRate: 0.42,
        interactionCount: 322271,
        interactionRate: 0.75,
      },
    });
  }),

  // 多维度数据分析 - 来源分析
  http.get('/api/multi-dimension/source', () => {
    return HttpResponse.json({
      status: 'success',
      data: [
        {
          type: '搜索引擎',
          count: 176884,
          percent: 19.92,
        },
        {
          type: '直接访问',
          count: 155879,
          percent: 17.46,
        },
        {
          type: '内部链接',
          count: 166470,
          percent: 18.7,
        },
        {
          type: '社交媒体',
          count: 212028,
          percent: 24.19,
        },
        {
          type: '其他网站',
          count: 131667,
          percent: 14.73,
        },
        {
          type: '电子邮件',
          count: 45311,
          percent: 5,
        },
      ],
    });
  }),

  // 多维度数据分析 - 用户行为
  http.get('/api/multi-dimension/user-behavior', () => {
    return HttpResponse.json({
      status: 'success',
      data: [
        {
          name: '浏览量',
          count: 50000,
          increment: 10.21,
        },
        {
          name: '下载量',
          count: 28780,
          increment: 8.99,
        },
        {
          name: '安装量',
          count: 23486,
          increment: 7.33,
        },
        {
          name: '活跃量',
          count: 18062,
          increment: -2.3,
        },
      ],
    });
  }),

  // 通用数据分析 API
  http.get('/api/visualization/data-analysis', () => {
    return HttpResponse.json({
      status: 'success',
      data: {
        overview: {
          traffic: [
            {
              name: '总访问量',
              value: '12,361',
              ratio: 10.51,
            },
            {
              name: '新用户数量',
              value: '8,106',
              ratio: 3.12,
            },
            {
              name: '付费用户数量',
              value: '5,021',
              ratio: 5.2,
            },
            {
              name: '付费转化率',
              value: '88%',
              ratio: 22.9,
            },
          ],
          inspiration: [
            {
              name: '内容数量',
              value: '8,126',
              ratio: 12.56,
            },
            {
              name: '内容点击量',
              value: '98,421',
              ratio: 25.9,
            },
            {
              name: '内容曝光量',
              value: '128,813',
              ratio: 21.3,
            },
            {
              name: '活跃用户数量',
              value: '5,631',
              ratio: 12.8,
            },
          ],
        },
      },
    });
  }),
];

export default visualizationHandlers;
