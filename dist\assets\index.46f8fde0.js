import {
  u as p,
  b as a,
  r as d,
  j as s,
  a as e,
  a4 as t,
} from './index.7dafa16d.js';
import { C as n } from './index.519f9d90.js';
import { l as f } from './index.69444b8c.js';
import u from './header.c505e7a4.js';
import b from './info.6396f8de.js';
import g from './security.b29ca502.js';
import h from './verified.0209a64c.js';
import './index.43d26da3.js';
import './index.69569894.js';
import './index.05c05262.js';
import './index.module.5c220b22.js';
import './index.503eee4e.js';
import './b-tween.es.d368a2a1.js';
function V() {
  const r = p(f),
    c = a((i) => i.userInfo),
    o = a((i) => i.userLoading),
    [l, m] = d.exports.useState('basic');
  return s('div', {
    children: [
      e(n, {
        style: { padding: '14px 20px' },
        children: e(u, { userInfo: c, loading: o }),
      }),
      e(n, {
        style: { marginTop: '16px' },
        children: s(t, {
          activeTab: l,
          onChange: m,
          type: 'rounded',
          children: [
            e(
              t.TabPane,
              {
                title: r['userSetting.title.basicInfo'],
                children: e(b, { loading: o }),
              },
              'basic'
            ),
            e(
              t.TabPane,
              { title: r['userSetting.title.security'], children: e(g, {}) },
              'security'
            ),
            e(
              t.TabPane,
              { title: r['userSetting.label.verified'], children: e(h, {}) },
              'verified'
            ),
          ],
        }),
      }),
    ],
  });
}
export { V as default };
