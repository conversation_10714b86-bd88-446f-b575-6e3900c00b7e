.header {
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-text-1);
  border-radius: 4px;
  position: relative;

  :global(.arco-avatar-trigger-icon-button) {
    color: rgb(var(--arcoblue-6));

    :global(.arco-icon) {
      vertical-align: -1px;
    }
  }

  .username {
    font-weight: 500;
    font-size: 16px;
  }

  .user-msg {
    &-text {
      display: inline-block;
      margin-left: 6px;
    }
  }
}

.header::after {
  background: url('../assets/header-banner.png') no-repeat;
  background-size: 100%;
  height: 200px;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.4;
  content: ' ';
}

.header-content {
  position: relative;
  z-index: 2;
}

.wrapper {
  margin-top: 16px;
}

.card-title-wrapper {
  display: flex;
  justify-content: space-between;
}

.list-meta-ellipsis {
  :global(.arco-list-item-meta-content) {
    width: 0;
    flex: 1;
  }
}
