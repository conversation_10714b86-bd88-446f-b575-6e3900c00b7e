.info-wrapper {
  display: flex;
}

.info-avatar {
  :global(.arco-avatar-trigger-icon-button) {
    color: rgb(var(--arcoblue-6));
    right: 0;
    bottom: 0;
    width: 30px;
    height: 30px;
    font-size: 14px;
    box-sizing: border-box;
    border: 2px solid var(--color-white);
  }
}

.info-content {
  flex: 1;
  width: 0;
  margin-left: 60px;
  padding-right: 60px;
}

.verified-tag {
  height: 20px;
  line-height: 20px;
  margin-top: -2px;
}

.edit-btn {
  margin-left: 12px;
}
