import { u, j as o, a, c as i, h as n, d as r } from './index.7dafa16d.js';
import { D as e } from './index.69569894.js';
import { C as d } from './index.519f9d90.js';
import { l as m } from './index.662e0e1f.js';
function b() {
  const t = u(m),
    l = [
      {
        label: o('span', {
          children: [
            a(i.Text, {
              style: { paddingRight: 8 },
              children: t['monitor.studioStatus.mainstream'],
            }),
            t['monitor.studioStatus.bitRate'],
          ],
        }),
        value: '6 Mbps',
      },
      { label: t['monitor.studioStatus.frameRate'], value: '60' },
      {
        label: o('span', {
          children: [
            a(i.Text, {
              style: { paddingRight: 8 },
              children: t['monitor.studioStatus.hotStandby'],
            }),
            t['monitor.studioStatus.bitRate'],
          ],
        }),
        value: '6 Mbps',
      },
      { label: t['monitor.studioStatus.frameRate'], value: '60' },
      {
        label: o('span', {
          children: [
            a(i.Text, {
              style: { paddingRight: 8 },
              children: t['monitor.studioStatus.coldStandby'],
            }),
            t['monitor.studioStatus.bitRate'],
          ],
        }),
        value: '6 Mbps',
      },
      { label: t['monitor.studioStatus.frameRate'], value: '60' },
    ],
    s = [
      { label: t['monitor.studioStatus.line'], value: '\u70ED\u5907' },
      { label: 'CDN', value: 'KS' },
      { label: t['monitor.studioStatus.play'], value: 'FLV' },
      {
        label: t['monitor.studioStatus.pictureQuality'],
        value: '\u539F\u753B',
      },
    ];
  return o(d, {
    children: [
      o(n, {
        align: 'start',
        children: [
          a(i.Title, {
            style: { marginTop: 0, marginBottom: 16 },
            heading: 6,
            children: t['monitor.studioStatus.title.studioStatus'],
          }),
          a(r, { color: 'green', children: t['monitor.studioStatus.smooth'] }),
        ],
      }),
      a(e, { colon: ': ', layout: 'horizontal', data: l, column: 2 }),
      a(i.Title, {
        style: { marginBottom: 16 },
        heading: 6,
        children: t['monitor.studioStatus.title.pictureInfo'],
      }),
      a(e, { colon: ': ', layout: 'horizontal', data: s, column: 2 }),
    ],
  });
}
export { b as default };
