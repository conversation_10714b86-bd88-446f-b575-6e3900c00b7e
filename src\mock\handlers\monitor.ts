import { http, HttpResponse } from 'msw';

const monitorHandlers = [
  http.get('/api/chatList', () => {
    const data = Array.from(
      { length: Math.floor(Math.random() * 3) + 4 },
      (_, i) => ({
        id: i + 1,
        username: '用户7352772',
        content: '马上就开始了，好激动！',
        time: '13:09:12',
        isCollect: Math.random() > 0.5,
      })
    );

    return HttpResponse.json(data);
  }),
];

export default monitorHandlers;
