import { l as e } from './index.1a52f4db.js';
import { b as n, r as m } from './index.7dafa16d.js';
const c = e.getTheme('dark');
e.registerTheme('darkTheme', { ...c, background: 'transparent' });
function d() {
  const t = n((r) => r.theme),
    a = t === 'dark' ? 'darkTheme' : 'light',
    [s, h] = m.exports.useState(e.getTheme(a));
  return (
    m.exports.useEffect(() => {
      const r = t === 'dark' ? 'darkTheme' : 'light',
        o = e.getTheme(r);
      h(o);
    }, [t]),
    s
  );
}
export { d as u };
