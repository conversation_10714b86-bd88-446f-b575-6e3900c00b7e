body {
  --red-1: 255, 236, 232;
  --red-2: 253, 205, 197;
  --red-3: 251, 172, 163;
  --red-4: 249, 137, 129;
  --red-5: 247, 101, 96;
  --red-6: 245, 63, 63;
  --red-7: 203, 39, 45;
  --red-8: 161, 21, 30;
  --red-9: 119, 8, 19;
  --red-10: 77, 0, 10;
  --orangered-1: 255, 243, 232;
  --orangered-2: 253, 221, 195;
  --orangered-3: 252, 197, 159;
  --orangered-4: 250, 172, 123;
  --orangered-5: 249, 144, 87;
  --orangered-6: 247, 114, 52;
  --orangered-7: 204, 81, 32;
  --orangered-8: 162, 53, 17;
  --orangered-9: 119, 31, 6;
  --orangered-10: 77, 14, 0;
  --orange-1: 255, 247, 232;
  --orange-2: 255, 228, 186;
  --orange-3: 255, 207, 139;
  --orange-4: 255, 182, 93;
  --orange-5: 255, 154, 46;
  --orange-6: 255, 125, 0;
  --orange-7: 210, 95, 0;
  --orange-8: 166, 69, 0;
  --orange-9: 121, 46, 0;
  --orange-10: 77, 27, 0;
  --gold-1: 255, 252, 232;
  --gold-2: 253, 244, 191;
  --gold-3: 252, 233, 150;
  --gold-4: 250, 220, 109;
  --gold-5: 249, 204, 69;
  --gold-6: 247, 186, 30;
  --gold-7: 204, 146, 19;
  --gold-8: 162, 109, 10;
  --gold-9: 119, 75, 4;
  --gold-10: 77, 45, 0;
  --yellow-1: 254, 255, 232;
  --yellow-2: 254, 254, 190;
  --yellow-3: 253, 250, 148;
  --yellow-4: 252, 242, 107;
  --yellow-5: 251, 232, 66;
  --yellow-6: 250, 220, 25;
  --yellow-7: 207, 175, 15;
  --yellow-8: 163, 132, 8;
  --yellow-9: 120, 93, 3;
  --yellow-10: 77, 56, 0;
  --lime-1: 252, 255, 232;
  --lime-2: 237, 248, 187;
  --lime-3: 220, 241, 144;
  --lime-4: 201, 233, 104;
  --lime-5: 181, 226, 65;
  --lime-6: 159, 219, 29;
  --lime-7: 126, 183, 18;
  --lime-8: 95, 148, 10;
  --lime-9: 67, 112, 4;
  --lime-10: 42, 77, 0;
  --green-1: 232, 255, 234;
  --green-2: 175, 240, 181;
  --green-3: 123, 225, 136;
  --green-4: 76, 210, 99;
  --green-5: 35, 195, 67;
  --green-6: 0, 180, 42;
  --green-7: 0, 154, 41;
  --green-8: 0, 128, 38;
  --green-9: 0, 102, 34;
  --green-10: 0, 77, 28;
  --cyan-1: 232, 255, 251;
  --cyan-2: 183, 244, 236;
  --cyan-3: 137, 233, 224;
  --cyan-4: 94, 223, 214;
  --cyan-5: 55, 212, 207;
  --cyan-6: 20, 201, 201;
  --cyan-7: 13, 165, 170;
  --cyan-8: 7, 130, 139;
  --cyan-9: 3, 97, 108;
  --cyan-10: 0, 66, 77;
  --blue-1: 232, 247, 255;
  --blue-2: 195, 231, 254;
  --blue-3: 159, 212, 253;
  --blue-4: 123, 192, 252;
  --blue-5: 87, 169, 251;
  --blue-6: 52, 145, 250;
  --blue-7: 32, 108, 207;
  --blue-8: 17, 75, 163;
  --blue-9: 6, 48, 120;
  --blue-10: 0, 26, 77;
  --arcoblue-1: 232, 243, 255;
  --arcoblue-2: 190, 218, 255;
  --arcoblue-3: 148, 191, 255;
  --arcoblue-4: 106, 161, 255;
  --arcoblue-5: 64, 128, 255;
  --arcoblue-6: 22, 93, 255;
  --arcoblue-7: 14, 66, 210;
  --arcoblue-8: 7, 44, 166;
  --arcoblue-9: 3, 26, 121;
  --arcoblue-10: 0, 13, 77;
  --purple-1: 245, 232, 255;
  --purple-2: 221, 190, 246;
  --purple-3: 195, 150, 237;
  --purple-4: 168, 113, 227;
  --purple-5: 141, 78, 218;
  --purple-6: 114, 46, 209;
  --purple-7: 85, 29, 176;
  --purple-8: 60, 16, 143;
  --purple-9: 39, 6, 110;
  --purple-10: 22, 0, 77;
  --pinkpurple-1: 255, 232, 251;
  --pinkpurple-2: 247, 186, 239;
  --pinkpurple-3: 240, 142, 230;
  --pinkpurple-4: 232, 101, 223;
  --pinkpurple-5: 225, 62, 219;
  --pinkpurple-6: 217, 26, 217;
  --pinkpurple-7: 176, 16, 182;
  --pinkpurple-8: 138, 9, 147;
  --pinkpurple-9: 101, 3, 112;
  --pinkpurple-10: 66, 0, 77;
  --magenta-1: 255, 232, 241;
  --magenta-2: 253, 194, 219;
  --magenta-3: 251, 157, 199;
  --magenta-4: 249, 121, 183;
  --magenta-5: 247, 84, 168;
  --magenta-6: 245, 49, 157;
  --magenta-7: 203, 30, 131;
  --magenta-8: 161, 16, 105;
  --magenta-9: 119, 6, 79;
  --magenta-10: 77, 0, 52;
  --gray-1: 247, 248, 250;
  --gray-2: 242, 243, 245;
  --gray-3: 229, 230, 235;
  --gray-4: 201, 205, 212;
  --gray-5: 169, 174, 184;
  --gray-6: 134, 144, 156;
  --gray-7: 107, 119, 133;
  --gray-8: 78, 89, 105;
  --gray-9: 39, 46, 59;
  --gray-10: 29, 33, 41;
  --success-1: var(--green-1);
  --success-2: var(--green-2);
  --success-3: var(--green-3);
  --success-4: var(--green-4);
  --success-5: var(--green-5);
  --success-6: var(--green-6);
  --success-7: var(--green-7);
  --success-8: var(--green-8);
  --success-9: var(--green-9);
  --success-10: var(--green-10);
  --primary-1: var(--arcoblue-1);
  --primary-2: var(--arcoblue-2);
  --primary-3: var(--arcoblue-3);
  --primary-4: var(--arcoblue-4);
  --primary-5: var(--arcoblue-5);
  --primary-6: var(--arcoblue-6);
  --primary-7: var(--arcoblue-7);
  --primary-8: var(--arcoblue-8);
  --primary-9: var(--arcoblue-9);
  --primary-10: var(--arcoblue-10);
  --danger-1: var(--red-1);
  --danger-2: var(--red-2);
  --danger-3: var(--red-3);
  --danger-4: var(--red-4);
  --danger-5: var(--red-5);
  --danger-6: var(--red-6);
  --danger-7: var(--red-7);
  --danger-8: var(--red-8);
  --danger-9: var(--red-9);
  --danger-10: var(--red-10);
  --warning-1: var(--orange-1);
  --warning-2: var(--orange-2);
  --warning-3: var(--orange-3);
  --warning-4: var(--orange-4);
  --warning-5: var(--orange-5);
  --warning-6: var(--orange-6);
  --warning-7: var(--orange-7);
  --warning-8: var(--orange-8);
  --warning-9: var(--orange-9);
  --warning-10: var(--orange-10);
  --link-1: var(--arcoblue-1);
  --link-2: var(--arcoblue-2);
  --link-3: var(--arcoblue-3);
  --link-4: var(--arcoblue-4);
  --link-5: var(--arcoblue-5);
  --link-6: var(--arcoblue-6);
  --link-7: var(--arcoblue-7);
  --link-8: var(--arcoblue-8);
  --link-9: var(--arcoblue-9);
  --link-10: var(--arcoblue-10);
  --data-1: var(--arcoblue-5);
  --data-2: var(--arcoblue-2);
  --data-3: 85, 197, 253;
  --data-4: 156, 220, 252;
  --data-5: var(--orange-6);
  --data-6: var(--orange-3);
  --data-7: var(--green-4);
  --data-8: var(--green-2);
  --data-9: var(--purple-4);
  --data-10: var(--purple-2);
  --data-11: var(--gold-6);
  --data-12: var(--gold-4);
  --data-13: var(--lime-6);
  --data-14: var(--lime-4);
  --data-15: var(--magenta-4);
  --data-16: var(--magenta-3);
  --data-17: var(--cyan-6);
  --data-18: var(--cyan-3);
  --data-19: var(--pinkpurple-4);
  --data-20: var(--pinkpurple-2);
}
body[arco-theme='dark'] {
  --red-1: 77, 0, 10;
  --red-2: 119, 6, 17;
  --red-3: 161, 22, 31;
  --red-4: 203, 46, 52;
  --red-5: 245, 78, 78;
  --red-6: 247, 105, 101;
  --red-7: 249, 141, 134;
  --red-8: 251, 176, 167;
  --red-9: 253, 209, 202;
  --red-10: 255, 240, 236;
  --orangered-1: 77, 14, 0;
  --orangered-2: 119, 30, 5;
  --orangered-3: 162, 55, 20;
  --orangered-4: 204, 87, 41;
  --orangered-5: 247, 126, 69;
  --orangered-6: 249, 146, 90;
  --orangered-7: 250, 173, 125;
  --orangered-8: 252, 198, 161;
  --orangered-9: 253, 222, 197;
  --orangered-10: 255, 244, 235;
  --orange-1: 77, 27, 0;
  --orange-2: 121, 48, 4;
  --orange-3: 166, 75, 10;
  --orange-4: 210, 105, 19;
  --orange-5: 255, 141, 31;
  --orange-6: 255, 150, 38;
  --orange-7: 255, 179, 87;
  --orange-8: 255, 205, 135;
  --orange-9: 255, 227, 184;
  --orange-10: 255, 247, 232;
  --gold-1: 77, 45, 0;
  --gold-2: 119, 75, 4;
  --gold-3: 162, 111, 15;
  --gold-4: 204, 150, 31;
  --gold-5: 247, 192, 52;
  --gold-6: 249, 204, 68;
  --gold-7: 250, 220, 108;
  --gold-8: 252, 233, 149;
  --gold-9: 253, 244, 190;
  --gold-10: 255, 252, 232;
  --yellow-1: 77, 56, 0;
  --yellow-2: 120, 94, 7;
  --yellow-3: 163, 134, 20;
  --yellow-4: 207, 179, 37;
  --yellow-5: 250, 225, 60;
  --yellow-6: 251, 233, 75;
  --yellow-7: 252, 243, 116;
  --yellow-8: 253, 250, 157;
  --yellow-9: 254, 254, 198;
  --yellow-10: 254, 255, 240;
  --lime-1: 42, 77, 0;
  --lime-2: 68, 112, 6;
  --lime-3: 98, 148, 18;
  --lime-4: 132, 183, 35;
  --lime-5: 168, 219, 57;
  --lime-6: 184, 226, 75;
  --lime-7: 203, 233, 112;
  --lime-8: 222, 241, 152;
  --lime-9: 238, 248, 194;
  --lime-10: 253, 255, 238;
  --green-1: 0, 77, 28;
  --green-2: 4, 102, 37;
  --green-3: 10, 128, 45;
  --green-4: 18, 154, 55;
  --green-5: 29, 180, 64;
  --green-6: 39, 195, 70;
  --green-7: 80, 210, 102;
  --green-8: 126, 225, 139;
  --green-9: 178, 240, 183;
  --green-10: 235, 255, 236;
  --cyan-1: 0, 66, 77;
  --cyan-2: 6, 97, 108;
  --cyan-3: 17, 131, 139;
  --cyan-4: 31, 166, 170;
  --cyan-5: 48, 201, 201;
  --cyan-6: 63, 212, 207;
  --cyan-7: 102, 223, 215;
  --cyan-8: 144, 233, 225;
  --cyan-9: 190, 244, 237;
  --cyan-10: 240, 255, 252;
  --blue-1: 0, 26, 77;
  --blue-2: 5, 47, 120;
  --blue-3: 19, 76, 163;
  --blue-4: 41, 113, 207;
  --blue-5: 70, 154, 250;
  --blue-6: 90, 170, 251;
  --blue-7: 125, 193, 252;
  --blue-8: 161, 213, 253;
  --blue-9: 198, 232, 254;
  --blue-10: 234, 248, 255;
  --arcoblue-1: 0, 13, 77;
  --arcoblue-2: 4, 27, 121;
  --arcoblue-3: 14, 50, 166;
  --arcoblue-4: 29, 77, 210;
  --arcoblue-5: 48, 111, 255;
  --arcoblue-6: 60, 126, 255;
  --arcoblue-7: 104, 159, 255;
  --arcoblue-8: 147, 190, 255;
  --arcoblue-9: 190, 218, 255;
  --arcoblue-10: 234, 244, 255;
  --purple-1: 22, 0, 77;
  --purple-2: 39, 6, 110;
  --purple-3: 62, 19, 143;
  --purple-4: 90, 37, 176;
  --purple-5: 123, 61, 209;
  --purple-6: 142, 81, 218;
  --purple-7: 169, 116, 227;
  --purple-8: 197, 154, 237;
  --purple-9: 223, 194, 246;
  --purple-10: 247, 237, 255;
  --pinkpurple-1: 66, 0, 77;
  --pinkpurple-2: 101, 3, 112;
  --pinkpurple-3: 138, 13, 147;
  --pinkpurple-4: 176, 27, 182;
  --pinkpurple-5: 217, 46, 217;
  --pinkpurple-6: 225, 61, 219;
  --pinkpurple-7: 232, 102, 223;
  --pinkpurple-8: 240, 146, 230;
  --pinkpurple-9: 247, 193, 240;
  --pinkpurple-10: 255, 242, 253;
  --magenta-1: 77, 0, 52;
  --magenta-2: 119, 8, 80;
  --magenta-3: 161, 23, 108;
  --magenta-4: 203, 43, 136;
  --magenta-5: 245, 69, 166;
  --magenta-6: 247, 86, 169;
  --magenta-7: 249, 122, 184;
  --magenta-8: 251, 158, 200;
  --magenta-9: 253, 195, 219;
  --magenta-10: 255, 232, 241;
  --gray-1: 23, 23, 26;
  --gray-2: 46, 46, 48;
  --gray-3: 72, 72, 73;
  --gray-4: 95, 95, 96;
  --gray-5: 120, 120, 122;
  --gray-6: 146, 146, 147;
  --gray-7: 171, 171, 172;
  --gray-8: 197, 197, 197;
  --gray-9: 223, 223, 223;
  --gray-10: 246, 246, 246;
  --primary-1: var(--arcoblue-1);
  --primary-2: var(--arcoblue-2);
  --primary-3: var(--arcoblue-3);
  --primary-4: var(--arcoblue-4);
  --primary-5: var(--arcoblue-5);
  --primary-6: var(--arcoblue-6);
  --primary-7: var(--arcoblue-7);
  --primary-8: var(--arcoblue-8);
  --primary-9: var(--arcoblue-9);
  --primary-10: var(--arcoblue-10);
  --success-1: var(--green-1);
  --success-2: var(--green-2);
  --success-3: var(--green-3);
  --success-4: var(--green-4);
  --success-5: var(--green-5);
  --success-6: var(--green-6);
  --success-7: var(--green-7);
  --success-8: var(--green-8);
  --success-9: var(--green-9);
  --success-10: var(--green-10);
  --danger-1: var(--red-1);
  --danger-2: var(--red-2);
  --danger-3: var(--red-3);
  --danger-4: var(--red-4);
  --danger-5: var(--red-5);
  --danger-6: var(--red-6);
  --danger-7: var(--red-7);
  --danger-8: var(--red-8);
  --danger-9: var(--red-9);
  --danger-10: var(--red-10);
  --warning-1: var(--orange-1);
  --warning-2: var(--orange-2);
  --warning-3: var(--orange-3);
  --warning-4: var(--orange-4);
  --warning-5: var(--orange-5);
  --warning-6: var(--orange-6);
  --warning-7: var(--orange-7);
  --warning-8: var(--orange-8);
  --warning-9: var(--orange-9);
  --warning-10: var(--orange-10);
  --link-1: var(--arcoblue-1);
  --link-2: var(--arcoblue-2);
  --link-3: var(--arcoblue-3);
  --link-4: var(--arcoblue-4);
  --link-5: var(--arcoblue-5);
  --link-6: var(--arcoblue-6);
  --link-7: var(--arcoblue-7);
  --link-8: var(--arcoblue-8);
  --link-9: var(--arcoblue-9);
  --link-10: var(--arcoblue-10);
  --data-1: var(--arcoblue-5);
  --data-2: var(--arcoblue-3);
  --data-3: var(--blue-5);
  --data-4: var(--blue-3);
  --data-5: var(--orange-6);
  --data-6: var(--orange-3);
  --data-7: var(--green-4);
  --data-8: var(--green-3);
  --data-9: var(--purple-4);
  --data-10: var(--purple-3);
  --data-11: var(--gold-6);
  --data-12: var(--gold-4);
  --data-13: var(--lime-6);
  --data-14: var(--lime-4);
  --data-15: var(--magenta-4);
  --data-16: var(--magenta-3);
  --data-17: var(--cyan-6);
  --data-18: var(--cyan-3);
  --data-19: var(--pinkpurple-4);
  --data-20: var(--pinkpurple-2);
}
body {
  --color-white: #ffffff;
  --color-black: #000000;
  --color-border: rgb(var(--gray-3));
  --color-bg-popup: var(--color-bg-5);
  --color-bg-1: #fff;
  --color-bg-2: #fff;
  --color-bg-3: #fff;
  --color-bg-4: #fff;
  --color-bg-5: #fff;
  --color-bg-white: #fff;
  --color-neutral-1: rgb(var(--gray-1));
  --color-neutral-2: rgb(var(--gray-2));
  --color-neutral-3: rgb(var(--gray-3));
  --color-neutral-4: rgb(var(--gray-4));
  --color-neutral-5: rgb(var(--gray-5));
  --color-neutral-6: rgb(var(--gray-6));
  --color-neutral-7: rgb(var(--gray-7));
  --color-neutral-8: rgb(var(--gray-8));
  --color-neutral-9: rgb(var(--gray-9));
  --color-neutral-10: rgb(var(--gray-10));
  --color-text-1: var(--color-neutral-10);
  --color-text-2: var(--color-neutral-8);
  --color-text-3: var(--color-neutral-6);
  --color-text-4: var(--color-neutral-4);
  --color-border-1: var(--color-neutral-2);
  --color-border-2: var(--color-neutral-3);
  --color-border-3: var(--color-neutral-4);
  --color-border-4: var(--color-neutral-6);
  --color-fill-1: var(--color-neutral-1);
  --color-fill-2: var(--color-neutral-2);
  --color-fill-3: var(--color-neutral-3);
  --color-fill-4: var(--color-neutral-4);
  --color-primary-light-1: rgb(var(--primary-1));
  --color-primary-light-2: rgb(var(--primary-2));
  --color-primary-light-3: rgb(var(--primary-3));
  --color-primary-light-4: rgb(var(--primary-4));
  --color-secondary: var(--color-neutral-2);
  --color-secondary-hover: var(--color-neutral-3);
  --color-secondary-active: var(--color-neutral-4);
  --color-secondary-disabled: var(--color-neutral-1);
  --color-danger-light-1: rgb(var(--danger-1));
  --color-danger-light-2: rgb(var(--danger-2));
  --color-danger-light-3: rgb(var(--danger-3));
  --color-danger-light-4: rgb(var(--danger-4));
  --color-success-light-1: rgb(var(--success-1));
  --color-success-light-2: rgb(var(--success-2));
  --color-success-light-3: rgb(var(--success-3));
  --color-success-light-4: rgb(var(--success-4));
  --color-warning-light-1: rgb(var(--warning-1));
  --color-warning-light-2: rgb(var(--warning-2));
  --color-warning-light-3: rgb(var(--warning-3));
  --color-warning-light-4: rgb(var(--warning-4));
  --color-link-light-1: rgb(var(--link-1));
  --color-link-light-2: rgb(var(--link-2));
  --color-link-light-3: rgb(var(--link-3));
  --color-link-light-4: rgb(var(--link-4));
  --color-data-1: rgb(var(--arcoblue-5));
  --color-data-2: rgb(var(--arcoblue-3));
  --color-data-3: rgb(var(--blue-5));
  --color-data-4: rgb(var(--blue-3));
  --color-data-5: rgb(var(--orange-6));
  --color-data-6: rgb(var(--orange-3));
  --color-data-7: rgb(var(--green-4));
  --color-data-8: rgb(var(--green-3));
  --color-data-9: rgb(var(--purple-4));
  --color-data-10: rgb(var(--purple-3));
  --color-data-11: rgb(var(--gold-6));
  --color-data-12: rgb(var(--gold-4));
  --color-data-13: rgb(var(--lime-6));
  --color-data-14: rgb(var(--lime-4));
  --color-data-15: rgb(var(--magenta-4));
  --color-data-16: rgb(var(--magenta-3));
  --color-data-17: rgb(var(--cyan-6));
  --color-data-18: rgb(var(--cyan-3));
  --color-data-19: rgb(var(--pinkpurple-4));
  --color-data-20: rgb(var(--pinkpurple-2));
  --border-radius-none: 0;
  --border-radius-small: 2px;
  --border-radius-medium: 4px;
  --border-radius-large: 8px;
  --border-radius-circle: 50%;
  --color-tooltip-bg: rgb(var(--gray-10));
  --color-spin-layer-bg: rgba(255, 255, 255, 0.6);
  --color-menu-dark-bg: #232324;
  --color-menu-light-bg: #ffffff;
  --color-menu-dark-hover: rgba(255, 255, 255, 0.04);
  --color-mask-bg: rgba(29, 33, 41, 0.6);
  --font-weight-100: 100;
  --font-weight-200: 200;
  --font-weight-300: 300;
  --font-weight-400: 400;
  --font-weight-500: 500;
  --font-weight-600: 600;
  --font-weight-700: 700;
  --font-weight-800: 800;
  --font-weight-900: 900;
}
body[arco-theme='dark'] {
  --color-white: rgba(255, 255, 255, 0.9);
  --color-black: #000000;
  --color-border: #333335;
  --color-bg-1: #17171a;
  --color-bg-2: #232324;
  --color-bg-3: #2a2a2b;
  --color-bg-4: #313132;
  --color-bg-5: #373739;
  --color-bg-white: #f6f6f6;
  --color-text-1: rgba(255, 255, 255, 0.9);
  --color-text-2: rgba(255, 255, 255, 0.7);
  --color-text-3: rgba(255, 255, 255, 0.5);
  --color-text-4: rgba(255, 255, 255, 0.3);
  --color-fill-1: rgba(255, 255, 255, 0.04);
  --color-fill-2: rgba(255, 255, 255, 0.08);
  --color-fill-3: rgba(255, 255, 255, 0.12);
  --color-fill-4: rgba(255, 255, 255, 0.16);
  --color-primary-light-1: rgba(var(--primary-6), 0.2);
  --color-primary-light-2: rgba(var(--primary-6), 0.35);
  --color-primary-light-3: rgba(var(--primary-6), 0.5);
  --color-primary-light-4: rgba(var(--primary-6), 0.65);
  --color-secondary: rgba(var(--gray-9), 0.08);
  --color-secondary-hover: rgba(var(--gray-8), 0.16);
  --color-secondary-active: rgba(var(--gray-7), 0.24);
  --color-secondary-disabled: rgba(var(--gray-9), 0.08);
  --color-danger-light-1: rgba(var(--danger-6), 0.2);
  --color-danger-light-2: rgba(var(--danger-6), 0.35);
  --color-danger-light-3: rgba(var(--danger-6), 0.5);
  --color-danger-light-4: rgba(var(--danger-6), 0.65);
  --color-success-light-1: rgb(var(--success-6), 0.2);
  --color-success-light-2: rgb(var(--success-6), 0.35);
  --color-success-light-3: rgb(var(--success-6), 0.5);
  --color-success-light-4: rgb(var(--success-6), 0.65);
  --color-warning-light-1: rgb(var(--warning-6), 0.2);
  --color-warning-light-2: rgb(var(--warning-6), 0.35);
  --color-warning-light-3: rgb(var(--warning-6), 0.5);
  --color-warning-light-4: rgb(var(--warning-6), 0.65);
  --color-link-light-1: rgba(var(--link-6), 0.2);
  --color-link-light-2: rgba(var(--link-6), 0.35);
  --color-link-light-3: rgba(var(--link-6), 0.5);
  --color-link-light-4: rgba(var(--link-6), 0.65);
  --color-tooltip-bg: #373739;
  --color-spin-layer-bg: rgba(51, 51, 51, 0.6);
  --color-menu-dark-bg: #232324;
  --color-menu-light-bg: #232324;
  --color-menu-dark-hover: var(--color-fill-2);
  --color-mask-bg: rgba(23, 23, 26, 0.6);
} /*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
html,
body {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: Inter, -apple-system, BlinkMacSystemFont, PingFang SC,
    Hiragino Sans GB, noto sans, Microsoft YaHei, Helvetica Neue, Helvetica,
    Arial, sans-serif;
}
body {
  margin: 0;
  padding: 0;
}
main {
  display: block;
}
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}
tr,
th {
  margin: 0;
  padding: 0;
}
pre {
  font-family: monospace, monospace;
  font-size: 1em;
}
a {
  background-color: transparent;
}
abbr[title] {
  border-bottom: none;
  text-decoration: underline;
  text-decoration: underline dotted;
}
b,
strong {
  font-weight: bolder;
}
code,
kbd,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}
small {
  font-size: 80%;
}
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
img {
  border-style: none;
}
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  line-height: 1.15;
  margin: 0;
}
button,
input {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button;
}
button::-moz-focus-inner,
[type='button']::-moz-focus-inner,
[type='reset']::-moz-focus-inner,
[type='submit']::-moz-focus-inner {
  border-style: none;
  padding: 0;
}
button:-moz-focusring,
[type='button']:-moz-focusring,
[type='reset']:-moz-focusring,
[type='submit']:-moz-focusring {
  outline: 1px dotted ButtonText;
}
fieldset {
  padding: 0.35em 0.75em 0.625em;
}
legend {
  box-sizing: border-box;
  color: inherit;
  display: table;
  max-width: 100%;
  padding: 0;
  white-space: normal;
}
progress {
  vertical-align: baseline;
}
textarea {
  overflow: auto;
}
[type='checkbox'],
[type='radio'] {
  box-sizing: border-box;
  padding: 0;
}
[type='number']::-webkit-inner-spin-button,
[type='number']::-webkit-outer-spin-button {
  height: auto;
}
[type='search'] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}
[type='search']::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}
details {
  display: block;
}
summary {
  display: list-item;
}
template {
  display: none;
}
[hidden] {
  display: none;
}
* {
  outline: none;
}
input::-ms-clear,
input::-ms-reveal {
  display: none;
}
.arco-icon {
  display: inline-block;
  color: inherit;
  font-style: normal;
  width: 1em;
  height: 1em;
  vertical-align: -2px;
  stroke: currentColor;
}
.arco-icon[fill='currentColor'] {
  fill: currentColor;
  stroke: none;
}
.arco-icon[stroke='currentColor'] {
  stroke: currentColor;
  fill: none;
}
.arco-icon[fill='currentColor'][stroke='currentColor'] {
  fill: currentColor;
  stroke: currentColor;
}
.arco-icon-loading {
  animation: arco-loading-circle 1s infinite cubic-bezier(0, 0, 1, 1);
}
@keyframes arco-loading-circle {
  to {
    transform: rotate(360deg);
  }
}
.arco-icon-hover {
  position: relative;
  display: inline-block;
  cursor: pointer;
  line-height: 0;
}
.arco-icon-hover .arco-icon {
  position: relative;
  vertical-align: -0.09em;
}
.arco-icon-hover:before {
  content: '';
  position: absolute;
  display: block;
  border-radius: var(--border-radius-circle);
  background-color: transparent;
  box-sizing: border-box;
  transition: background-color 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-icon-hover:hover:before {
  background-color: var(--color-fill-2);
}
.arco-icon-hover.arco-icon-hover-disabled:before {
  opacity: 0;
}
.arco-icon-hover:before {
  top: 50%;
  left: 50%;
  height: 20px;
  width: 20px;
  transform: translate(-50%, -50%);
}
.arco-icon-hover-size-mini:before {
  top: 50%;
  left: 50%;
  height: 20px;
  width: 20px;
  transform: translate(-50%, -50%);
}
.arco-icon-hover-size-small:before {
  top: 50%;
  left: 50%;
  height: 20px;
  width: 20px;
  transform: translate(-50%, -50%);
}
.arco-icon-hover-size-large:before {
  top: 50%;
  left: 50%;
  height: 24px;
  width: 24px;
  transform: translate(-50%, -50%);
}
.arco-icon-hover-size-huge:before {
  top: 50%;
  left: 50%;
  height: 24px;
  width: 24px;
  transform: translate(-50%, -50%);
}
.fadeInStandard-enter,
.fadeInStandard-appear {
  opacity: 0;
}
.fadeInStandard-enter-active,
.fadeInStandard-appear-active {
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.fadeInStandard-exit {
  opacity: 1;
}
.fadeInStandard-exit-active {
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.fadeIn-enter,
.fadeIn-appear {
  opacity: 0;
}
.fadeIn-enter-active,
.fadeIn-appear-active {
  opacity: 1;
  transition: opacity 0.1s cubic-bezier(0, 0, 1, 1);
}
.fadeIn-exit {
  opacity: 1;
}
.fadeIn-exit-active {
  opacity: 0;
  transition: opacity 0.1s cubic-bezier(0, 0, 1, 1);
}
.slideDynamicOrigin-enter,
.slideDynamicOrigin-appear {
  opacity: 0;
  transform-origin: 0 0;
  transform: scaleY(0.9) translateZ(0);
}
.slideDynamicOrigin-enter-active,
.slideDynamicOrigin-appear-active,
.slideDynamicOrigin-exit {
  opacity: 1;
  transform-origin: 0 0;
  transform: scaleY(1) translateZ(0);
  transition: transform 0.2s cubic-bezier(0.34, 0.69, 0.1, 1),
    opacity 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.slideDynamicOrigin-exit-active {
  opacity: 0;
  transform-origin: 0 0;
  transform: scaleY(0.9) translateZ(0);
  transition: transform 0.2s cubic-bezier(0.34, 0.69, 0.1, 1),
    opacity 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.slideLeft-enter,
.slideLeft-appear {
  transform: translate(-100%);
}
.slideLeft-enter-active,
.slideLeft-appear-active {
  transform: translate(0);
  transition: transform 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.slideLeft-exit {
  transform: translate(0);
}
.slideLeft-exit-active {
  transform: translate(-100%);
  transition: transform 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.slideRight-enter,
.slideRight-appear {
  transform: translate(100%);
}
.slideRight-enter-active,
.slideRight-appear-active {
  transform: translate(0);
  transition: transform 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.slideRight-exit {
  transform: translate(0);
}
.slideRight-exit-active {
  transform: translate(100%);
  transition: transform 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.slideTop-enter,
.slideTop-appear {
  transform: translateY(-100%);
}
.slideTop-enter-active,
.slideTop-appear-active {
  transform: translateY(0);
  transition: transform 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.slideTop-exit {
  transform: translateY(0);
}
.slideTop-exit-active {
  transform: translateY(-100%);
  transition: transform 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.slideBottom-enter,
.slideBottom-appear {
  transform: translateY(100%);
}
.slideBottom-enter-active,
.slideBottom-appear-active {
  transform: translateY(0);
  transition: transform 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.slideBottom-exit {
  transform: translateY(0);
}
.slideBottom-exit-active {
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.zoomIn-enter,
.zoomIn-appear {
  opacity: 0;
  transform: scale(0.5);
}
.zoomIn-enter-active,
.zoomIn-appear-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.3s cubic-bezier(0.34, 0.69, 0.1, 1),
    transform 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.zoomIn-exit {
  opacity: 1;
  transform: scale(1);
}
.zoomIn-exit-active {
  opacity: 0;
  transform: scale(0.5);
  transition: opacity 0.3s cubic-bezier(0.3, 1.3, 0.3, 1),
    transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.zoomInFadeOut-enter,
.zoomInFadeOut-appear {
  opacity: 0;
  transform: scale(0.5);
}
.zoomInFadeOut-enter-active,
.zoomInFadeOut-appear-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.2s cubic-bezier(0.34, 0.69, 0.1, 1),
    transform 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.zoomInFadeOut-exit {
  opacity: 1;
  transform: scale(1);
}
.zoomInFadeOut-exit-active {
  opacity: 0;
  transform: scale(0.5);
  transition: opacity 0.2s cubic-bezier(0.3, 1.3, 0.3, 1),
    transform 0.2s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.zoomInBig-enter,
.zoomInBig-appear {
  opacity: 0;
  transform: scale(0.5);
}
.zoomInBig-enter-active,
.zoomInBig-appear-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.2s cubic-bezier(0, 0, 1, 1),
    transform 0.2s cubic-bezier(0, 0, 1, 1);
}
.zoomInBig-exit {
  opacity: 1;
  transform: scale(1);
}
.zoomInBig-exit-active {
  opacity: 0;
  transform: scale(0.2);
  transition: opacity 0.2s cubic-bezier(0, 0, 1, 1),
    transform 0.2s cubic-bezier(0, 0, 1, 1);
}
.zoomInLeft-enter,
.zoomInLeft-appear {
  opacity: 0.1;
  transform-origin: 0 50%;
  transform: scale(0.1);
}
.zoomInLeft-enter-active,
.zoomInLeft-appear-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.3s cubic-bezier(0, 0, 1, 1),
    transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.zoomInLeft-exit {
  opacity: 1;
  transform-origin: 0 50%;
  transform: scale(1);
}
.zoomInLeft-exit-active {
  opacity: 0.1;
  transform: scale(0.1);
  transition: opacity 0.3s cubic-bezier(0, 0, 1, 1),
    transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.zoomInTop-enter,
.zoomInTop-appear {
  opacity: 0;
  transform-origin: 0% 0%;
  transform: scaleY(0.8) translateZ(0);
}
.zoomInTop-enter-active,
.zoomInTop-appear-active {
  opacity: 1;
  transform-origin: 0% 0%;
  transform: scaleY(1) translateZ(0);
  transition: transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1),
    opacity 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.zoomInTop-exit {
  opacity: 1;
  transform-origin: 0% 0%;
  transform: scaleY(1) translateZ(0);
}
.zoomInTop-exit-active {
  opacity: 0;
  transform-origin: 0% 0%;
  transform: scaleY(0.8) translateZ(0);
  transition: transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1),
    opacity 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.zoomInBottom-enter,
.zoomInBottom-appear {
  opacity: 0;
  transform-origin: 100% 100%;
  transform: scaleY(0.8) translateZ(0);
}
.zoomInBottom-enter-active,
.zoomInBottom-appear-active {
  opacity: 1;
  transform-origin: 100% 100%;
  transform: scaleY(1) translateZ(0);
  transition: transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1),
    opacity 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.zoomInBottom-exit {
  opacity: 1;
  transform-origin: 100% 100%;
  transform: scaleY(1) translateZ(0);
}
.zoomInBottom-exit-active {
  opacity: 0;
  transform-origin: 100% 100%;
  transform: scaleY(0.8) translateZ(0);
  transition: transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1),
    opacity 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
body {
  font-size: 14px;
}
body {
  font-family: Inter, -apple-system, BlinkMacSystemFont, PingFang SC,
    Hiragino Sans GB, noto sans, Microsoft YaHei, Helvetica Neue, Helvetica,
    Arial, sans-serif;
}
body {
  --color-white: #ffffff;
  --color-black: #000000;
  --color-border: rgb(var(--gray-3));
  --color-bg-popup: var(--color-bg-5);
  --color-bg-1: #fff;
  --color-bg-2: #fff;
  --color-bg-3: #fff;
  --color-bg-4: #fff;
  --color-bg-5: #fff;
  --color-bg-white: #fff;
  --color-neutral-1: rgb(var(--gray-1));
  --color-neutral-2: rgb(var(--gray-2));
  --color-neutral-3: rgb(var(--gray-3));
  --color-neutral-4: rgb(var(--gray-4));
  --color-neutral-5: rgb(var(--gray-5));
  --color-neutral-6: rgb(var(--gray-6));
  --color-neutral-7: rgb(var(--gray-7));
  --color-neutral-8: rgb(var(--gray-8));
  --color-neutral-9: rgb(var(--gray-9));
  --color-neutral-10: rgb(var(--gray-10));
  --color-text-1: var(--color-neutral-10);
  --color-text-2: var(--color-neutral-8);
  --color-text-3: var(--color-neutral-6);
  --color-text-4: var(--color-neutral-4);
  --color-fill-1: var(--color-neutral-1);
  --color-fill-2: var(--color-neutral-2);
  --color-fill-3: var(--color-neutral-3);
  --color-fill-4: var(--color-neutral-4);
  --color-border-1: var(--color-neutral-2);
  --color-border-2: var(--color-neutral-3);
  --color-border-3: var(--color-neutral-4);
  --color-border-4: var(--color-neutral-6);
  --color-primary-light-1: rgb(var(--primary-1));
  --color-primary-light-2: rgb(var(--primary-2));
  --color-primary-light-3: rgb(var(--primary-3));
  --color-primary-light-4: rgb(var(--primary-4));
  --color-secondary: var(--color-neutral-2);
  --color-secondary-hover: var(--color-neutral-3);
  --color-secondary-active: var(--color-neutral-4);
  --color-secondary-disabled: var(--color-neutral-1);
  --color-danger-light-1: rgb(var(--danger-1));
  --color-danger-light-2: rgb(var(--danger-2));
  --color-danger-light-3: rgb(var(--danger-3));
  --color-danger-light-4: rgb(var(--danger-4));
  --color-success-light-1: rgb(var(--success-1));
  --color-success-light-2: rgb(var(--success-2));
  --color-success-light-3: rgb(var(--success-3));
  --color-success-light-4: rgb(var(--success-4));
  --color-warning-light-1: rgb(var(--warning-1));
  --color-warning-light-2: rgb(var(--warning-2));
  --color-warning-light-3: rgb(var(--warning-3));
  --color-warning-light-4: rgb(var(--warning-4));
  --color-link-light-1: rgb(var(--link-1));
  --color-link-light-2: rgb(var(--link-2));
  --color-link-light-3: rgb(var(--link-3));
  --color-link-light-4: rgb(var(--link-4));
  --border-radius-none: 0;
  --border-radius-small: 2px;
  --border-radius-medium: 4px;
  --border-radius-large: 8px;
  --border-radius-circle: 50%;
  --color-tooltip-bg: rgb(var(--gray-10));
  --color-spin-layer-bg: rgba(255, 255, 255, 0.6);
  --color-menu-dark-bg: #232324;
  --color-menu-light-bg: #ffffff;
  --color-menu-dark-hover: rgba(255, 255, 255, 0.04);
  --color-mask-bg: rgba(29, 33, 41, 0.6);
}
body[arco-theme='dark'] {
  --color-black: #000000;
  --color-border: #333335;
  --color-bg-1: #17171a;
  --color-bg-2: #232324;
  --color-bg-3: #2a2a2b;
  --color-bg-4: #313132;
  --color-bg-5: #373739;
  --color-bg-white: #f6f6f6;
  --color-text-1: rgba(255, 255, 255, 0.9);
  --color-text-2: rgba(255, 255, 255, 0.7);
  --color-text-3: rgba(255, 255, 255, 0.5);
  --color-text-4: rgba(255, 255, 255, 0.3);
  --color-fill-1: rgba(255, 255, 255, 0.04);
  --color-fill-2: rgba(255, 255, 255, 0.08);
  --color-fill-3: rgba(255, 255, 255, 0.12);
  --color-fill-4: rgba(255, 255, 255, 0.16);
  --color-border-1: #2e2e30;
  --color-border-2: #484849;
  --color-border-3: #5f5f60;
  --color-border-4: #929293;
  --color-primary-light-1: rgba(var(--primary-6), 0.2);
  --color-primary-light-2: rgba(var(--primary-6), 0.35);
  --color-primary-light-3: rgba(var(--primary-6), 0.5);
  --color-primary-light-4: rgba(var(--primary-6), 0.65);
  --color-secondary: rgba(var(--gray-9), 0.08);
  --color-secondary-hover: rgba(var(--gray-8), 0.16);
  --color-secondary-active: rgba(var(--gray-7), 0.24);
  --color-secondary-disabled: rgba(var(--gray-9), 0.08);
  --color-danger-light-1: rgba(var(--danger-6), 0.2);
  --color-danger-light-2: rgba(var(--danger-6), 0.35);
  --color-danger-light-3: rgba(var(--danger-6), 0.5);
  --color-danger-light-4: rgba(var(--danger-6), 0.65);
  --color-success-light-1: rgb(var(--success-6), 0.2);
  --color-success-light-2: rgb(var(--success-6), 0.35);
  --color-success-light-3: rgb(var(--success-6), 0.5);
  --color-success-light-4: rgb(var(--success-6), 0.65);
  --color-warning-light-1: rgb(var(--warning-6), 0.2);
  --color-warning-light-2: rgb(var(--warning-6), 0.35);
  --color-warning-light-3: rgb(var(--warning-6), 0.5);
  --color-warning-light-4: rgb(var(--warning-6), 0.65);
  --color-link-light-1: rgba(var(--link-6), 0.2);
  --color-link-light-2: rgba(var(--link-6), 0.35);
  --color-link-light-3: rgba(var(--link-6), 0.5);
  --color-link-light-4: rgba(var(--link-6), 0.65);
  --color-tooltip-bg: #373739;
  --color-spin-layer-bg: rgba(51, 51, 51, 0.6);
  --color-menu-dark-bg: #232324;
  --color-menu-light-bg: #232324;
  --color-menu-dark-hover: var(--color-fill-2);
  --color-mask-bg: rgba(23, 23, 26, 0.6);
}
body {
  --red-1: 255, 236, 232;
  --red-2: 253, 205, 197;
  --red-3: 251, 172, 163;
  --red-4: 249, 137, 129;
  --red-5: 247, 101, 96;
  --red-6: 245, 63, 63;
  --red-7: 203, 39, 45;
  --red-8: 161, 21, 30;
  --red-9: 119, 8, 19;
  --red-10: 77, 0, 10;
  --orangered-1: 255, 243, 232;
  --orangered-2: 253, 221, 195;
  --orangered-3: 252, 197, 159;
  --orangered-4: 250, 172, 123;
  --orangered-5: 249, 144, 87;
  --orangered-6: 247, 114, 52;
  --orangered-7: 204, 81, 32;
  --orangered-8: 162, 53, 17;
  --orangered-9: 119, 31, 6;
  --orangered-10: 77, 14, 0;
  --orange-1: 255, 247, 232;
  --orange-2: 255, 228, 186;
  --orange-3: 255, 207, 139;
  --orange-4: 255, 182, 93;
  --orange-5: 255, 154, 46;
  --orange-6: 255, 125, 0;
  --orange-7: 210, 95, 0;
  --orange-8: 166, 69, 0;
  --orange-9: 121, 46, 0;
  --orange-10: 77, 27, 0;
  --gold-1: 255, 252, 232;
  --gold-2: 253, 244, 191;
  --gold-3: 252, 233, 150;
  --gold-4: 250, 220, 109;
  --gold-5: 249, 204, 69;
  --gold-6: 247, 186, 30;
  --gold-7: 204, 146, 19;
  --gold-8: 162, 109, 10;
  --gold-9: 119, 75, 4;
  --gold-10: 77, 45, 0;
  --yellow-1: 254, 255, 232;
  --yellow-2: 254, 254, 190;
  --yellow-3: 253, 250, 148;
  --yellow-4: 252, 242, 107;
  --yellow-5: 251, 232, 66;
  --yellow-6: 250, 220, 25;
  --yellow-7: 207, 175, 15;
  --yellow-8: 163, 132, 8;
  --yellow-9: 120, 93, 3;
  --yellow-10: 77, 56, 0;
  --lime-1: 252, 255, 232;
  --lime-2: 237, 248, 187;
  --lime-3: 220, 241, 144;
  --lime-4: 201, 233, 104;
  --lime-5: 181, 226, 65;
  --lime-6: 159, 219, 29;
  --lime-7: 126, 183, 18;
  --lime-8: 95, 148, 10;
  --lime-9: 67, 112, 4;
  --lime-10: 42, 77, 0;
  --green-1: 232, 255, 234;
  --green-2: 175, 240, 181;
  --green-3: 123, 225, 136;
  --green-4: 76, 210, 99;
  --green-5: 35, 195, 67;
  --green-6: 0, 180, 42;
  --green-7: 0, 154, 41;
  --green-8: 0, 128, 38;
  --green-9: 0, 102, 34;
  --green-10: 0, 77, 28;
  --cyan-1: 232, 255, 251;
  --cyan-2: 183, 244, 236;
  --cyan-3: 137, 233, 224;
  --cyan-4: 94, 223, 214;
  --cyan-5: 55, 212, 207;
  --cyan-6: 20, 201, 201;
  --cyan-7: 13, 165, 170;
  --cyan-8: 7, 130, 139;
  --cyan-9: 3, 97, 108;
  --cyan-10: 0, 66, 77;
  --blue-1: 232, 247, 255;
  --blue-2: 195, 231, 254;
  --blue-3: 159, 212, 253;
  --blue-4: 123, 192, 252;
  --blue-5: 87, 169, 251;
  --blue-6: 52, 145, 250;
  --blue-7: 32, 108, 207;
  --blue-8: 17, 75, 163;
  --blue-9: 6, 48, 120;
  --blue-10: 0, 26, 77;
  --arcoblue-1: 232, 243, 255;
  --arcoblue-2: 190, 218, 255;
  --arcoblue-3: 148, 191, 255;
  --arcoblue-4: 106, 161, 255;
  --arcoblue-5: 64, 128, 255;
  --arcoblue-6: 22, 93, 255;
  --arcoblue-7: 14, 66, 210;
  --arcoblue-8: 7, 44, 166;
  --arcoblue-9: 3, 26, 121;
  --arcoblue-10: 0, 13, 77;
  --purple-1: 245, 232, 255;
  --purple-2: 221, 190, 246;
  --purple-3: 195, 150, 237;
  --purple-4: 168, 113, 227;
  --purple-5: 141, 78, 218;
  --purple-6: 114, 46, 209;
  --purple-7: 85, 29, 176;
  --purple-8: 60, 16, 143;
  --purple-9: 39, 6, 110;
  --purple-10: 22, 0, 77;
  --pinkpurple-1: 255, 232, 251;
  --pinkpurple-2: 247, 186, 239;
  --pinkpurple-3: 240, 142, 230;
  --pinkpurple-4: 232, 101, 223;
  --pinkpurple-5: 225, 62, 219;
  --pinkpurple-6: 217, 26, 217;
  --pinkpurple-7: 176, 16, 182;
  --pinkpurple-8: 138, 9, 147;
  --pinkpurple-9: 101, 3, 112;
  --pinkpurple-10: 66, 0, 77;
  --magenta-1: 255, 232, 241;
  --magenta-2: 253, 194, 219;
  --magenta-3: 251, 157, 199;
  --magenta-4: 249, 121, 183;
  --magenta-5: 247, 84, 168;
  --magenta-6: 245, 49, 157;
  --magenta-7: 203, 30, 131;
  --magenta-8: 161, 16, 105;
  --magenta-9: 119, 6, 79;
  --magenta-10: 77, 0, 52;
  --gray-1: 247, 248, 250;
  --gray-2: 242, 243, 245;
  --gray-3: 229, 230, 235;
  --gray-4: 201, 205, 212;
  --gray-5: 169, 174, 184;
  --gray-6: 134, 144, 156;
  --gray-7: 107, 119, 133;
  --gray-8: 78, 89, 105;
  --gray-9: 39, 46, 59;
  --gray-10: 29, 33, 41;
  --primary-1: var(--arcoblue-1);
  --primary-2: var(--arcoblue-2);
  --primary-3: var(--arcoblue-3);
  --primary-4: var(--arcoblue-4);
  --primary-5: var(--arcoblue-5);
  --primary-6: var(--arcoblue-6);
  --primary-7: var(--arcoblue-7);
  --primary-8: var(--arcoblue-8);
  --primary-9: var(--arcoblue-9);
  --primary-10: var(--arcoblue-10);
  --link-1: var(--arcoblue-1);
  --link-2: var(--arcoblue-2);
  --link-3: var(--arcoblue-3);
  --link-4: var(--arcoblue-4);
  --link-5: var(--arcoblue-5);
  --link-6: var(--arcoblue-6);
  --link-7: var(--arcoblue-7);
  --link-8: var(--arcoblue-8);
  --link-9: var(--arcoblue-9);
  --link-10: var(--arcoblue-10);
  --success-1: var(--green-1);
  --success-2: var(--green-2);
  --success-3: var(--green-3);
  --success-4: var(--green-4);
  --success-5: var(--green-5);
  --success-6: var(--green-6);
  --success-7: var(--green-7);
  --success-8: var(--green-8);
  --success-9: var(--green-9);
  --success-10: var(--green-10);
  --danger-1: var(--red-1);
  --danger-2: var(--red-2);
  --danger-3: var(--red-3);
  --danger-4: var(--red-4);
  --danger-5: var(--red-5);
  --danger-6: var(--red-6);
  --danger-7: var(--red-7);
  --danger-8: var(--red-8);
  --danger-9: var(--red-9);
  --danger-10: var(--red-10);
  --warning-1: var(--orange-1);
  --warning-2: var(--orange-2);
  --warning-3: var(--orange-3);
  --warning-4: var(--orange-4);
  --warning-5: var(--orange-5);
  --warning-6: var(--orange-6);
  --warning-7: var(--orange-7);
  --warning-8: var(--orange-8);
  --warning-9: var(--orange-9);
  --warning-10: var(--orange-10);
}
body[arco-theme='dark'] {
  --red-1: 77, 0, 10;
  --red-2: 119, 6, 17;
  --red-3: 161, 22, 31;
  --red-4: 203, 46, 52;
  --red-5: 245, 78, 78;
  --red-6: 247, 105, 101;
  --red-7: 249, 141, 134;
  --red-8: 251, 176, 167;
  --red-9: 253, 209, 202;
  --red-10: 255, 240, 236;
  --orangered-1: 77, 14, 0;
  --orangered-2: 119, 30, 5;
  --orangered-3: 162, 55, 20;
  --orangered-4: 204, 87, 41;
  --orangered-5: 247, 126, 69;
  --orangered-6: 249, 146, 90;
  --orangered-7: 250, 173, 125;
  --orangered-8: 252, 198, 161;
  --orangered-9: 253, 222, 197;
  --orangered-10: 255, 244, 235;
  --orange-1: 77, 27, 0;
  --orange-2: 121, 48, 4;
  --orange-3: 166, 75, 10;
  --orange-4: 210, 105, 19;
  --orange-5: 255, 141, 31;
  --orange-6: 255, 150, 38;
  --orange-7: 255, 179, 87;
  --orange-8: 255, 205, 135;
  --orange-9: 255, 227, 184;
  --orange-10: 255, 247, 232;
  --gold-1: 77, 45, 0;
  --gold-2: 119, 75, 4;
  --gold-3: 162, 111, 15;
  --gold-4: 204, 150, 31;
  --gold-5: 247, 192, 52;
  --gold-6: 249, 204, 68;
  --gold-7: 250, 220, 108;
  --gold-8: 252, 233, 149;
  --gold-9: 253, 244, 190;
  --gold-10: 255, 252, 232;
  --yellow-1: 77, 56, 0;
  --yellow-2: 120, 94, 7;
  --yellow-3: 163, 134, 20;
  --yellow-4: 207, 179, 37;
  --yellow-5: 250, 225, 60;
  --yellow-6: 251, 233, 75;
  --yellow-7: 252, 243, 116;
  --yellow-8: 253, 250, 157;
  --yellow-9: 254, 254, 198;
  --yellow-10: 254, 255, 240;
  --lime-1: 42, 77, 0;
  --lime-2: 68, 112, 6;
  --lime-3: 98, 148, 18;
  --lime-4: 132, 183, 35;
  --lime-5: 168, 219, 57;
  --lime-6: 184, 226, 75;
  --lime-7: 203, 233, 112;
  --lime-8: 222, 241, 152;
  --lime-9: 238, 248, 194;
  --lime-10: 253, 255, 238;
  --green-1: 0, 77, 28;
  --green-2: 4, 102, 37;
  --green-3: 10, 128, 45;
  --green-4: 18, 154, 55;
  --green-5: 29, 180, 64;
  --green-6: 39, 195, 70;
  --green-7: 80, 210, 102;
  --green-8: 126, 225, 139;
  --green-9: 178, 240, 183;
  --green-10: 235, 255, 236;
  --cyan-1: 0, 66, 77;
  --cyan-2: 6, 97, 108;
  --cyan-3: 17, 131, 139;
  --cyan-4: 31, 166, 170;
  --cyan-5: 48, 201, 201;
  --cyan-6: 63, 212, 207;
  --cyan-7: 102, 223, 215;
  --cyan-8: 144, 233, 225;
  --cyan-9: 190, 244, 237;
  --cyan-10: 240, 255, 252;
  --blue-1: 0, 26, 77;
  --blue-2: 5, 47, 120;
  --blue-3: 19, 76, 163;
  --blue-4: 41, 113, 207;
  --blue-5: 70, 154, 250;
  --blue-6: 90, 170, 251;
  --blue-7: 125, 193, 252;
  --blue-8: 161, 213, 253;
  --blue-9: 198, 232, 254;
  --blue-10: 234, 248, 255;
  --arcoblue-1: 0, 13, 77;
  --arcoblue-2: 4, 27, 121;
  --arcoblue-3: 14, 50, 166;
  --arcoblue-4: 29, 77, 210;
  --arcoblue-5: 48, 111, 255;
  --arcoblue-6: 60, 126, 255;
  --arcoblue-7: 104, 159, 255;
  --arcoblue-8: 147, 190, 255;
  --arcoblue-9: 190, 218, 255;
  --arcoblue-10: 234, 244, 255;
  --purple-1: 22, 0, 77;
  --purple-2: 39, 6, 110;
  --purple-3: 62, 19, 143;
  --purple-4: 90, 37, 176;
  --purple-5: 123, 61, 209;
  --purple-6: 142, 81, 218;
  --purple-7: 169, 116, 227;
  --purple-8: 197, 154, 237;
  --purple-9: 223, 194, 246;
  --purple-10: 247, 237, 255;
  --pinkpurple-1: 66, 0, 77;
  --pinkpurple-2: 101, 3, 112;
  --pinkpurple-3: 138, 13, 147;
  --pinkpurple-4: 176, 27, 182;
  --pinkpurple-5: 217, 46, 217;
  --pinkpurple-6: 225, 61, 219;
  --pinkpurple-7: 232, 102, 223;
  --pinkpurple-8: 240, 146, 230;
  --pinkpurple-9: 247, 193, 240;
  --pinkpurple-10: 255, 242, 253;
  --magenta-1: 77, 0, 52;
  --magenta-2: 119, 8, 80;
  --magenta-3: 161, 23, 108;
  --magenta-4: 203, 43, 136;
  --magenta-5: 245, 69, 166;
  --magenta-6: 247, 86, 169;
  --magenta-7: 249, 122, 184;
  --magenta-8: 251, 158, 200;
  --magenta-9: 253, 195, 219;
  --magenta-10: 255, 232, 241;
  --gray-1: 23, 23, 26;
  --gray-2: 46, 46, 48;
  --gray-3: 72, 72, 73;
  --gray-4: 95, 95, 96;
  --gray-5: 120, 120, 122;
  --gray-6: 146, 146, 147;
  --gray-7: 171, 171, 172;
  --gray-8: 197, 197, 197;
  --gray-9: 223, 223, 223;
  --gray-10: 246, 246, 246;
  --primary-1: var(--arcoblue-1);
  --primary-2: var(--arcoblue-2);
  --primary-3: var(--arcoblue-3);
  --primary-4: var(--arcoblue-4);
  --primary-5: var(--arcoblue-5);
  --primary-6: var(--arcoblue-6);
  --primary-7: var(--arcoblue-7);
  --primary-8: var(--arcoblue-8);
  --primary-9: var(--arcoblue-9);
  --primary-10: var(--arcoblue-10);
  --link-1: var(--arcoblue-1);
  --link-2: var(--arcoblue-2);
  --link-3: var(--arcoblue-3);
  --link-4: var(--arcoblue-4);
  --link-5: var(--arcoblue-5);
  --link-6: var(--arcoblue-6);
  --link-7: var(--arcoblue-7);
  --link-8: var(--arcoblue-8);
  --link-9: var(--arcoblue-9);
  --link-10: var(--arcoblue-10);
  --success-1: var(--green-1);
  --success-2: var(--green-2);
  --success-3: var(--green-3);
  --success-4: var(--green-4);
  --success-5: var(--green-5);
  --success-6: var(--green-6);
  --success-7: var(--green-7);
  --success-8: var(--green-8);
  --success-9: var(--green-9);
  --success-10: var(--green-10);
  --danger-1: var(--red-1);
  --danger-2: var(--red-2);
  --danger-3: var(--red-3);
  --danger-4: var(--red-4);
  --danger-5: var(--red-5);
  --danger-6: var(--red-6);
  --danger-7: var(--red-7);
  --danger-8: var(--red-8);
  --danger-9: var(--red-9);
  --danger-10: var(--red-10);
  --warning-1: var(--orange-1);
  --warning-2: var(--orange-2);
  --warning-3: var(--orange-3);
  --warning-4: var(--orange-4);
  --warning-5: var(--orange-5);
  --warning-6: var(--orange-6);
  --warning-7: var(--orange-7);
  --warning-8: var(--orange-8);
  --warning-9: var(--orange-9);
  --warning-10: var(--orange-10);
}
#nprogress {
  pointer-events: none;
}
#nprogress .bar {
  background: #29d;
  position: fixed;
  z-index: 1031;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
}
#nprogress .peg {
  display: block;
  position: absolute;
  right: 0px;
  width: 100px;
  height: 100%;
  box-shadow: 0 0 10px #29d, 0 0 5px #29d;
  opacity: 1;
  -webkit-transform: rotate(3deg) translate(0px, -4px);
  -ms-transform: rotate(3deg) translate(0px, -4px);
  transform: rotate(3deg) translateY(-4px);
}
#nprogress .spinner {
  display: block;
  position: fixed;
  z-index: 1031;
  top: 15px;
  right: 15px;
}
#nprogress .spinner-icon {
  width: 18px;
  height: 18px;
  box-sizing: border-box;
  border: solid 2px transparent;
  border-top-color: #29d;
  border-left-color: #29d;
  border-radius: 50%;
  -webkit-animation: nprogress-spinner 0.4s linear infinite;
  animation: nprogress-spinner 0.4s linear infinite;
}
.nprogress-custom-parent {
  overflow: hidden;
  position: relative;
}
.nprogress-custom-parent #nprogress .spinner,
.nprogress-custom-parent #nprogress .bar {
  position: absolute;
}
@-webkit-keyframes nprogress-spinner {
  0% {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes nprogress-spinner {
  0% {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
  background-color: var(--color-bg-1);
}
.chart-wrapper .bizcharts-tooltip {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border-radius: 6px;
  backdrop-filter: blur(10px);
  padding: 8px !important;
  width: 180px !important;
  opacity: 1 !important;
}
body[arco-theme='dark'] .chart-wrapper .bizcharts-tooltip {
  background: linear-gradient(
    304.17deg,
    rgba(90, 92, 95, 0.6) -6.04%,
    rgba(87, 87, 87, 0.6) 85.2%
  ) !important;
  backdrop-filter: blur(10px);
  border-radius: 6px;
  box-shadow: none !important;
}
.arco-spin {
  display: inline-block;
}
.arco-spin-block {
  display: block;
}
.arco-spin-with-tip {
  text-align: center;
}
.arco-spin-icon {
  color: rgb(var(--primary-6));
  font-size: 20px;
}
.arco-spin-tip {
  margin-top: 6px;
  font-size: 14px;
  font-weight: 500;
  color: rgb(var(--primary-6));
}
.arco-spin-loading-layer {
  text-align: center;
  user-select: none;
}
.arco-spin-children {
  position: relative;
}
.arco-spin-children:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-spin-layer-bg);
  opacity: 0;
  transition: opacity 0.1s cubic-bezier(0, 0, 1, 1);
  pointer-events: none;
  z-index: 1;
}
.arco-spin-loading {
  position: relative;
  user-select: none;
}
.arco-spin-loading .arco-spin-loading-layer-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}
.arco-spin-loading .arco-spin-children:after {
  opacity: 1;
  pointer-events: auto;
}
.arco-spin-dot {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%) scale(0);
  width: 8px;
  height: 8px;
  background-color: rgb(var(--primary-6));
  border-radius: var(--border-radius-circle);
  animation: arco-dot-loading 2s cubic-bezier(0, 0, 1, 1) infinite forwards;
}
.arco-spin-dot:nth-child(2) {
  background-color: rgb(var(--primary-5));
  animation-delay: 0.4s;
}
.arco-spin-dot:nth-child(3) {
  background-color: rgb(var(--primary-4));
  animation-delay: 0.8s;
}
.arco-spin-dot:nth-child(4) {
  background-color: rgb(var(--primary-4));
  animation-delay: 1.2s;
}
.arco-spin-dot:nth-child(5) {
  background-color: rgb(var(--primary-2));
  animation-delay: 1.6s;
}
.arco-spin-dot-list {
  display: inline-block;
  transform-style: preserve-3d;
  perspective: 200px;
  width: 56px;
  position: relative;
  height: 8px;
}
@keyframes arco-dot-loading {
  0% {
    transform: translate3D(-48.621%, 0, -0.985px) scale(0.511);
  }
  2.778% {
    transform: translate3D(-95.766%, 0, -0.94px) scale(0.545);
  }
  5.556% {
    transform: translate3D(-140%, 0, -0.866px) scale(0.6);
  }
  8.333% {
    transform: translate3D(-179.981%, 0, -0.766px) scale(0.675);
  }
  11.111% {
    transform: translate3D(-214.492%, 0, -0.643px) scale(0.768);
  }
  13.889% {
    transform: translate3D(-242.487%, 0, -0.5px) scale(0.875);
  }
  16.667% {
    transform: translate3D(-263.114%, 0, -0.342px) scale(0.993);
  }
  19.444% {
    transform: translate3D(-275.746%, 0, -0.174px) scale(1.12);
  }
  22.222% {
    transform: translate3D(-280%, 0, 0) scale(1.25);
  }
  25% {
    transform: translate3D(-275.746%, 0, 0.174px) scale(1.38);
  }
  27.778% {
    transform: translate3D(-263.114%, 0, 0.342px) scale(1.507);
  }
  30.556% {
    transform: translate3D(-242.487%, 0, 0.5px) scale(1.625);
  }
  33.333% {
    transform: translate3D(-214.492%, 0, 0.643px) scale(1.732);
  }
  36.111% {
    transform: translate3D(-179.981%, 0, 0.766px) scale(1.825);
  }
  38.889% {
    transform: translate3D(-140%, 0, 0.866px) scale(1.9);
  }
  41.667% {
    transform: translate3D(-95.766%, 0, 0.94px) scale(1.955);
  }
  44.444% {
    transform: translate3D(-48.621%, 0, 0.985px) scale(1.989);
  }
  47.222% {
    transform: translateZ(1px) scale(2);
  }
  50% {
    transform: translate3D(48.621%, 0, 0.985px) scale(1.989);
  }
  52.778% {
    transform: translate3D(95.766%, 0, 0.94px) scale(1.955);
  }
  55.556% {
    transform: translate3D(140%, 0, 0.866px) scale(1.9);
  }
  58.333% {
    transform: translate3D(179.981%, 0, 0.766px) scale(1.825);
  }
  61.111% {
    transform: translate3D(214.492%, 0, 0.643px) scale(1.732);
  }
  63.889% {
    transform: translate3D(242.487%, 0, 0.5px) scale(1.625);
  }
  66.667% {
    transform: translate3D(263.114%, 0, 0.342px) scale(1.507);
  }
  69.444% {
    transform: translate3D(275.746%, 0, 0.174px) scale(1.38);
  }
  72.222% {
    transform: translate3D(280%, 0, 0) scale(1.25);
  }
  75% {
    transform: translate3D(275.746%, 0, -0.174px) scale(1.12);
  }
  77.778% {
    transform: translate3D(263.114%, 0, -0.342px) scale(0.993);
  }
  80.556% {
    transform: translate3D(242.487%, 0, -0.5px) scale(0.875);
  }
  83.333% {
    transform: translate3D(214.492%, 0, -0.643px) scale(0.768);
  }
  86.111% {
    transform: translate3D(179.981%, 0, -0.766px) scale(0.675);
  }
  88.889% {
    transform: translate3D(140%, 0, -0.866px) scale(0.6);
  }
  91.667% {
    transform: translate3D(95.766%, 0, -0.94px) scale(0.545);
  }
  94.444% {
    transform: translate3D(48.621%, 0, -0.985px) scale(0.511);
  }
  97.222% {
    transform: translateZ(-1px) scale(0.5);
  }
}
@keyframes arco-menu-selected-item-label-enter {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.arco-menu {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  font-size: 14px;
  line-height: 1.5715;
  transition: width 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-menu-indent {
  display: inline-block;
  width: 20px;
}
.arco-menu .arco-menu-item,
.arco-menu .arco-menu-group-title,
.arco-menu .arco-menu-pop-header,
.arco-menu .arco-menu-inline-header {
  position: relative;
  border-radius: var(--border-radius-small);
  box-sizing: border-box;
  cursor: pointer;
}
.arco-menu .arco-menu-item.arco-menu-disabled,
.arco-menu .arco-menu-group-title.arco-menu-disabled,
.arco-menu .arco-menu-pop-header.arco-menu-disabled,
.arco-menu .arco-menu-inline-header.arco-menu-disabled {
  cursor: not-allowed;
}
.arco-menu .arco-menu-item.arco-menu-selected,
.arco-menu .arco-menu-group-title.arco-menu-selected,
.arco-menu .arco-menu-pop-header.arco-menu-selected,
.arco-menu .arco-menu-inline-header.arco-menu-selected {
  font-weight: 500;
  transition: color 0.2s cubic-bezier(0, 0, 1, 1);
}
.arco-menu .arco-menu-item.arco-menu-selected svg,
.arco-menu .arco-menu-group-title.arco-menu-selected svg,
.arco-menu .arco-menu-pop-header.arco-menu-selected svg,
.arco-menu .arco-menu-inline-header.arco-menu-selected svg {
  transition: color 0.2s cubic-bezier(0, 0, 1, 1);
}
.arco-menu .arco-menu-item .arco-icon,
.arco-menu .arco-menu-group-title .arco-icon,
.arco-menu .arco-menu-pop-header .arco-icon,
.arco-menu .arco-menu-inline-header .arco-icon {
  margin-right: 16px;
}
.arco-menu-light {
  background-color: var(--color-menu-light-bg);
}
.arco-menu-light .arco-menu-item,
.arco-menu-light .arco-menu-group-title,
.arco-menu-light .arco-menu-pop-header,
.arco-menu-light .arco-menu-inline-header {
  background-color: var(--color-menu-light-bg);
  color: var(--color-text-2);
}
.arco-menu-light .arco-menu-item .arco-icon,
.arco-menu-light .arco-menu-group-title .arco-icon,
.arco-menu-light .arco-menu-pop-header .arco-icon,
.arco-menu-light .arco-menu-inline-header .arco-icon {
  color: var(--color-text-3);
}
.arco-menu-light .arco-menu-item:hover,
.arco-menu-light .arco-menu-group-title:hover,
.arco-menu-light .arco-menu-pop-header:hover,
.arco-menu-light .arco-menu-inline-header:hover {
  background-color: var(--color-fill-2);
  color: var(--color-text-2);
}
.arco-menu-light .arco-menu-item:hover .arco-icon,
.arco-menu-light .arco-menu-group-title:hover .arco-icon,
.arco-menu-light .arco-menu-pop-header:hover .arco-icon,
.arco-menu-light .arco-menu-inline-header:hover .arco-icon {
  color: var(--color-text-3);
}
.arco-menu-light .arco-menu-item:focus-visible,
.arco-menu-light .arco-menu-group-title:focus-visible,
.arco-menu-light .arco-menu-pop-header:focus-visible,
.arco-menu-light .arco-menu-inline-header:focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--primary-6)) inset;
}
.arco-menu-light .arco-menu-item.arco-menu-selected,
.arco-menu-light .arco-menu-group-title.arco-menu-selected,
.arco-menu-light .arco-menu-pop-header.arco-menu-selected,
.arco-menu-light .arco-menu-inline-header.arco-menu-selected,
.arco-menu-light .arco-menu-item.arco-menu-selected .arco-icon,
.arco-menu-light .arco-menu-group-title.arco-menu-selected .arco-icon,
.arco-menu-light .arco-menu-pop-header.arco-menu-selected .arco-icon,
.arco-menu-light .arco-menu-inline-header.arco-menu-selected .arco-icon {
  color: rgb(var(--primary-6));
}
.arco-menu-light .arco-menu-item.arco-menu-disabled,
.arco-menu-light .arco-menu-group-title.arco-menu-disabled,
.arco-menu-light .arco-menu-pop-header.arco-menu-disabled,
.arco-menu-light .arco-menu-inline-header.arco-menu-disabled {
  background-color: var(--color-menu-light-bg);
  color: var(--color-text-4);
}
.arco-menu-light .arco-menu-item.arco-menu-disabled .arco-icon,
.arco-menu-light .arco-menu-group-title.arco-menu-disabled .arco-icon,
.arco-menu-light .arco-menu-pop-header.arco-menu-disabled .arco-icon,
.arco-menu-light .arco-menu-inline-header.arco-menu-disabled .arco-icon {
  color: var(--color-text-4);
}
.arco-menu-light .arco-menu-item.arco-menu-selected {
  background-color: var(--color-fill-2);
}
.arco-menu-light .arco-menu-inline-header.arco-menu-selected,
.arco-menu-light .arco-menu-inline-header.arco-menu-selected .arco-icon {
  color: rgb(var(--primary-6));
}
.arco-menu-light .arco-menu-inline-header.arco-menu-selected:hover {
  background-color: var(--color-fill-2);
}
.arco-menu-light.arco-menu-horizontal .arco-menu-item.arco-menu-selected,
.arco-menu-light.arco-menu-horizontal .arco-menu-group-title.arco-menu-selected,
.arco-menu-light.arco-menu-horizontal .arco-menu-pop-header.arco-menu-selected,
.arco-menu-light.arco-menu-horizontal
  .arco-menu-inline-header.arco-menu-selected {
  background: none;
  transition: color 0.2s cubic-bezier(0, 0, 1, 1);
}
.arco-menu-light.arco-menu-horizontal .arco-menu-item.arco-menu-selected:hover,
.arco-menu-light.arco-menu-horizontal
  .arco-menu-group-title.arco-menu-selected:hover,
.arco-menu-light.arco-menu-horizontal
  .arco-menu-pop-header.arco-menu-selected:hover,
.arco-menu-light.arco-menu-horizontal
  .arco-menu-inline-header.arco-menu-selected:hover {
  background-color: var(--color-fill-2);
}
.arco-menu-light .arco-menu-group-title {
  color: var(--color-text-3);
  pointer-events: none;
}
.arco-menu-light .arco-menu-collapse-button {
  background-color: var(--color-fill-1);
  color: var(--color-text-3);
}
.arco-menu-light .arco-menu-collapse-button:hover {
  background-color: var(--color-fill-3);
}
.arco-menu-light .arco-menu-collapse-button:focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--primary-6));
}
.arco-menu-dark {
  background-color: var(--color-menu-dark-bg);
}
.arco-menu-dark .arco-menu-item,
.arco-menu-dark .arco-menu-group-title,
.arco-menu-dark .arco-menu-pop-header,
.arco-menu-dark .arco-menu-inline-header {
  background-color: var(--color-menu-dark-bg);
  color: var(--color-text-4);
}
.arco-menu-dark .arco-menu-item .arco-icon,
.arco-menu-dark .arco-menu-group-title .arco-icon,
.arco-menu-dark .arco-menu-pop-header .arco-icon,
.arco-menu-dark .arco-menu-inline-header .arco-icon {
  color: var(--color-text-3);
}
.arco-menu-dark .arco-menu-item:hover,
.arco-menu-dark .arco-menu-group-title:hover,
.arco-menu-dark .arco-menu-pop-header:hover,
.arco-menu-dark .arco-menu-inline-header:hover {
  background-color: var(--color-menu-dark-hover);
  color: var(--color-text-4);
}
.arco-menu-dark .arco-menu-item:hover .arco-icon,
.arco-menu-dark .arco-menu-group-title:hover .arco-icon,
.arco-menu-dark .arco-menu-pop-header:hover .arco-icon,
.arco-menu-dark .arco-menu-inline-header:hover .arco-icon {
  color: var(--color-text-3);
}
.arco-menu-dark .arco-menu-item:focus-visible,
.arco-menu-dark .arco-menu-group-title:focus-visible,
.arco-menu-dark .arco-menu-pop-header:focus-visible,
.arco-menu-dark .arco-menu-inline-header:focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--primary-6)) inset;
}
.arco-menu-dark .arco-menu-item.arco-menu-selected,
.arco-menu-dark .arco-menu-group-title.arco-menu-selected,
.arco-menu-dark .arco-menu-pop-header.arco-menu-selected,
.arco-menu-dark .arco-menu-inline-header.arco-menu-selected,
.arco-menu-dark .arco-menu-item.arco-menu-selected .arco-icon,
.arco-menu-dark .arco-menu-group-title.arco-menu-selected .arco-icon,
.arco-menu-dark .arco-menu-pop-header.arco-menu-selected .arco-icon,
.arco-menu-dark .arco-menu-inline-header.arco-menu-selected .arco-icon {
  color: var(--color-white);
}
.arco-menu-dark .arco-menu-item.arco-menu-disabled,
.arco-menu-dark .arco-menu-group-title.arco-menu-disabled,
.arco-menu-dark .arco-menu-pop-header.arco-menu-disabled,
.arco-menu-dark .arco-menu-inline-header.arco-menu-disabled {
  background-color: var(--color-menu-dark-bg);
  color: var(--color-text-2);
}
.arco-menu-dark .arco-menu-item.arco-menu-disabled .arco-icon,
.arco-menu-dark .arco-menu-group-title.arco-menu-disabled .arco-icon,
.arco-menu-dark .arco-menu-pop-header.arco-menu-disabled .arco-icon,
.arco-menu-dark .arco-menu-inline-header.arco-menu-disabled .arco-icon {
  color: var(--color-text-2);
}
.arco-menu-dark .arco-menu-item.arco-menu-selected {
  background-color: var(--color-menu-dark-hover);
}
.arco-menu-dark .arco-menu-inline-header.arco-menu-selected,
.arco-menu-dark .arco-menu-inline-header.arco-menu-selected .arco-icon {
  color: rgb(var(--primary-6));
}
.arco-menu-dark .arco-menu-inline-header.arco-menu-selected:hover {
  background-color: var(--color-menu-dark-hover);
}
.arco-menu-dark.arco-menu-horizontal .arco-menu-item.arco-menu-selected,
.arco-menu-dark.arco-menu-horizontal .arco-menu-group-title.arco-menu-selected,
.arco-menu-dark.arco-menu-horizontal .arco-menu-pop-header.arco-menu-selected,
.arco-menu-dark.arco-menu-horizontal
  .arco-menu-inline-header.arco-menu-selected {
  background: none;
  transition: color 0.2s cubic-bezier(0, 0, 1, 1);
}
.arco-menu-dark.arco-menu-horizontal .arco-menu-item.arco-menu-selected:hover,
.arco-menu-dark.arco-menu-horizontal
  .arco-menu-group-title.arco-menu-selected:hover,
.arco-menu-dark.arco-menu-horizontal
  .arco-menu-pop-header.arco-menu-selected:hover,
.arco-menu-dark.arco-menu-horizontal
  .arco-menu-inline-header.arco-menu-selected:hover {
  background-color: var(--color-menu-dark-hover);
}
.arco-menu-dark .arco-menu-group-title {
  color: var(--color-text-3);
  pointer-events: none;
}
.arco-menu-dark .arco-menu-collapse-button {
  background-color: rgb(var(--primary-6));
  color: var(--color-white);
}
.arco-menu-dark .arco-menu-collapse-button:hover {
  background-color: rgb(var(--primary-7));
}
.arco-menu-dark .arco-menu-collapse-button:focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--primary-6));
}
.arco-menu a,
.arco-menu a:hover,
.arco-menu a:focus,
.arco-menu a:active {
  color: inherit;
  cursor: inherit;
  text-decoration: none;
}
.arco-menu-item-inner > a:only-child:before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.arco-menu-inner {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  overflow: auto;
}
.arco-menu-vertical .arco-menu-item,
.arco-menu-vertical .arco-menu-group-title,
.arco-menu-vertical .arco-menu-pop-header,
.arco-menu-vertical .arco-menu-inline-header {
  padding: 0 12px;
  line-height: 40px;
}
.arco-menu-vertical .arco-menu-item .arco-menu-icon-suffix .arco-icon,
.arco-menu-vertical .arco-menu-group-title .arco-menu-icon-suffix .arco-icon,
.arco-menu-vertical .arco-menu-pop-header .arco-menu-icon-suffix .arco-icon,
.arco-menu-vertical .arco-menu-inline-header .arco-menu-icon-suffix .arco-icon {
  margin-right: 0;
}
.arco-menu-vertical .arco-menu-item,
.arco-menu-vertical .arco-menu-group-title,
.arco-menu-vertical .arco-menu-pop-header,
.arco-menu-vertical .arco-menu-inline-header {
  margin-bottom: 4px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-menu-vertical .arco-menu-item .arco-menu-item-inner,
.arco-menu-vertical .arco-menu-group-title .arco-menu-item-inner,
.arco-menu-vertical .arco-menu-pop-header .arco-menu-item-inner,
.arco-menu-vertical .arco-menu-inline-header .arco-menu-item-inner {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
}
.arco-menu-vertical .arco-menu-item .arco-menu-icon-suffix,
.arco-menu-vertical .arco-menu-group-title .arco-menu-icon-suffix,
.arco-menu-vertical .arco-menu-pop-header .arco-menu-icon-suffix,
.arco-menu-vertical .arco-menu-inline-header .arco-menu-icon-suffix {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
}
.arco-menu-vertical .arco-menu-item .arco-menu-icon-suffix.is-open,
.arco-menu-vertical .arco-menu-group-title .arco-menu-icon-suffix.is-open,
.arco-menu-vertical .arco-menu-pop-header .arco-menu-icon-suffix.is-open,
.arco-menu-vertical .arco-menu-inline-header .arco-menu-icon-suffix.is-open {
  transform: translateY(-50%) rotate(180deg);
}
.arco-menu-vertical .arco-menu-inner {
  padding: 4px 8px;
}
.arco-menu-vertical .arco-menu-item.arco-menu-item-indented {
  display: flex;
}
.arco-menu-vertical .arco-menu-pop-header,
.arco-menu-vertical .arco-menu-inline-header {
  padding-right: 28px;
}
.arco-menu-horizontal {
  width: auto;
  height: auto;
}
.arco-menu-horizontal .arco-menu-item,
.arco-menu-horizontal .arco-menu-group-title,
.arco-menu-horizontal .arco-menu-pop-header,
.arco-menu-horizontal .arco-menu-inline-header {
  padding: 0 12px;
  line-height: 30px;
}
.arco-menu-horizontal .arco-menu-item .arco-menu-icon-suffix .arco-icon,
.arco-menu-horizontal .arco-menu-group-title .arco-menu-icon-suffix .arco-icon,
.arco-menu-horizontal .arco-menu-pop-header .arco-menu-icon-suffix .arco-icon,
.arco-menu-horizontal
  .arco-menu-inline-header
  .arco-menu-icon-suffix
  .arco-icon {
  margin-right: 0;
}
.arco-menu-horizontal .arco-menu-item .arco-icon,
.arco-menu-horizontal .arco-menu-group-title .arco-icon,
.arco-menu-horizontal .arco-menu-pop-header .arco-icon,
.arco-menu-horizontal .arco-menu-inline-header .arco-icon {
  margin-right: 16px;
}
.arco-menu-horizontal .arco-menu-item .arco-menu-icon-suffix,
.arco-menu-horizontal .arco-menu-group-title .arco-menu-icon-suffix,
.arco-menu-horizontal .arco-menu-pop-header .arco-menu-icon-suffix,
.arco-menu-horizontal .arco-menu-inline-header .arco-menu-icon-suffix {
  margin-left: 6px;
}
.arco-menu-horizontal .arco-menu-inner {
  display: flex;
  align-items: center;
  padding: 14px 20px;
}
.arco-menu-horizontal .arco-menu-item,
.arco-menu-horizontal .arco-menu-pop {
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
}
.arco-menu-horizontal .arco-menu-item:not(:first-child),
.arco-menu-horizontal .arco-menu-pop:not(:first-child) {
  margin-left: 12px;
}
.arco-menu-horizontal .arco-menu-pop:after {
  content: ' ';
  width: 100%;
  height: 14px;
  position: absolute;
  left: 0;
  bottom: -14px;
}
.arco-menu-overflow-wrap {
  width: 100%;
}
.arco-menu-overflow-sub-menu-mirror {
  margin-left: 12px;
}
.arco-menu-overflow-sub-menu-mirror,
.arco-menu-overflow-hidden-menu-item {
  position: absolute !important;
  white-space: nowrap;
  visibility: hidden;
  pointer-events: none;
}
.arco-menu-selected-label {
  position: absolute;
  left: 12px;
  right: 12px;
  bottom: -14px;
  height: 3px;
  background-color: rgb(var(--primary-6));
  animation: arco-menu-selected-item-label-enter 0.2s cubic-bezier(0, 0, 1, 1);
}
.arco-menu-pop-button {
  width: auto;
  background: none;
  box-shadow: none;
}
.arco-menu-pop-button.arco-menu-collapse {
  width: auto;
}
.arco-menu-pop-button .arco-menu-item,
.arco-menu-pop-button .arco-menu-group-title,
.arco-menu-pop-button .arco-menu-pop-header,
.arco-menu-pop-button .arco-menu-inline-header {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 50%;
  border: 1px solid transparent;
  box-shadow: 0 4px 10px #0000001a;
  margin-bottom: 16px;
}
.arco-menu-collapse {
  width: 48px;
}
.arco-menu-collapse .arco-menu-inner {
  padding: 4px;
}
.arco-menu-collapse .arco-menu-group-title,
.arco-menu-collapse .arco-menu-icon-suffix {
  display: none;
}
.arco-menu-collapse .arco-menu-item .arco-icon,
.arco-menu-collapse .arco-menu-group-title .arco-icon,
.arco-menu-collapse .arco-menu-pop-header .arco-icon,
.arco-menu-collapse .arco-menu-inline-header .arco-icon {
  margin-left: 1px;
  margin-right: 100vw;
}
.arco-menu-collapse .arco-menu-collapse-button {
  right: unset;
  left: 50%;
  transform: translate(-50%);
}
.arco-menu-collapse-button {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 12px;
  bottom: 12px;
  width: 24px;
  height: 24px;
  border-radius: var(--border-radius-small);
  cursor: pointer;
}
.arco-menu-inline-content {
  overflow: hidden;
  height: auto;
  transition: height 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-menu-item-tooltip a {
  color: inherit;
  cursor: pointer;
  text-decoration: none;
}
.arco-menu-item-tooltip a:hover,
.arco-menu-item-tooltip a:focus,
.arco-menu-item-tooltip a:active {
  color: inherit;
}
.arco-menu-item-tooltip a:before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.arco-menu-pop-trigger.arco-trigger-position-bl {
  transform: translateY(14px);
}
.arco-menu-pop-trigger.arco-trigger-position-bl .arco-trigger-arrow {
  z-index: 0;
  border-left: 1px solid var(--color-neutral-3);
  border-top: 1px solid var(--color-neutral-3);
}
.arco-menu-pop-trigger.arco-trigger[trigger-placement='rt'] {
  transform: translate(8px);
}
.arco-menu-pop-trigger.arco-trigger[trigger-placement='rt']
  .arco-trigger-arrow {
  z-index: 0;
  border-left: 1px solid var(--color-neutral-3);
  border-bottom: 1px solid var(--color-neutral-3);
}
.arco-menu-pop-trigger.arco-trigger[trigger-placement='lt'] {
  transform: translate(-8px);
}
.arco-menu-pop-trigger.arco-trigger[trigger-placement='lt']
  .arco-trigger-arrow {
  z-index: 0;
  border-right: 1px solid var(--color-neutral-3);
  border-top: 1px solid var(--color-neutral-3);
}
.arco-menu-pop-trigger
  .arco-dropdown-menu-dark
  ~ .arco-trigger-arrow-container
  .arco-trigger-arrow {
  background-color: var(--color-menu-dark-bg);
  border-color: var(--color-menu-dark-bg);
}
.arco-menu-rtl {
  direction: rtl;
}
.arco-menu-rtl .arco-menu-item .arco-icon,
.arco-menu-rtl .arco-menu-group-title .arco-icon,
.arco-menu-rtl .arco-menu-pop-header .arco-icon,
.arco-menu-rtl .arco-menu-inline-header .arco-icon {
  margin-right: 0;
  margin-left: 16px;
}
.arco-menu-rtl.arco-menu-horizontal
  .arco-menu-item
  .arco-menu-icon-suffix
  .arco-icon,
.arco-menu-rtl.arco-menu-horizontal
  .arco-menu-group-title
  .arco-menu-icon-suffix
  .arco-icon,
.arco-menu-rtl.arco-menu-horizontal
  .arco-menu-pop-header
  .arco-menu-icon-suffix
  .arco-icon,
.arco-menu-rtl.arco-menu-horizontal
  .arco-menu-inline-header
  .arco-menu-icon-suffix
  .arco-icon {
  margin-left: 0;
}
.arco-menu-rtl.arco-menu-horizontal .arco-menu-item .arco-icon,
.arco-menu-rtl.arco-menu-horizontal .arco-menu-group-title .arco-icon,
.arco-menu-rtl.arco-menu-horizontal .arco-menu-pop-header .arco-icon,
.arco-menu-rtl.arco-menu-horizontal .arco-menu-inline-header .arco-icon {
  margin-right: 0;
  margin-left: 16px;
}
.arco-menu-rtl.arco-menu-horizontal .arco-menu-item .arco-menu-icon-suffix,
.arco-menu-rtl.arco-menu-horizontal
  .arco-menu-group-title
  .arco-menu-icon-suffix,
.arco-menu-rtl.arco-menu-horizontal
  .arco-menu-pop-header
  .arco-menu-icon-suffix,
.arco-menu-rtl.arco-menu-horizontal
  .arco-menu-inline-header
  .arco-menu-icon-suffix {
  margin-left: 0;
  margin-right: 6px;
}
.arco-menu-rtl.arco-menu-horizontal .arco-menu-item:not(:first-child),
.arco-menu-rtl.arco-menu-horizontal .arco-menu-pop:not(:first-child) {
  margin-left: 0;
  margin-right: 12px;
}
.arco-menu-rtl.arco-menu-vertical
  .arco-menu-item
  .arco-menu-icon-suffix
  .arco-icon,
.arco-menu-rtl.arco-menu-vertical
  .arco-menu-group-title
  .arco-menu-icon-suffix
  .arco-icon,
.arco-menu-rtl.arco-menu-vertical
  .arco-menu-pop-header
  .arco-menu-icon-suffix
  .arco-icon,
.arco-menu-rtl.arco-menu-vertical
  .arco-menu-inline-header
  .arco-menu-icon-suffix
  .arco-icon {
  margin-left: 0;
}
.arco-menu-rtl.arco-menu-vertical .arco-menu-item,
.arco-menu-rtl.arco-menu-vertical .arco-menu-group-title,
.arco-menu-rtl.arco-menu-vertical .arco-menu-pop-header,
.arco-menu-rtl.arco-menu-vertical .arco-menu-inline-header,
.arco-menu-rtl.arco-menu-vertical .arco-menu-item .arco-menu-item-inner,
.arco-menu-rtl.arco-menu-vertical .arco-menu-group-title .arco-menu-item-inner,
.arco-menu-rtl.arco-menu-vertical .arco-menu-pop-header .arco-menu-item-inner,
.arco-menu-rtl.arco-menu-vertical
  .arco-menu-inline-header
  .arco-menu-item-inner {
  text-overflow: clip;
}
.arco-menu-rtl.arco-menu-vertical .arco-menu-item .arco-menu-icon-suffix,
.arco-menu-rtl.arco-menu-vertical .arco-menu-group-title .arco-menu-icon-suffix,
.arco-menu-rtl.arco-menu-vertical .arco-menu-pop-header .arco-menu-icon-suffix,
.arco-menu-rtl.arco-menu-vertical
  .arco-menu-inline-header
  .arco-menu-icon-suffix {
  right: initial;
  left: 12px;
}
.arco-menu-rtl.arco-menu-vertical .arco-menu-pop-header,
.arco-menu-rtl.arco-menu-vertical .arco-menu-inline-header {
  padding-right: 12px;
  padding-left: 28px;
}
.arco-menu-rtl .arco-menu-pop:after {
  right: 0;
  left: initial;
}
.arco-menu-rtl .arco-menu-collapse .arco-menu-item .arco-icon,
.arco-menu-rtl .arco-menu-collapse .arco-menu-group-title .arco-icon,
.arco-menu-rtl .arco-menu-collapse .arco-menu-pop-header .arco-icon,
.arco-menu-rtl .arco-menu-collapse .arco-menu-inline-header .arco-icon {
  margin-left: 100vw;
  margin-right: 1px;
}
.arco-menu-rtl
  .arco-menu-pop-trigger.arco-trigger-position-bl
  .arco-trigger-arrow {
  border-left: none;
  border-right: 1px solid var(--color-neutral-3);
}
.arco-menu-rtl .arco-menu-pop-trigger.arco-trigger[trigger-placement='rt'] {
  transform: translate(-8px);
}
.arco-menu-rtl .arco-menu-pop-trigger.arco-trigger[trigger-placement='lt'] {
  transform: translate(8px);
}
.arco-menu-rtl
  .arco-menu-pop-trigger.arco-trigger[trigger-placement='lt']
  .arco-trigger-arrow {
  border-right: none;
  border-left: 1px solid var(--color-neutral-3);
}
.arco-breadcrumb {
  display: inline-block;
  font-size: 14px;
  color: var(--color-text-2);
}
.arco-breadcrumb-icon {
  color: var(--color-text-2);
}
.arco-breadcrumb-item {
  display: inline-flex;
  align-items: center;
  padding: 0 4px;
  vertical-align: middle;
  line-height: 24px;
  color: var(--color-text-2);
}
.arco-breadcrumb-item > .arco-icon {
  color: var(--color-text-3);
}
.arco-breadcrumb-item a,
.arco-breadcrumb-item[href] {
  display: inline-block;
  border-radius: var(--border-radius-small);
  padding: 0 4px;
  margin: 0 -4px;
  text-decoration: none;
  color: var(--color-text-2);
  background-color: transparent;
}
.arco-breadcrumb-item a:hover,
.arco-breadcrumb-item[href]:hover {
  background-color: var(--color-fill-2);
  color: rgb(var(--link-6));
}
.arco-breadcrumb-item a:focus-visible,
.arco-breadcrumb-item[href]:focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--primary-6));
}
.arco-breadcrumb-item:last-child {
  color: var(--color-text-1);
  font-weight: 500;
}
.arco-breadcrumb-item-ellipses {
  display: inline-block;
  position: relative;
  top: -3px;
  padding: 0 4px;
  color: var(--color-text-2);
}
.arco-breadcrumb-item-separator {
  display: inline-block;
  margin: 0 4px;
  vertical-align: middle;
  line-height: 24px;
  color: var(--color-text-4);
}
.arco-breadcrumb-item-with-dropdown {
  cursor: pointer;
}
.arco-breadcrumb-item-dropdown-icon {
  font-size: 12px;
  margin-left: 4px;
  color: var(--color-text-2);
}
.arco-breadcrumb-item-dropdown-icon-active svg {
  transform: rotate(180deg);
}
.arco-breadcrumb-rtl .arco-breadcrumb-item-dropdown-icon {
  margin-left: 0;
  margin-right: 4px;
}
.arco-btn {
  display: inline-block;
  position: relative;
  outline: none;
  font-weight: 400;
  appearance: none;
  user-select: none;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
  box-sizing: border-box;
  line-height: 1.5715;
}
.arco-btn > a:only-child {
  color: currentColor;
}
.arco-btn:active {
  transition: none;
}
.arco-btn:empty {
  display: inline-block;
  vertical-align: bottom;
}
.arco-btn-long {
  display: block;
  width: 100%;
}
.arco-btn-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}
.arco-btn-link:not([href]) {
  color: var(--color-text-4);
}
.arco-btn-link:hover {
  text-decoration: none;
}
.arco-btn-loading {
  cursor: default;
  position: relative;
}
.arco-btn-loading:before {
  content: '';
  position: absolute;
  top: -1px;
  right: -1px;
  bottom: -1px;
  left: -1px;
  z-index: 1;
  display: block;
  background: #fff;
  border-radius: inherit;
  opacity: 0.4;
  transition: opacity 0.1s cubic-bezier(0, 0, 1, 1);
  pointer-events: none;
}
.arco-btn-loading-fixed-width {
  transition: none;
}
.arco-btn-two-chinese-chars > *:not(svg) {
  letter-spacing: 0.3em;
  margin-right: -0.3em;
}
a.arco-btn-icon-only {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: top;
}
.arco-btn-outline:not(.arco-btn-disabled) {
  background-color: transparent;
  color: rgb(var(--primary-6));
  border: 1px solid rgb(var(--primary-6));
}
.arco-btn-outline:not(.arco-btn-disabled):not(.arco-btn-loading):hover {
  border-color: rgb(var(--primary-5));
  color: rgb(var(--primary-5));
  background-color: transparent;
}
.arco-btn-outline:not(.arco-btn-disabled):not(.arco-btn-loading):active {
  border-color: rgb(var(--primary-7));
  color: rgb(var(--primary-7));
  background-color: transparent;
}
.arco-btn-outline:not(.arco-btn-disabled):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--primary-3));
}
.arco-btn-outline.arco-btn-disabled {
  background-color: transparent;
  color: var(--color-primary-light-3);
  border: 1px solid var(--color-primary-light-3);
  cursor: not-allowed;
}
.arco-btn-outline.arco-btn-status-warning:not(.arco-btn-disabled) {
  background-color: transparent;
  color: rgb(var(--warning-6));
  border-color: rgb(var(--warning-6));
}
.arco-btn-outline.arco-btn-status-warning:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):hover {
  border-color: rgb(var(--warning-5));
  color: rgb(var(--warning-5));
  background-color: transparent;
}
.arco-btn-outline.arco-btn-status-warning:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):active {
  border-color: rgb(var(--warning-7));
  color: rgb(var(--warning-7));
  background-color: transparent;
}
.arco-btn-outline.arco-btn-status-warning:not(
    .arco-btn-disabled
  ):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--warning-3));
}
.arco-btn-outline.arco-btn-status-warning.arco-btn-disabled {
  color: var(--color-warning-light-3);
  background-color: transparent;
  border: 1px solid var(--color-warning-light-3);
}
.arco-btn-outline.arco-btn-status-danger:not(.arco-btn-disabled) {
  background-color: transparent;
  color: rgb(var(--danger-6));
  border-color: rgb(var(--danger-6));
}
.arco-btn-outline.arco-btn-status-danger:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):hover {
  border-color: rgb(var(--danger-5));
  color: rgb(var(--danger-5));
  background-color: transparent;
}
.arco-btn-outline.arco-btn-status-danger:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):active {
  border-color: rgb(var(--danger-7));
  color: rgb(var(--danger-7));
  background-color: transparent;
}
.arco-btn-outline.arco-btn-status-danger:not(.arco-btn-disabled):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--danger-3));
}
.arco-btn-outline.arco-btn-status-danger.arco-btn-disabled {
  color: var(--color-danger-light-3);
  background-color: transparent;
  border: 1px solid var(--color-danger-light-3);
}
.arco-btn-outline.arco-btn-status-success:not(.arco-btn-disabled) {
  background-color: transparent;
  color: rgb(var(--success-6));
  border-color: rgb(var(--success-6));
}
.arco-btn-outline.arco-btn-status-success:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):hover {
  border-color: rgb(var(--success-5));
  color: rgb(var(--success-5));
  background-color: transparent;
}
.arco-btn-outline.arco-btn-status-success:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):active {
  border-color: rgb(var(--success-7));
  color: rgb(var(--success-7));
  background-color: transparent;
}
.arco-btn-outline.arco-btn-status-success:not(
    .arco-btn-disabled
  ):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--success-3));
}
.arco-btn-outline.arco-btn-status-success.arco-btn-disabled {
  color: var(--color-success-light-3);
  background-color: transparent;
  border: 1px solid var(--color-success-light-3);
}
.arco-btn-primary:not(.arco-btn-disabled) {
  background-color: rgb(var(--primary-6));
  color: #fff;
  border: 1px solid transparent;
}
.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover {
  border-color: transparent;
  color: #fff;
  background-color: rgb(var(--primary-5));
}
.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):active {
  border-color: transparent;
  color: #fff;
  background-color: rgb(var(--primary-7));
}
.arco-btn-primary:not(.arco-btn-disabled):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--primary-3));
}
.arco-btn-primary.arco-btn-disabled {
  background-color: var(--color-primary-light-3);
  color: #fff;
  border: 1px solid transparent;
  cursor: not-allowed;
}
.arco-btn-primary.arco-btn-status-warning:not(.arco-btn-disabled) {
  background-color: rgb(var(--warning-6));
  color: #fff;
  border-color: transparent;
}
.arco-btn-primary.arco-btn-status-warning:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):hover {
  border-color: transparent;
  color: #fff;
  background-color: rgb(var(--warning-5));
}
.arco-btn-primary.arco-btn-status-warning:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):active {
  border-color: transparent;
  color: #fff;
  background-color: rgb(var(--warning-7));
}
.arco-btn-primary.arco-btn-status-warning:not(
    .arco-btn-disabled
  ):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--warning-3));
}
.arco-btn-primary.arco-btn-status-warning.arco-btn-disabled {
  color: #fff;
  background-color: var(--color-warning-light-3);
  border: 1px solid transparent;
}
.arco-btn-primary.arco-btn-status-danger:not(.arco-btn-disabled) {
  background-color: rgb(var(--danger-6));
  color: #fff;
  border-color: transparent;
}
.arco-btn-primary.arco-btn-status-danger:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):hover {
  border-color: transparent;
  color: #fff;
  background-color: rgb(var(--danger-5));
}
.arco-btn-primary.arco-btn-status-danger:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):active {
  border-color: transparent;
  color: #fff;
  background-color: rgb(var(--danger-7));
}
.arco-btn-primary.arco-btn-status-danger:not(.arco-btn-disabled):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--danger-3));
}
.arco-btn-primary.arco-btn-status-danger.arco-btn-disabled {
  color: #fff;
  background-color: var(--color-danger-light-3);
  border: 1px solid transparent;
}
.arco-btn-primary.arco-btn-status-success:not(.arco-btn-disabled) {
  background-color: rgb(var(--success-6));
  color: #fff;
  border-color: transparent;
}
.arco-btn-primary.arco-btn-status-success:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):hover {
  border-color: transparent;
  color: #fff;
  background-color: rgb(var(--success-5));
}
.arco-btn-primary.arco-btn-status-success:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):active {
  border-color: transparent;
  color: #fff;
  background-color: rgb(var(--success-7));
}
.arco-btn-primary.arco-btn-status-success:not(
    .arco-btn-disabled
  ):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--success-3));
}
.arco-btn-primary.arco-btn-status-success.arco-btn-disabled {
  color: #fff;
  background-color: var(--color-success-light-3);
  border: 1px solid transparent;
}
.arco-btn-secondary:not(.arco-btn-disabled) {
  background-color: var(--color-secondary);
  color: var(--color-text-2);
  border: 1px solid transparent;
}
.arco-btn-secondary:not(.arco-btn-disabled):not(.arco-btn-loading):hover {
  border-color: transparent;
  color: var(--color-text-2);
  background-color: var(--color-secondary-hover);
}
.arco-btn-secondary:not(.arco-btn-disabled):not(.arco-btn-loading):active {
  border-color: transparent;
  color: var(--color-text-2);
  background-color: var(--color-secondary-active);
}
.arco-btn-secondary:not(.arco-btn-disabled):focus-visible {
  box-shadow: 0 0 0 2px var(--color-neutral-4);
}
.arco-btn-secondary.arco-btn-disabled {
  background-color: var(--color-secondary-disabled);
  color: var(--color-text-4);
  border: 1px solid transparent;
  cursor: not-allowed;
}
.arco-btn-secondary.arco-btn-status-warning:not(.arco-btn-disabled) {
  background-color: var(--color-warning-light-1);
  color: rgb(var(--warning-6));
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-status-warning:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):hover {
  border-color: transparent;
  color: rgb(var(--warning-6));
  background-color: var(--color-warning-light-2);
}
.arco-btn-secondary.arco-btn-status-warning:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):active {
  border-color: transparent;
  color: rgb(var(--warning-6));
  background-color: var(--color-warning-light-3);
}
.arco-btn-secondary.arco-btn-status-warning:not(
    .arco-btn-disabled
  ):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--warning-3));
}
.arco-btn-secondary.arco-btn-status-warning.arco-btn-disabled {
  color: var(--color-warning-light-3);
  background-color: var(--color-warning-light-1);
  border: 1px solid transparent;
}
.arco-btn-secondary.arco-btn-status-danger:not(.arco-btn-disabled) {
  background-color: var(--color-danger-light-1);
  color: rgb(var(--danger-6));
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-status-danger:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):hover {
  border-color: transparent;
  color: rgb(var(--danger-6));
  background-color: var(--color-danger-light-2);
}
.arco-btn-secondary.arco-btn-status-danger:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):active {
  border-color: transparent;
  color: rgb(var(--danger-6));
  background-color: var(--color-danger-light-3);
}
.arco-btn-secondary.arco-btn-status-danger:not(
    .arco-btn-disabled
  ):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--danger-3));
}
.arco-btn-secondary.arco-btn-status-danger.arco-btn-disabled {
  color: var(--color-danger-light-3);
  background-color: var(--color-danger-light-1);
  border: 1px solid transparent;
}
.arco-btn-secondary.arco-btn-status-success:not(.arco-btn-disabled) {
  background-color: var(--color-success-light-1);
  color: rgb(var(--success-6));
  border-color: transparent;
}
.arco-btn-secondary.arco-btn-status-success:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):hover {
  border-color: transparent;
  color: rgb(var(--success-6));
  background-color: var(--color-success-light-2);
}
.arco-btn-secondary.arco-btn-status-success:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):active {
  border-color: transparent;
  color: rgb(var(--success-6));
  background-color: var(--color-success-light-3);
}
.arco-btn-secondary.arco-btn-status-success:not(
    .arco-btn-disabled
  ):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--success-3));
}
.arco-btn-secondary.arco-btn-status-success.arco-btn-disabled {
  color: var(--color-success-light-3);
  background-color: var(--color-success-light-1);
  border: 1px solid transparent;
}
.arco-btn-dashed:not(.arco-btn-disabled) {
  background-color: var(--color-fill-2);
  color: var(--color-text-2);
  border: 1px dashed var(--color-neutral-3);
}
.arco-btn-dashed:not(.arco-btn-disabled):not(.arco-btn-loading):hover {
  border-color: var(--color-neutral-4);
  color: var(--color-text-2);
  background-color: var(--color-fill-3);
}
.arco-btn-dashed:not(.arco-btn-disabled):not(.arco-btn-loading):active {
  border-color: var(--color-neutral-5);
  color: var(--color-text-2);
  background-color: var(--color-fill-4);
}
.arco-btn-dashed:not(.arco-btn-disabled):focus-visible {
  box-shadow: 0 0 0 2px var(--color-neutral-4);
}
.arco-btn-dashed.arco-btn-disabled {
  background-color: var(--color-fill-2);
  color: var(--color-text-4);
  border: 1px dashed var(--color-neutral-3);
  cursor: not-allowed;
}
.arco-btn-dashed.arco-btn-status-warning:not(.arco-btn-disabled) {
  background-color: var(--color-warning-light-1);
  color: rgb(var(--warning-6));
  border-color: var(--color-warning-light-2);
}
.arco-btn-dashed.arco-btn-status-warning:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):hover {
  border-color: var(--color-warning-light-3);
  color: rgb(var(--warning-6));
  background-color: var(--color-warning-light-2);
}
.arco-btn-dashed.arco-btn-status-warning:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):active {
  border-color: var(--color-warning-light-4);
  color: rgb(var(--warning-6));
  background-color: var(--color-warning-light-3);
}
.arco-btn-dashed.arco-btn-status-warning:not(.arco-btn-disabled):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--warning-3));
}
.arco-btn-dashed.arco-btn-status-warning.arco-btn-disabled {
  color: var(--color-warning-light-3);
  background-color: var(--color-warning-light-1);
  border: 1px dashed var(--color-warning-light-2);
}
.arco-btn-dashed.arco-btn-status-danger:not(.arco-btn-disabled) {
  background-color: var(--color-danger-light-1);
  color: rgb(var(--danger-6));
  border-color: var(--color-danger-light-2);
}
.arco-btn-dashed.arco-btn-status-danger:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):hover {
  border-color: var(--color-danger-light-3);
  color: rgb(var(--danger-6));
  background-color: var(--color-danger-light-2);
}
.arco-btn-dashed.arco-btn-status-danger:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):active {
  border-color: var(--color-danger-light-4);
  color: rgb(var(--danger-6));
  background-color: var(--color-danger-light-3);
}
.arco-btn-dashed.arco-btn-status-danger:not(.arco-btn-disabled):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--danger-3));
}
.arco-btn-dashed.arco-btn-status-danger.arco-btn-disabled {
  color: var(--color-danger-light-3);
  background-color: var(--color-danger-light-1);
  border: 1px dashed var(--color-danger-light-2);
}
.arco-btn-dashed.arco-btn-status-success:not(.arco-btn-disabled) {
  background-color: var(--color-success-light-1);
  color: rgb(var(--success-6));
  border-color: var(--color-success-light-2);
}
.arco-btn-dashed.arco-btn-status-success:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):hover {
  border-color: var(--color-success-light-3);
  color: rgb(var(--success-6));
  background-color: var(--color-success-light-2);
}
.arco-btn-dashed.arco-btn-status-success:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):active {
  border-color: var(--color-success-light-4);
  color: rgb(var(--success-6));
  background-color: var(--color-success-light-3);
}
.arco-btn-dashed.arco-btn-status-success:not(.arco-btn-disabled):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--success-3));
}
.arco-btn-dashed.arco-btn-status-success.arco-btn-disabled {
  color: var(--color-success-light-3);
  background-color: var(--color-success-light-1);
  border: 1px dashed var(--color-success-light-2);
}
.arco-btn-text:not(.arco-btn-disabled) {
  background-color: transparent;
  color: rgb(var(--primary-6));
  border: 1px solid transparent;
}
.arco-btn-text:not(.arco-btn-disabled):not(.arco-btn-loading):hover {
  border-color: transparent;
  color: rgb(var(--primary-6));
  background-color: var(--color-fill-2);
}
.arco-btn-text:not(.arco-btn-disabled):not(.arco-btn-loading):active {
  border-color: transparent;
  color: rgb(var(--primary-6));
  background-color: var(--color-fill-3);
}
.arco-btn-text:not(.arco-btn-disabled):focus-visible {
  box-shadow: 0 0 0 2px var(--color-neutral-4);
}
.arco-btn-text.arco-btn-disabled {
  background-color: transparent;
  color: var(--color-primary-light-3);
  border: 1px solid transparent;
  cursor: not-allowed;
}
.arco-btn-text.arco-btn-status-warning:not(.arco-btn-disabled) {
  background-color: transparent;
  color: rgb(var(--warning-6));
  border-color: transparent;
}
.arco-btn-text.arco-btn-status-warning:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):hover {
  border-color: transparent;
  color: rgb(var(--warning-6));
  background-color: var(--color-fill-2);
}
.arco-btn-text.arco-btn-status-warning:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):active {
  border-color: transparent;
  color: rgb(var(--warning-6));
  background-color: var(--color-fill-3);
}
.arco-btn-text.arco-btn-status-warning:not(.arco-btn-disabled):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--warning-3));
}
.arco-btn-text.arco-btn-status-warning.arco-btn-disabled {
  color: var(--color-warning-light-3);
  background-color: transparent;
  border: 1px solid transparent;
}
.arco-btn-text.arco-btn-status-danger:not(.arco-btn-disabled) {
  background-color: transparent;
  color: rgb(var(--danger-6));
  border-color: transparent;
}
.arco-btn-text.arco-btn-status-danger:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):hover {
  border-color: transparent;
  color: rgb(var(--danger-6));
  background-color: var(--color-fill-2);
}
.arco-btn-text.arco-btn-status-danger:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):active {
  border-color: transparent;
  color: rgb(var(--danger-6));
  background-color: var(--color-fill-3);
}
.arco-btn-text.arco-btn-status-danger:not(.arco-btn-disabled):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--danger-3));
}
.arco-btn-text.arco-btn-status-danger.arco-btn-disabled {
  color: var(--color-danger-light-3);
  background-color: transparent;
  border: 1px solid transparent;
}
.arco-btn-text.arco-btn-status-success:not(.arco-btn-disabled) {
  background-color: transparent;
  color: rgb(var(--success-6));
  border-color: transparent;
}
.arco-btn-text.arco-btn-status-success:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):hover {
  border-color: transparent;
  color: rgb(var(--success-6));
  background-color: var(--color-fill-2);
}
.arco-btn-text.arco-btn-status-success:not(.arco-btn-disabled):not(
    .arco-btn-loading
  ):active {
  border-color: transparent;
  color: rgb(var(--success-6));
  background-color: var(--color-fill-3);
}
.arco-btn-text.arco-btn-status-success:not(.arco-btn-disabled):focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--success-3));
}
.arco-btn-text.arco-btn-status-success.arco-btn-disabled {
  color: var(--color-success-light-3);
  background-color: transparent;
  border: 1px solid transparent;
}
.arco-btn-size-mini {
  padding: 0 11px;
  font-size: 12px;
  height: 24px;
  border-radius: var(--border-radius-small);
}
.arco-btn-size-mini > svg + span,
.arco-btn-size-mini > span + svg {
  margin-left: 4px;
}
.arco-btn-size-mini svg {
  vertical-align: -1px;
}
.arco-btn-size-mini.arco-btn-rtl > svg + span,
.arco-btn-size-mini.arco-btn-rtl > span + svg {
  margin-left: 0;
  margin-right: 4px;
}
.arco-btn-size-mini.arco-btn-loading-fixed-width.arco-btn-loading {
  padding-left: 3px;
  padding-right: 3px;
}
.arco-btn-size-mini.arco-btn-icon-only {
  width: 24px;
  height: 24px;
  padding: 0;
}
.arco-btn-size-mini.arco-btn-shape-circle {
  width: 24px;
  height: 24px;
  padding: 0;
  text-align: center;
  border-radius: var(--border-radius-circle);
}
.arco-btn-size-mini.arco-btn-shape-round {
  border-radius: 12px;
}
.arco-btn-group .arco-btn-size-mini:first-child {
  border-radius: var(--border-radius-small) 0 0 var(--border-radius-small);
}
.arco-btn-group .arco-btn-size-mini:last-child {
  border-radius: 0 var(--border-radius-small) var(--border-radius-small) 0;
}
.arco-btn-group .arco-btn-size-mini:first-child:last-child {
  border-radius: var(--border-radius-small);
}
.arco-btn-group .arco-btn-size-mini.arco-btn-shape-round:first-child {
  border-radius: 12px 0 0 12px;
}
.arco-btn-group .arco-btn-size-mini.arco-btn-shape-round:last-child {
  border-radius: 0 12px 12px 0;
}
.arco-btn-group
  .arco-btn-size-mini.arco-btn-shape-round:first-child:last-child {
  border-radius: 12px;
}
.arco-btn-group .arco-btn-rtl.arco-btn-size-mini:first-child {
  border-radius: 0 var(--border-radius-small) var(--border-radius-small) 0;
}
.arco-btn-group .arco-btn-rtl.arco-btn-size-mini:last-child {
  border-radius: var(--border-radius-small) 0 0 var(--border-radius-small);
}
.arco-btn-group .arco-btn-rtl.arco-btn-size-mini:first-child:last-child {
  border-radius: var(--border-radius-small);
}
.arco-btn-group
  .arco-btn-rtl.arco-btn-size-mini.arco-btn-shape-round:first-child {
  border-radius: 0 12px 12px 0;
}
.arco-btn-group
  .arco-btn-rtl.arco-btn-size-mini.arco-btn-shape-round:last-child {
  border-radius: 12px 0 0 12px;
}
.arco-btn-group
  .arco-btn-rtl.arco-btn-size-mini.arco-btn-shape-round:first-child:last-child {
  border-radius: 12px;
}
.arco-btn-size-small {
  padding: 0 15px;
  font-size: 14px;
  height: 28px;
  border-radius: var(--border-radius-small);
}
.arco-btn-size-small > svg + span,
.arco-btn-size-small > span + svg {
  margin-left: 6px;
}
.arco-btn-size-small svg {
  vertical-align: -2px;
}
.arco-btn-size-small.arco-btn-rtl > svg + span,
.arco-btn-size-small.arco-btn-rtl > span + svg {
  margin-left: 0;
  margin-right: 6px;
}
.arco-btn-size-small.arco-btn-loading-fixed-width.arco-btn-loading {
  padding-left: 5px;
  padding-right: 5px;
}
.arco-btn-size-small.arco-btn-icon-only {
  width: 28px;
  height: 28px;
  padding: 0;
}
.arco-btn-size-small.arco-btn-shape-circle {
  width: 28px;
  height: 28px;
  padding: 0;
  text-align: center;
  border-radius: var(--border-radius-circle);
}
.arco-btn-size-small.arco-btn-shape-round {
  border-radius: 14px;
}
.arco-btn-group .arco-btn-size-small:first-child {
  border-radius: var(--border-radius-small) 0 0 var(--border-radius-small);
}
.arco-btn-group .arco-btn-size-small:last-child {
  border-radius: 0 var(--border-radius-small) var(--border-radius-small) 0;
}
.arco-btn-group .arco-btn-size-small:first-child:last-child {
  border-radius: var(--border-radius-small);
}
.arco-btn-group .arco-btn-size-small.arco-btn-shape-round:first-child {
  border-radius: 14px 0 0 14px;
}
.arco-btn-group .arco-btn-size-small.arco-btn-shape-round:last-child {
  border-radius: 0 14px 14px 0;
}
.arco-btn-group
  .arco-btn-size-small.arco-btn-shape-round:first-child:last-child {
  border-radius: 14px;
}
.arco-btn-group .arco-btn-rtl.arco-btn-size-small:first-child {
  border-radius: 0 var(--border-radius-small) var(--border-radius-small) 0;
}
.arco-btn-group .arco-btn-rtl.arco-btn-size-small:last-child {
  border-radius: var(--border-radius-small) 0 0 var(--border-radius-small);
}
.arco-btn-group .arco-btn-rtl.arco-btn-size-small:first-child:last-child {
  border-radius: var(--border-radius-small);
}
.arco-btn-group
  .arco-btn-rtl.arco-btn-size-small.arco-btn-shape-round:first-child {
  border-radius: 0 14px 14px 0;
}
.arco-btn-group
  .arco-btn-rtl.arco-btn-size-small.arco-btn-shape-round:last-child {
  border-radius: 14px 0 0 14px;
}
.arco-btn-group
  .arco-btn-rtl.arco-btn-size-small.arco-btn-shape-round:first-child:last-child {
  border-radius: 14px;
}
.arco-btn-size-default {
  padding: 0 15px;
  font-size: 14px;
  height: 32px;
  border-radius: var(--border-radius-small);
}
.arco-btn-size-default > svg + span,
.arco-btn-size-default > span + svg {
  margin-left: 8px;
}
.arco-btn-size-default svg {
  vertical-align: -2px;
}
.arco-btn-size-default.arco-btn-rtl > svg + span,
.arco-btn-size-default.arco-btn-rtl > span + svg {
  margin-left: 0;
  margin-right: 8px;
}
.arco-btn-size-default.arco-btn-loading-fixed-width.arco-btn-loading {
  padding-left: 4px;
  padding-right: 4px;
}
.arco-btn-size-default.arco-btn-icon-only {
  width: 32px;
  height: 32px;
  padding: 0;
}
.arco-btn-size-default.arco-btn-shape-circle {
  width: 32px;
  height: 32px;
  padding: 0;
  text-align: center;
  border-radius: var(--border-radius-circle);
}
.arco-btn-size-default.arco-btn-shape-round {
  border-radius: 16px;
}
.arco-btn-group .arco-btn-size-default:first-child {
  border-radius: var(--border-radius-small) 0 0 var(--border-radius-small);
}
.arco-btn-group .arco-btn-size-default:last-child {
  border-radius: 0 var(--border-radius-small) var(--border-radius-small) 0;
}
.arco-btn-group .arco-btn-size-default:first-child:last-child {
  border-radius: var(--border-radius-small);
}
.arco-btn-group .arco-btn-size-default.arco-btn-shape-round:first-child {
  border-radius: 16px 0 0 16px;
}
.arco-btn-group .arco-btn-size-default.arco-btn-shape-round:last-child {
  border-radius: 0 16px 16px 0;
}
.arco-btn-group
  .arco-btn-size-default.arco-btn-shape-round:first-child:last-child {
  border-radius: 16px;
}
.arco-btn-group .arco-btn-rtl.arco-btn-size-default:first-child {
  border-radius: 0 var(--border-radius-small) var(--border-radius-small) 0;
}
.arco-btn-group .arco-btn-rtl.arco-btn-size-default:last-child {
  border-radius: var(--border-radius-small) 0 0 var(--border-radius-small);
}
.arco-btn-group .arco-btn-rtl.arco-btn-size-default:first-child:last-child {
  border-radius: var(--border-radius-small);
}
.arco-btn-group
  .arco-btn-rtl.arco-btn-size-default.arco-btn-shape-round:first-child {
  border-radius: 0 16px 16px 0;
}
.arco-btn-group
  .arco-btn-rtl.arco-btn-size-default.arco-btn-shape-round:last-child {
  border-radius: 16px 0 0 16px;
}
.arco-btn-group
  .arco-btn-rtl.arco-btn-size-default.arco-btn-shape-round:first-child:last-child {
  border-radius: 16px;
}
.arco-btn-size-large {
  padding: 0 19px;
  font-size: 14px;
  height: 36px;
  border-radius: var(--border-radius-small);
}
.arco-btn-size-large > svg + span,
.arco-btn-size-large > span + svg {
  margin-left: 8px;
}
.arco-btn-size-large svg {
  vertical-align: -2px;
}
.arco-btn-size-large.arco-btn-rtl > svg + span,
.arco-btn-size-large.arco-btn-rtl > span + svg {
  margin-left: 0;
  margin-right: 8px;
}
.arco-btn-size-large.arco-btn-loading-fixed-width.arco-btn-loading {
  padding-left: 8px;
  padding-right: 8px;
}
.arco-btn-size-large.arco-btn-icon-only {
  width: 36px;
  height: 36px;
  padding: 0;
}
.arco-btn-size-large.arco-btn-shape-circle {
  width: 36px;
  height: 36px;
  padding: 0;
  text-align: center;
  border-radius: var(--border-radius-circle);
}
.arco-btn-size-large.arco-btn-shape-round {
  border-radius: 18px;
}
.arco-btn-group .arco-btn-size-large:first-child {
  border-radius: var(--border-radius-small) 0 0 var(--border-radius-small);
}
.arco-btn-group .arco-btn-size-large:last-child {
  border-radius: 0 var(--border-radius-small) var(--border-radius-small) 0;
}
.arco-btn-group .arco-btn-size-large:first-child:last-child {
  border-radius: var(--border-radius-small);
}
.arco-btn-group .arco-btn-size-large.arco-btn-shape-round:first-child {
  border-radius: 18px 0 0 18px;
}
.arco-btn-group .arco-btn-size-large.arco-btn-shape-round:last-child {
  border-radius: 0 18px 18px 0;
}
.arco-btn-group
  .arco-btn-size-large.arco-btn-shape-round:first-child:last-child {
  border-radius: 18px;
}
.arco-btn-group .arco-btn-rtl.arco-btn-size-large:first-child {
  border-radius: 0 var(--border-radius-small) var(--border-radius-small) 0;
}
.arco-btn-group .arco-btn-rtl.arco-btn-size-large:last-child {
  border-radius: var(--border-radius-small) 0 0 var(--border-radius-small);
}
.arco-btn-group .arco-btn-rtl.arco-btn-size-large:first-child:last-child {
  border-radius: var(--border-radius-small);
}
.arco-btn-group
  .arco-btn-rtl.arco-btn-size-large.arco-btn-shape-round:first-child {
  border-radius: 0 18px 18px 0;
}
.arco-btn-group
  .arco-btn-rtl.arco-btn-size-large.arco-btn-shape-round:last-child {
  border-radius: 18px 0 0 18px;
}
.arco-btn-group
  .arco-btn-rtl.arco-btn-size-large.arco-btn-shape-round:first-child:last-child {
  border-radius: 18px;
}
.arco-btn-group {
  display: inline-block;
}
.arco-btn-group .arco-btn-outline:not(:first-child),
.arco-btn-group .arco-btn-dashed:not(:first-child) {
  margin-left: -1px;
}
.arco-btn-group .arco-btn-primary:not(:last-child) {
  border-right: 1px solid rgb(var(--primary-5));
}
.arco-btn-group .arco-btn-secondary:not(:last-child) {
  border-right: 1px solid var(--color-secondary-hover);
}
.arco-btn-group .arco-btn-text:not(:last-child) {
  border-right: 1px solid transparent;
}
.arco-btn-group .arco-btn-status-warning:not(:last-child) {
  border-right: 1px solid rgb(var(--warning-5));
}
.arco-btn-group .arco-btn-status-warning:not(:last-child).arco-btn-text {
  border-right: 1px solid var(--color-fill-2);
}
.arco-btn-group .arco-btn-status-danger:not(:last-child) {
  border-right: 1px solid rgb(var(--danger-5));
}
.arco-btn-group .arco-btn-status-danger:not(:last-child).arco-btn-text {
  border-right: 1px solid var(--color-fill-2);
}
.arco-btn-group .arco-btn-status-success:not(:last-child) {
  border-right: 1px solid rgb(var(--success-5));
}
.arco-btn-group .arco-btn-status-success:not(:last-child).arco-btn-text {
  border-right: 1px solid var(--color-fill-2);
}
.arco-btn-group .arco-btn-rtl.arco-btn-outline:not(:first-child),
.arco-btn-group .arco-btn-rtl.arco-btn-dashed:not(:first-child) {
  margin-left: 0;
  margin-right: -1px;
}
.arco-btn-group .arco-btn-rtl.arco-btn-primary:not(:last-child) {
  border-left: 1px solid rgb(var(--primary-5));
  border-right: 0;
}
.arco-btn-group .arco-btn-rtl.arco-btn-secondary:not(:last-child) {
  border-left: 1px solid var(--color-secondary-hover);
  border-right: 0;
}
.arco-btn-group .arco-btn-rtl.arco-btn-text:not(:last-child) {
  border-left: 1px solid transparent;
  border-right: 0;
}
.arco-btn-group .arco-btn-rtl.arco-btn-status-warning:not(:last-child) {
  border-left: 1px solid rgb(var(--warning-5));
  border-right: 0;
}
.arco-btn-group
  .arco-btn-rtl.arco-btn-status-warning:not(:last-child).arco-btn-text {
  border-left: 1px solid var(--color-fill-2);
}
.arco-btn-group .arco-btn-rtl.arco-btn-status-danger:not(:last-child) {
  border-left: 1px solid rgb(var(--danger-5));
  border-right: 0;
}
.arco-btn-group
  .arco-btn-rtl.arco-btn-status-danger:not(:last-child).arco-btn-text {
  border-left: 1px solid var(--color-fill-2);
}
.arco-btn-group .arco-btn-rtl.arco-btn-status-success:not(:last-child) {
  border-left: 1px solid rgb(var(--success-5));
  border-right: 0;
}
.arco-btn-group
  .arco-btn-rtl.arco-btn-status-success:not(:last-child).arco-btn-text {
  border-left: 1px solid var(--color-fill-2);
}
.arco-btn-group .arco-btn-outline:hover,
.arco-btn-group .arco-btn-dashed:hover,
.arco-btn-group .arco-btn-outline:active,
.arco-btn-group .arco-btn-dashed:active {
  z-index: 2;
}
.arco-btn-group .arco-btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.arco-btn-rtl {
  direction: rtl;
}
body[arco-theme='dark'] .arco-btn-primary.arco-btn-disabled {
  color: #ffffff4d;
}
.arco-trigger {
  position: absolute;
  z-index: 1000;
  backface-visibility: hidden;
}
.arco-trigger-arrow {
  background-color: var(--color-bg-5);
  content: '';
  height: 8px;
  width: 8px;
  position: absolute;
  display: block;
  box-sizing: border-box;
  transform: rotate(45deg);
  transform-origin: 50% 50% 0;
  z-index: -1;
}
.arco-trigger[trigger-placement='top']
  > .arco-trigger-arrow-container
  .arco-trigger-arrow,
.arco-trigger[trigger-placement='tl']
  > .arco-trigger-arrow-container
  .arco-trigger-arrow,
.arco-trigger[trigger-placement='tr']
  > .arco-trigger-arrow-container
  .arco-trigger-arrow {
  bottom: -4px;
  margin-left: -4px;
  border-top: none;
  border-left: none;
  border-bottom-right-radius: 2px;
}
.arco-trigger[trigger-placement='bottom']
  > .arco-trigger-arrow-container
  .arco-trigger-arrow,
.arco-trigger[trigger-placement='bl']
  > .arco-trigger-arrow-container
  .arco-trigger-arrow,
.arco-trigger[trigger-placement='br']
  > .arco-trigger-arrow-container
  .arco-trigger-arrow {
  top: -4px;
  margin-left: -4px;
  border-bottom: none;
  border-right: none;
  border-top-left-radius: 2px;
}
.arco-trigger[trigger-placement='left']
  > .arco-trigger-arrow-container
  .arco-trigger-arrow,
.arco-trigger[trigger-placement='lt']
  > .arco-trigger-arrow-container
  .arco-trigger-arrow,
.arco-trigger[trigger-placement='lb']
  > .arco-trigger-arrow-container
  .arco-trigger-arrow {
  right: -4px;
  margin-top: -4px;
  border-left: none;
  border-bottom: none;
  border-top-right-radius: 2px;
}
.arco-trigger[trigger-placement='right']
  > .arco-trigger-arrow-container
  .arco-trigger-arrow,
.arco-trigger[trigger-placement='rt']
  > .arco-trigger-arrow-container
  .arco-trigger-arrow,
.arco-trigger[trigger-placement='rb']
  > .arco-trigger-arrow-container
  .arco-trigger-arrow {
  left: -4px;
  margin-top: -4px;
  border-top: none;
  border-right: none;
  border-bottom-left-radius: 2px;
}
.arco-trigger-rtl {
  direction: rtl;
}
.arco-dropdown-menu {
  position: relative;
  box-sizing: border-box;
  max-height: 200px;
  padding: 4px 0;
  border: 1px solid var(--color-fill-3);
  border-radius: var(--border-radius-medium);
  background-color: var(--color-bg-popup);
  box-shadow: 0 4px 10px #0000001a;
  overflow: auto;
}
.arco-dropdown-menu-hidden {
  display: none;
}
.arco-dropdown-menu-item,
.arco-dropdown-menu-pop-header {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 36px;
  padding: 0 12px;
  font-size: 14px;
  line-height: 36px;
  text-align: left;
  cursor: pointer;
  z-index: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: var(--color-text-1);
  background-color: transparent;
}
.arco-dropdown-menu-item.arco-dropdown-menu-selected,
.arco-dropdown-menu-pop-header.arco-dropdown-menu-selected {
  color: var(--color-text-1);
  background-color: transparent;
  font-weight: 500;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-dropdown-menu-item:hover,
.arco-dropdown-menu-pop-header:hover {
  color: var(--color-text-1);
  background-color: var(--color-fill-2);
}
.arco-dropdown-menu-item:focus-visible,
.arco-dropdown-menu-pop-header:focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--primary-6)) inset;
}
.arco-dropdown-menu-item.arco-dropdown-menu-active,
.arco-dropdown-menu-pop-header.arco-dropdown-menu-active {
  box-shadow: 0 0 0 1px rgb(var(--primary-6)) inset;
}
.arco-dropdown-menu-item.arco-dropdown-menu-disabled,
.arco-dropdown-menu-pop-header.arco-dropdown-menu-disabled {
  color: var(--color-text-4);
  background-color: transparent;
  cursor: not-allowed;
}
.arco-dropdown-menu-item a,
.arco-dropdown-menu-pop-header a,
.arco-dropdown-menu-item a:hover,
.arco-dropdown-menu-pop-header a:hover,
.arco-dropdown-menu-item a:focus,
.arco-dropdown-menu-pop-header a:focus,
.arco-dropdown-menu-item a:active,
.arco-dropdown-menu-pop-header a:active {
  color: inherit;
  cursor: inherit;
  text-decoration: none;
}
.arco-dropdown-menu-item > a:only-child:before,
.arco-dropdown-menu-pop-header > a:only-child:before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.arco-dropdown-menu-pop-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.arco-dropdown-menu-pop-header .arco-dropdown-menu-icon-suffix {
  margin-left: 12px;
}
.arco-dropdown-menu-group:first-child .arco-dropdown-menu-group-title {
  margin-top: 4px;
}
.arco-dropdown-menu-group-title {
  margin-top: 8px;
  box-sizing: border-box;
  width: 100%;
  padding: 0 12px;
  line-height: 20px;
  font-size: 12px;
  color: var(--color-text-3);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-dropdown-menu-dark {
  border-color: var(--color-menu-dark-bg);
  background-color: var(--color-menu-dark-bg);
}
.arco-dropdown-menu-dark .arco-dropdown-menu-item,
.arco-dropdown-menu-dark .arco-dropdown-menu-pop-header {
  color: var(--color-text-4);
  background-color: transparent;
}
.arco-dropdown-menu-dark .arco-dropdown-menu-item.arco-dropdown-menu-selected,
.arco-dropdown-menu-dark
  .arco-dropdown-menu-pop-header.arco-dropdown-menu-selected {
  color: var(--color-white);
  background-color: transparent;
}
.arco-dropdown-menu-dark
  .arco-dropdown-menu-item.arco-dropdown-menu-selected:hover,
.arco-dropdown-menu-dark
  .arco-dropdown-menu-pop-header.arco-dropdown-menu-selected:hover {
  color: var(--color-white);
}
.arco-dropdown-menu-dark .arco-dropdown-menu-item:hover,
.arco-dropdown-menu-dark .arco-dropdown-menu-pop-header:hover {
  color: var(--color-text-4);
  background-color: var(--color-menu-dark-hover);
}
.arco-dropdown-menu-dark .arco-dropdown-menu-item.arco-dropdown-menu-disabled,
.arco-dropdown-menu-dark
  .arco-dropdown-menu-pop-header.arco-dropdown-menu-disabled {
  color: var(--color-text-2);
  background-color: transparent;
}
.arco-dropdown-menu-dark .arco-dropdown-menu-group-title {
  color: var(--color-text-3);
}
.arco-dropdown-menu-pop-trigger .arco-trigger-arrow {
  display: none;
}
.arco-dropdown-menu + .arco-trigger-arrow {
  background-color: var(--color-bg-popup);
}
.arco-dropdown-menu-rtl .arco-dropdown-menu-item,
.arco-dropdown-menu-rtl .arco-dropdown-menu-pop-header {
  text-align: right;
}
.arco-dropdown-menu-rtl
  .arco-dropdown-menu-item
  .arco-dropdown-menu-icon-suffix,
.arco-dropdown-menu-rtl
  .arco-dropdown-menu-pop-header
  .arco-dropdown-menu-icon-suffix {
  margin-left: 0;
  margin-right: 12px;
}
.arco-tooltip-content {
  padding: 8px 12px;
  background-color: var(--color-tooltip-bg);
  font-size: 14px;
  border-radius: var(--border-radius-small);
  color: #fff;
  line-height: 1.5715;
  box-shadow: 0 4px 10px #0000001a;
}
.arco-tooltip-content-inner {
  word-wrap: break-word;
  text-align: start;
}
.arco-tooltip-mini {
  font-size: 14px;
  padding: 4px 12px;
}
.arco-trigger-arrow.arco-tooltip-arrow {
  background-color: var(--color-tooltip-bg);
}
body[arco-theme='dark'] .arco-tooltip-content {
  border: 1px solid var(--color-neutral-3);
}
body[arco-theme='dark'] .arco-tooltip .arco-trigger-arrow.arco-tooltip-arrow {
  z-index: 1;
}
body[arco-theme='dark']
  .arco-trigger[trigger-placement='top']
  .arco-trigger-arrow.arco-tooltip-arrow,
body[arco-theme='dark']
  .arco-trigger[trigger-placement='tl']
  .arco-trigger-arrow.arco-tooltip-arrow,
body[arco-theme='dark']
  .arco-trigger[trigger-placement='tr']
  .arco-trigger-arrow.arco-tooltip-arrow {
  border-bottom: 1px solid var(--color-neutral-3);
  border-right: 1px solid var(--color-neutral-3);
}
body[arco-theme='dark']
  .arco-trigger[trigger-placement='bottom']
  .arco-trigger-arrow.arco-tooltip-arrow,
body[arco-theme='dark']
  .arco-trigger[trigger-placement='bl']
  .arco-trigger-arrow.arco-tooltip-arrow,
body[arco-theme='dark']
  .arco-trigger[trigger-placement='br']
  .arco-trigger-arrow.arco-tooltip-arrow {
  border-top: 1px solid var(--color-neutral-3);
  border-left: 1px solid var(--color-neutral-3);
}
body[arco-theme='dark']
  .arco-trigger[trigger-placement='left']
  .arco-trigger-arrow.arco-tooltip-arrow,
body[arco-theme='dark']
  .arco-trigger[trigger-placement='lt']
  .arco-trigger-arrow.arco-tooltip-arrow,
body[arco-theme='dark']
  .arco-trigger[trigger-placement='lb']
  .arco-trigger-arrow.arco-tooltip-arrow {
  border-top: 1px solid var(--color-neutral-3);
  border-right: 1px solid var(--color-neutral-3);
}
body[arco-theme='dark']
  .arco-trigger[trigger-placement='right']
  .arco-trigger-arrow.arco-tooltip-arrow,
body[arco-theme='dark']
  .arco-trigger[trigger-placement='rt']
  .arco-trigger-arrow.arco-tooltip-arrow,
body[arco-theme='dark']
  .arco-trigger[trigger-placement='rb']
  .arco-trigger-arrow.arco-tooltip-arrow {
  border-left: 1px solid var(--color-neutral-3);
  border-bottom: 1px solid var(--color-neutral-3);
}
.arco-resizebox {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.arco-resizebox-direction-left,
.arco-resizebox-direction-right,
.arco-resizebox-direction-top,
.arco-resizebox-direction-bottom {
  position: absolute;
  left: 0;
  top: 0;
  user-select: none;
  box-sizing: border-box;
}
.arco-resizebox-direction-right {
  left: unset;
  right: 0;
}
.arco-resizebox-direction-bottom {
  top: unset;
  bottom: 0;
}
.arco-resizebox-split,
.arco-resizebox-split-group {
  display: flex;
  user-select: auto;
}
.arco-resizebox-split .arco-resizebox-slit-trigger,
.arco-resizebox-split-group .arco-resizebox-slit-trigger {
  flex: 0;
}
.arco-resizebox-split-pane,
.arco-resizebox-split-group-pane {
  overflow: auto;
}
.arco-resizebox-split .second-pane,
.arco-resizebox-split-group .second-pane {
  flex: 1;
}
.arco-resizebox-split-horizontal,
.arco-resizebox-split-group-horizontal {
  flex-direction: row;
}
.arco-resizebox-split-vertical,
.arco-resizebox-split-group-vertical {
  flex-direction: column;
}
.arco-resizebox-split-moving,
.arco-resizebox-split-group-moving {
  user-select: none;
}
.arco-resizebox-trigger-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: var(--color-neutral-3);
}
.arco-resizebox-trigger-icon {
  display: inline-block;
  color: var(--color-text-1);
  font-size: 12px;
}
.arco-resizebox-trigger-prev > svg,
.arco-resizebox-trigger-next > svg {
  cursor: pointer;
}
.arco-resizebox-trigger-vertical {
  height: 100%;
  cursor: col-resize;
}
.arco-resizebox-trigger-vertical.arco-resizebox-trigger-not-resizable {
  cursor: default;
}
.arco-resizebox-trigger-vertical .arco-resizebox-trigger-prev,
.arco-resizebox-trigger-vertical .arco-resizebox-trigger-next {
  height: 18px;
  line-height: 18px;
}
.arco-resizebox-trigger-vertical .arco-resizebox-trigger-icon-wrapper {
  width: 6px;
  flex-direction: column;
}
.arco-resizebox-trigger-vertical .arco-resizebox-trigger-icon-empty {
  height: 18px;
  width: 100%;
}
.arco-resizebox-trigger-horizontal {
  width: 100%;
  cursor: row-resize;
}
.arco-resizebox-trigger-horizontal.arco-resizebox-trigger-not-resizable {
  cursor: default;
}
.arco-resizebox-trigger-horizontal .arco-resizebox-trigger-prev,
.arco-resizebox-trigger-horizontal .arco-resizebox-trigger-next {
  width: 18px;
  text-align: center;
}
.arco-resizebox-trigger-horizontal .arco-resizebox-trigger-icon-wrapper {
  height: 6px;
}
.arco-resizebox-trigger-horizontal
  .arco-resizebox-trigger-icon-wrapper
  .arco-icon {
  vertical-align: -1px;
}
.arco-resizebox-trigger-horizontal .arco-resizebox-trigger-icon-empty {
  width: 18px;
  height: 100%;
}
.arco-resizebox-rtl,
.arco-resizebox-split-rtl,
.arco-resizebox-split-group-rtl,
.arco-resizebox-trigger-rtl {
  direction: rtl;
}
.arco-affix {
  position: fixed;
  z-index: 999;
}
.arco-layout {
  display: flex;
  flex: 1;
  margin: 0;
  padding: 0;
  flex-direction: column;
}
.arco-layout-sider {
  position: relative;
  width: auto;
  margin: 0;
  padding: 0;
  flex: none;
  background: var(--color-menu-dark-bg);
  transition: width 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-layout-sider-children {
  height: 100%;
  overflow: auto;
}
.arco-layout-sider-collapsed .arco-layout-sider-children::-webkit-scrollbar {
  width: 0;
}
.arco-layout-sider-has-trigger {
  padding-bottom: 48px;
  box-sizing: border-box;
}
.arco-layout-sider-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 100%;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  color: var(--color-white);
  cursor: pointer;
  transition: width 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
  z-index: 1;
}
.arco-layout-sider-trigger-light {
  background: var(--color-menu-light-bg);
  border-top: 1px solid var(--color-bg-5);
  color: var(--color-text-1);
}
.arco-layout-sider-light {
  background: var(--color-menu-light-bg);
  box-shadow: 0 2px 5px #00000014;
}
.arco-layout-header {
  margin: 0;
  flex: 0 0 auto;
  box-sizing: border-box;
}
.arco-layout-content {
  flex: 1;
}
.arco-layout-footer {
  flex: 0 0 auto;
  margin: 0;
}
.arco-layout-has-sider {
  flex-direction: row;
}
.arco-layout-has-sider > .arco-layout,
.arco-layout-has-sider > .arco-layout-content {
  overflow-x: hidden;
}
.arco-message-wrapper {
  width: 100%;
  position: fixed;
  z-index: 1003;
  padding: 0 10px;
  text-align: center;
  pointer-events: none;
  box-sizing: border-box;
  left: 0;
}
.arco-message-wrapper-top {
  top: 40px;
}
.arco-message-wrapper-bottom {
  bottom: 40px;
}
.arco-message {
  position: relative;
  display: inline-block;
  padding: 10px 16px;
  line-height: 1;
  border-radius: var(--border-radius-small);
  border: 1px solid var(--color-neutral-3);
  margin-bottom: 16px;
  background-color: var(--color-bg-popup);
  text-align: center;
  box-shadow: 0 4px 10px #0000001a;
  overflow: hidden;
  pointer-events: auto;
  transition: opacity 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-message-closable {
  padding-right: 38px;
}
.arco-message-icon {
  font-size: 20px;
  color: var(--color-text-1);
  margin-right: 8px;
  vertical-align: middle;
  display: inline-block;
  animation: arco-msg-fade 0.1s cubic-bezier(0, 0, 1, 1),
    arco-msg-fade 0.4s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.arco-message-content {
  vertical-align: middle;
  color: var(--color-text-1);
  font-size: 14px;
}
.arco-message-info {
  background-color: var(--color-bg-popup);
  border-color: var(--color-neutral-3);
}
.arco-message-info .arco-message-icon {
  color: rgb(var(--primary-6));
}
.arco-message-info .arco-message-content {
  color: var(--color-text-1);
}
.arco-message-success {
  background-color: var(--color-bg-popup);
  border-color: var(--color-neutral-3);
}
.arco-message-success .arco-message-icon {
  color: rgb(var(--success-6));
}
.arco-message-success .arco-message-content {
  color: var(--color-text-1);
}
.arco-message-warning {
  background-color: var(--color-bg-popup);
  border-color: var(--color-neutral-3);
}
.arco-message-warning .arco-message-icon {
  color: rgb(var(--warning-6));
}
.arco-message-warning .arco-message-content {
  color: var(--color-text-1);
}
.arco-message-error {
  background-color: var(--color-bg-popup);
  border-color: var(--color-neutral-3);
}
.arco-message-error .arco-message-icon {
  color: rgb(var(--danger-6));
}
.arco-message-error .arco-message-content {
  color: var(--color-text-1);
}
.arco-message-loading {
  background-color: var(--color-bg-popup);
  border-color: var(--color-neutral-3);
}
.arco-message-loading .arco-message-icon {
  color: rgb(var(--primary-6));
}
.arco-message-loading .arco-message-content {
  color: var(--color-text-1);
}
.arco-message-close-btn {
  position: absolute;
  top: 14px;
  right: 12px;
  color: var(--color-text-1);
  font-size: 12px;
}
.arco-message-close-btn > svg {
  position: relative;
}
.arco-message .arco-icon-hover.arco-message-icon-hover:before {
  width: 20px;
  height: 20px;
}
.fadeMessage-enter,
.fadeMessage-appear {
  opacity: 0;
}
.fadeMessage-enter-active,
.fadeMessage-appear-active {
  opacity: 1;
  transition: opacity 0.1s cubic-bezier(0, 0, 1, 1);
}
.fadeMessage-exit {
  opacity: 0;
  overflow: hidden;
}
.fadeMessage-exit-active {
  opacity: 0;
  height: 0;
  transition: all 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-message-rtl {
  direction: rtl;
}
.arco-message-rtl .arco-message-icon {
  margin-right: 0;
  margin-left: 8px;
}
@keyframes arco-msg-fade {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes arco-msg-scale {
  0% {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
.arco-divider-horizontal {
  position: relative;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin: 20px 0;
  border-bottom: 1px solid var(--color-neutral-3);
  clear: both;
}
.arco-divider-horizontal.arco-divider-with-text {
  margin: 20px 0;
  display: flex;
  align-items: center;
  border-bottom-width: 0;
  border-bottom-style: solid;
}
.arco-divider-horizontal.arco-divider-with-text:before,
.arco-divider-horizontal.arco-divider-with-text:after {
  content: '';
  height: 0;
  flex: 1;
  border-bottom: 1px;
  border-bottom-style: inherit;
  border-bottom-color: inherit;
}
.arco-divider-horizontal.arco-divider-with-text-left:before {
  flex-basis: 24px;
  flex-grow: 0;
}
.arco-divider-horizontal.arco-divider-with-text-right:after {
  flex-basis: 24px;
  flex-grow: 0;
}
.arco-divider-vertical {
  display: inline-block;
  min-width: 1px;
  max-width: 1px;
  height: 1em;
  margin: 0 12px;
  border-left: 1px solid var(--color-neutral-3);
  vertical-align: middle;
}
.arco-divider-text {
  box-sizing: border-box;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 500;
  line-height: 2;
  color: var(--color-text-1);
}
.arco-empty {
  width: 100%;
  padding: 10px 0;
  box-sizing: border-box;
}
.arco-empty .arco-empty-wrapper {
  width: 100%;
  box-sizing: border-box;
  text-align: center;
  color: rgb(var(--gray-5));
}
.arco-empty .arco-empty-wrapper .arco-empty-image {
  font-size: 48px;
  line-height: 1;
  margin-bottom: 4px;
}
.arco-empty .arco-empty-wrapper img {
  height: 80px;
}
.arco-empty .arco-empty-description {
  color: rgb(var(--gray-5));
  font-size: 14px;
}
.arco-icon-hover.arco-checkbox-icon-hover:before {
  width: 24px;
  height: 24px;
}
.arco-checkbox {
  display: inline-block;
  cursor: pointer;
  box-sizing: border-box;
  font-size: 14px;
  padding-left: 5px;
  line-height: unset;
  position: relative;
}
.arco-checkbox > input[type='checkbox'] {
  position: absolute;
  opacity: 0;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
}
.arco-checkbox
  > input[type='checkbox']:focus-visible
  + .arco-checkbox-icon-hover:before {
  background-color: var(--color-fill-2);
  opacity: 1;
}
.arco-checkbox:hover .arco-checkbox-icon-hover:before {
  background-color: var(--color-fill-2);
}
.arco-checkbox-text {
  color: var(--color-text-1);
  margin-left: 8px;
}
.arco-checkbox-mask-wrapper {
  vertical-align: middle;
  top: -0.09em;
  position: relative;
  line-height: 1;
}
.arco-checkbox-mask {
  position: relative;
  box-sizing: border-box;
  width: 14px;
  height: 14px;
  border: 2px solid var(--color-fill-3);
  border-radius: var(--border-radius-small);
  background-color: var(--color-bg-2);
  user-select: none;
}
.arco-checkbox-mask:after {
  content: '';
  display: block;
  height: 2px;
  width: 6px;
  background: var(--color-white);
  top: 50%;
  left: 50%;
  transform: translate(-50%) translateY(-50%) scale(0);
  position: absolute;
  border-radius: 0.5px;
}
.arco-checkbox-mask-icon {
  position: relative;
  height: 100%;
  transform: scale(0);
  color: var(--color-white);
  transform-origin: center 75%;
  margin: 0 auto;
  display: block;
  width: 8px;
}
.arco-checkbox:hover .arco-checkbox-mask {
  border-color: var(--color-fill-4);
  transition: border-color 0.1s cubic-bezier(0, 0, 1, 1),
    transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.arco-checkbox-checked:hover .arco-checkbox-mask,
.arco-checkbox-indeterminate:hover .arco-checkbox-mask {
  transition: transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.arco-checkbox-checked .arco-checkbox-mask {
  border-color: transparent;
  background-color: rgb(var(--primary-6));
}
.arco-checkbox-checked .arco-checkbox-mask-icon {
  transform: scale(1);
  transition: transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.arco-checkbox-indeterminate .arco-checkbox-mask {
  border-color: transparent;
  background-color: rgb(var(--primary-6));
}
.arco-checkbox-indeterminate .arco-checkbox-mask-icon {
  transform: scale(0);
}
.arco-checkbox-indeterminate .arco-checkbox-mask:after {
  transform: translate(-50%) translateY(-50%) scale(1);
  transition: transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.arco-checkbox.arco-checkbox-disabled,
.arco-checkbox.arco-checkbox-disabled .arco-checkbox-icon-hover {
  cursor: not-allowed;
}
.arco-checkbox.arco-checkbox-disabled:hover .arco-checkbox-mask {
  border-color: var(--color-fill-3);
}
.arco-checkbox-checked:hover .arco-checkbox-mask,
.arco-checkbox-indeterminate:hover .arco-checkbox-mask {
  border-color: transparent;
}
.arco-checkbox-disabled .arco-checkbox-mask {
  border-color: var(--color-fill-3);
  background-color: var(--color-fill-2);
}
.arco-checkbox-disabled.arco-checkbox-checked .arco-checkbox-mask,
.arco-checkbox-disabled.arco-checkbox-checked:hover .arco-checkbox-mask,
.arco-checkbox-disabled.arco-checkbox-indeterminate .arco-checkbox-mask,
.arco-checkbox-disabled.arco-checkbox-indeterminate:hover .arco-checkbox-mask {
  border-color: transparent;
  background-color: var(--color-primary-light-3);
}
.arco-checkbox-disabled:hover .arco-checkbox-mask-wrapper:before,
.arco-checkbox-checked:hover .arco-checkbox-mask-wrapper:before,
.arco-checkbox-indeterminate:hover .arco-checkbox-mask-wrapper:before {
  background-color: transparent;
}
.arco-checkbox-disabled .arco-checkbox-text {
  color: var(--color-text-4);
}
.arco-checkbox-disabled .arco-checkbox-mask-icon {
  color: var(--color-fill-3);
}
.arco-checkbox-group {
  display: inline-block;
}
.arco-checkbox-group .arco-checkbox {
  margin-right: 16px;
}
.arco-checkbox-group-direction-vertical .arco-checkbox {
  display: block;
  margin-right: 0;
  line-height: 32px;
}
.arco-checkbox-rtl {
  direction: rtl;
  padding-left: 0;
  padding-right: 5px;
}
.arco-checkbox-rtl .arco-checkbox-text {
  margin-left: 0;
  margin-right: 8px;
}
.arco-checkbox-group-rtl .arco-checkbox {
  margin-right: 0;
  margin-left: 16px;
}
.arco-overflow {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  overflow: hidden;
  max-width: 100%;
  justify-content: flex-start;
  align-items: center;
}
.arco-overflow-item {
  display: inline-flex;
  max-width: 100%;
}
.arco-overflow-item-hidden {
  position: absolute;
  z-index: -1;
  opacity: 0;
}
.arco-tag {
  display: inline-flex;
  align-items: center;
  box-sizing: border-box;
  height: 24px;
  padding: 0 8px;
  border: 1px solid transparent;
  border-radius: var(--border-radius-small);
  font-size: 12px;
  font-weight: 500;
  line-height: 22px;
  color: var(--color-text-1);
}
.arco-tag .arco-icon-hover.arco-tag-icon-hover:before {
  width: 16px;
  height: 16px;
}
.arco-tag .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: var(--color-fill-3);
}
.arco-tag-content {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-tag-checkable {
  cursor: pointer;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-tag-checkable:hover {
  background-color: var(--color-fill-2);
}
.arco-tag-checked {
  border-color: transparent;
  background-color: var(--color-fill-2);
}
.arco-tag-checkable.arco-tag-checked:hover {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-tag-bordered,
.arco-tag-checkable.arco-tag-checked.arco-tag-bordered:hover {
  border-color: var(--color-border-2);
}
.arco-tag-size-small {
  height: 20px;
  line-height: 18px;
  font-size: 12px;
}
.arco-tag-size-medium {
  height: 28px;
  line-height: 26px;
  font-size: 14px;
}
.arco-tag-size-large {
  height: 32px;
  line-height: 30px;
  font-size: 14px;
}
.arco-tag-hide {
  display: none;
}
.arco-tag-loading {
  opacity: 0.8;
  cursor: default;
}
.arco-tag-icon {
  margin-right: 4px;
  color: var(--color-text-2);
}
.arco-tag-checked.arco-tag-red {
  color: rgb(var(--red-6));
  background-color: rgb(var(--red-1));
  border: 1px solid transparent;
}
.arco-tag-checked.arco-tag-red
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgb(var(--red-2));
}
.arco-tag-checkable.arco-tag-checked.arco-tag-red:hover {
  background-color: rgb(var(--red-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-red.arco-tag-bordered,
.arco-tag-checked.arco-tag-red.arco-tag-bordered:hover {
  border-color: rgb(var(--red-6));
}
.arco-tag-checked.arco-tag-red .arco-tag-icon,
.arco-tag-checked.arco-tag-red .arco-tag-close-icon,
.arco-tag-checked.arco-tag-red .arco-tag-loading-icon {
  color: rgb(var(--red-6));
}
.arco-tag-checked.arco-tag-orangered {
  color: rgb(var(--orangered-6));
  background-color: rgb(var(--orangered-1));
  border: 1px solid transparent;
}
.arco-tag-checked.arco-tag-orangered
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgb(var(--orangered-2));
}
.arco-tag-checkable.arco-tag-checked.arco-tag-orangered:hover {
  background-color: rgb(var(--orangered-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-orangered.arco-tag-bordered,
.arco-tag-checked.arco-tag-orangered.arco-tag-bordered:hover {
  border-color: rgb(var(--orangered-6));
}
.arco-tag-checked.arco-tag-orangered .arco-tag-icon,
.arco-tag-checked.arco-tag-orangered .arco-tag-close-icon,
.arco-tag-checked.arco-tag-orangered .arco-tag-loading-icon {
  color: rgb(var(--orangered-6));
}
.arco-tag-checked.arco-tag-orange {
  color: rgb(var(--orange-6));
  background-color: rgb(var(--orange-1));
  border: 1px solid transparent;
}
.arco-tag-checked.arco-tag-orange
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgb(var(--orange-2));
}
.arco-tag-checkable.arco-tag-checked.arco-tag-orange:hover {
  background-color: rgb(var(--orange-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-orange.arco-tag-bordered,
.arco-tag-checked.arco-tag-orange.arco-tag-bordered:hover {
  border-color: rgb(var(--orange-6));
}
.arco-tag-checked.arco-tag-orange .arco-tag-icon,
.arco-tag-checked.arco-tag-orange .arco-tag-close-icon,
.arco-tag-checked.arco-tag-orange .arco-tag-loading-icon {
  color: rgb(var(--orange-6));
}
.arco-tag-checked.arco-tag-gold {
  color: rgb(var(--gold-6));
  background-color: rgb(var(--gold-1));
  border: 1px solid transparent;
}
.arco-tag-checked.arco-tag-gold
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgb(var(--gold-2));
}
.arco-tag-checkable.arco-tag-checked.arco-tag-gold:hover {
  background-color: rgb(var(--gold-3));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-gold.arco-tag-bordered,
.arco-tag-checked.arco-tag-gold.arco-tag-bordered:hover {
  border-color: rgb(var(--gold-6));
}
.arco-tag-checked.arco-tag-gold .arco-tag-icon,
.arco-tag-checked.arco-tag-gold .arco-tag-close-icon,
.arco-tag-checked.arco-tag-gold .arco-tag-loading-icon {
  color: rgb(var(--gold-6));
}
.arco-tag-checked.arco-tag-lime {
  color: rgb(var(--lime-6));
  background-color: rgb(var(--lime-1));
  border: 1px solid transparent;
}
.arco-tag-checked.arco-tag-lime
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgb(var(--lime-2));
}
.arco-tag-checkable.arco-tag-checked.arco-tag-lime:hover {
  background-color: rgb(var(--lime-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-lime.arco-tag-bordered,
.arco-tag-checked.arco-tag-lime.arco-tag-bordered:hover {
  border-color: rgb(var(--lime-6));
}
.arco-tag-checked.arco-tag-lime .arco-tag-icon,
.arco-tag-checked.arco-tag-lime .arco-tag-close-icon,
.arco-tag-checked.arco-tag-lime .arco-tag-loading-icon {
  color: rgb(var(--lime-6));
}
.arco-tag-checked.arco-tag-green {
  color: rgb(var(--green-6));
  background-color: rgb(var(--green-1));
  border: 1px solid transparent;
}
.arco-tag-checked.arco-tag-green
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgb(var(--green-2));
}
.arco-tag-checkable.arco-tag-checked.arco-tag-green:hover {
  background-color: rgb(var(--green-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-green.arco-tag-bordered,
.arco-tag-checked.arco-tag-green.arco-tag-bordered:hover {
  border-color: rgb(var(--green-6));
}
.arco-tag-checked.arco-tag-green .arco-tag-icon,
.arco-tag-checked.arco-tag-green .arco-tag-close-icon,
.arco-tag-checked.arco-tag-green .arco-tag-loading-icon {
  color: rgb(var(--green-6));
}
.arco-tag-checked.arco-tag-cyan {
  color: rgb(var(--cyan-6));
  background-color: rgb(var(--cyan-1));
  border: 1px solid transparent;
}
.arco-tag-checked.arco-tag-cyan
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgb(var(--cyan-2));
}
.arco-tag-checkable.arco-tag-checked.arco-tag-cyan:hover {
  background-color: rgb(var(--cyan-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-cyan.arco-tag-bordered,
.arco-tag-checked.arco-tag-cyan.arco-tag-bordered:hover {
  border-color: rgb(var(--cyan-6));
}
.arco-tag-checked.arco-tag-cyan .arco-tag-icon,
.arco-tag-checked.arco-tag-cyan .arco-tag-close-icon,
.arco-tag-checked.arco-tag-cyan .arco-tag-loading-icon {
  color: rgb(var(--cyan-6));
}
.arco-tag-checked.arco-tag-blue {
  color: rgb(var(--blue-6));
  background-color: rgb(var(--blue-1));
  border: 1px solid transparent;
}
.arco-tag-checked.arco-tag-blue
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgb(var(--blue-2));
}
.arco-tag-checkable.arco-tag-checked.arco-tag-blue:hover {
  background-color: rgb(var(--blue-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-blue.arco-tag-bordered,
.arco-tag-checked.arco-tag-blue.arco-tag-bordered:hover {
  border-color: rgb(var(--blue-6));
}
.arco-tag-checked.arco-tag-blue .arco-tag-icon,
.arco-tag-checked.arco-tag-blue .arco-tag-close-icon,
.arco-tag-checked.arco-tag-blue .arco-tag-loading-icon {
  color: rgb(var(--blue-6));
}
.arco-tag-checked.arco-tag-arcoblue {
  color: rgb(var(--arcoblue-6));
  background-color: rgb(var(--arcoblue-1));
  border: 1px solid transparent;
}
.arco-tag-checked.arco-tag-arcoblue
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgb(var(--arcoblue-2));
}
.arco-tag-checkable.arco-tag-checked.arco-tag-arcoblue:hover {
  background-color: rgb(var(--arcoblue-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-arcoblue.arco-tag-bordered,
.arco-tag-checked.arco-tag-arcoblue.arco-tag-bordered:hover {
  border-color: rgb(var(--arcoblue-6));
}
.arco-tag-checked.arco-tag-arcoblue .arco-tag-icon,
.arco-tag-checked.arco-tag-arcoblue .arco-tag-close-icon,
.arco-tag-checked.arco-tag-arcoblue .arco-tag-loading-icon {
  color: rgb(var(--arcoblue-6));
}
.arco-tag-checked.arco-tag-purple {
  color: rgb(var(--purple-6));
  background-color: rgb(var(--purple-1));
  border: 1px solid transparent;
}
.arco-tag-checked.arco-tag-purple
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgb(var(--purple-2));
}
.arco-tag-checkable.arco-tag-checked.arco-tag-purple:hover {
  background-color: rgb(var(--purple-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-purple.arco-tag-bordered,
.arco-tag-checked.arco-tag-purple.arco-tag-bordered:hover {
  border-color: rgb(var(--purple-6));
}
.arco-tag-checked.arco-tag-purple .arco-tag-icon,
.arco-tag-checked.arco-tag-purple .arco-tag-close-icon,
.arco-tag-checked.arco-tag-purple .arco-tag-loading-icon {
  color: rgb(var(--purple-6));
}
.arco-tag-checked.arco-tag-pinkpurple {
  color: rgb(var(--pinkpurple-6));
  background-color: rgb(var(--pinkpurple-1));
  border: 1px solid transparent;
}
.arco-tag-checked.arco-tag-pinkpurple
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgb(var(--pinkpurple-2));
}
.arco-tag-checkable.arco-tag-checked.arco-tag-pinkpurple:hover {
  background-color: rgb(var(--pinkpurple-2));
  border-color: rgb(var(--pinkpurple-2));
}
.arco-tag-checked.arco-tag-pinkpurple.arco-tag-bordered,
.arco-tag-checked.arco-tag-pinkpurple.arco-tag-bordered:hover {
  border-color: rgb(var(--pinkpurple-6));
}
.arco-tag-checked.arco-tag-pinkpurple .arco-tag-icon,
.arco-tag-checked.arco-tag-pinkpurple .arco-tag-close-icon,
.arco-tag-checked.arco-tag-pinkpurple .arco-tag-loading-icon {
  color: transparent;
}
.arco-tag-checked.arco-tag-magenta {
  color: rgb(var(--magenta-6));
  background-color: rgb(var(--magenta-1));
  border: 1px solid transparent;
}
.arco-tag-checked.arco-tag-magenta
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgb(var(--magenta-2));
}
.arco-tag-checkable.arco-tag-checked.arco-tag-magenta:hover {
  background-color: rgb(var(--magenta-2));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-magenta.arco-tag-bordered,
.arco-tag-checked.arco-tag-magenta.arco-tag-bordered:hover {
  border-color: rgb(var(--magenta-6));
}
.arco-tag-checked.arco-tag-magenta .arco-tag-icon,
.arco-tag-checked.arco-tag-magenta .arco-tag-close-icon,
.arco-tag-checked.arco-tag-magenta .arco-tag-loading-icon {
  color: rgb(var(--magenta-6));
}
.arco-tag-checked.arco-tag-gray {
  color: rgb(var(--gray-6));
  background-color: rgb(var(--gray-2));
  border: 1px solid transparent;
}
.arco-tag-checked.arco-tag-gray
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgb(var(--gray-3));
}
.arco-tag-checkable.arco-tag-checked.arco-tag-gray:hover {
  background-color: rgb(var(--gray-3));
  border-color: transparent;
}
.arco-tag-checked.arco-tag-gray.arco-tag-bordered,
.arco-tag-checked.arco-tag-gray.arco-tag-bordered:hover {
  border-color: rgb(var(--gray-6));
}
.arco-tag-checked.arco-tag-gray .arco-tag-icon,
.arco-tag-checked.arco-tag-gray .arco-tag-close-icon,
.arco-tag-checked.arco-tag-gray .arco-tag-loading-icon {
  color: rgb(var(--gray-6));
}
.arco-tag-custom-color {
  color: var(--color-white);
}
.arco-tag-custom-color .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: #fff3;
}
.arco-tag .arco-tag-close-btn {
  font-size: 12px;
  margin-left: 4px;
}
.arco-tag .arco-tag-close-btn:focus-visible:before {
  box-shadow: inset 0 0 0 2px var(--color-primary-light-3);
}
.arco-tag .arco-tag-close-btn > svg {
  position: relative;
}
.arco-tag-loading-icon {
  font-size: 12px;
  margin-left: 4px;
}
body[arco-theme='dark'] .arco-tag-checked {
  color: #ffffffe6;
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-red {
  background-color: rgba(var(--red-6), 0.2);
}
body[arco-theme='dark']
  .arco-tag-checked.arco-tag-red
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgba(var(--red-6), 0.35);
}
body[arco-theme='dark']
  .arco-tag-checkable.arco-tag-checked.arco-tag-red:hover {
  background-color: rgba(var(--red-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-orangered {
  background-color: rgba(var(--orangered-6), 0.2);
}
body[arco-theme='dark']
  .arco-tag-checked.arco-tag-orangered
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgba(var(--orangered-6), 0.35);
}
body[arco-theme='dark']
  .arco-tag-checkable.arco-tag-checked.arco-tag-orangered:hover {
  background-color: rgba(var(--orangered-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-orange {
  background-color: rgba(var(--orange-6), 0.2);
}
body[arco-theme='dark']
  .arco-tag-checked.arco-tag-orange
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgba(var(--orange-6), 0.35);
}
body[arco-theme='dark']
  .arco-tag-checkable.arco-tag-checked.arco-tag-orange:hover {
  background-color: rgba(var(--orange-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-gold {
  background-color: rgba(var(--gold-6), 0.2);
}
body[arco-theme='dark']
  .arco-tag-checked.arco-tag-gold
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgba(var(--gold-6), 0.35);
}
body[arco-theme='dark']
  .arco-tag-checkable.arco-tag-checked.arco-tag-gold:hover {
  background-color: rgba(var(--gold-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-lime {
  background-color: rgba(var(--lime-6), 0.2);
}
body[arco-theme='dark']
  .arco-tag-checked.arco-tag-lime
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgba(var(--lime-6), 0.35);
}
body[arco-theme='dark']
  .arco-tag-checkable.arco-tag-checked.arco-tag-lime:hover {
  background-color: rgba(var(--lime-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-green {
  background-color: rgba(var(--green-6), 0.2);
}
body[arco-theme='dark']
  .arco-tag-checked.arco-tag-green
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgba(var(--green-6), 0.35);
}
body[arco-theme='dark']
  .arco-tag-checkable.arco-tag-checked.arco-tag-green:hover {
  background-color: rgba(var(--green-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-cyan {
  background-color: rgba(var(--cyan-6), 0.2);
}
body[arco-theme='dark']
  .arco-tag-checked.arco-tag-cyan
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgba(var(--cyan-6), 0.35);
}
body[arco-theme='dark']
  .arco-tag-checkable.arco-tag-checked.arco-tag-cyan:hover {
  background-color: rgba(var(--cyan-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-blue {
  background-color: rgba(var(--blue-6), 0.2);
}
body[arco-theme='dark']
  .arco-tag-checked.arco-tag-blue
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgba(var(--blue-6), 0.35);
}
body[arco-theme='dark']
  .arco-tag-checkable.arco-tag-checked.arco-tag-blue:hover {
  background-color: rgba(var(--blue-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-arcoblue {
  background-color: rgba(var(--arcoblue-6), 0.2);
}
body[arco-theme='dark']
  .arco-tag-checked.arco-tag-arcoblue
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgba(var(--arcoblue-6), 0.35);
}
body[arco-theme='dark']
  .arco-tag-checkable.arco-tag-checked.arco-tag-arcoblue:hover {
  background-color: rgba(var(--arcoblue-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-purple {
  background-color: rgba(var(--purple-6), 0.2);
}
body[arco-theme='dark']
  .arco-tag-checked.arco-tag-purple
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgba(var(--purple-6), 0.35);
}
body[arco-theme='dark']
  .arco-tag-checkable.arco-tag-checked.arco-tag-purple:hover {
  background-color: rgba(var(--purple-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-pinkpurple {
  background-color: rgba(var(--pinkpurple-6), 0.2);
}
body[arco-theme='dark']
  .arco-tag-checked.arco-tag-pinkpurple
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgba(var(--pinkpurple-6), 0.35);
}
body[arco-theme='dark']
  .arco-tag-checkable.arco-tag-checked.arco-tag-pinkpurple:hover {
  background-color: rgba(var(--pinkpurple-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-magenta {
  background-color: rgba(var(--magenta-6), 0.2);
}
body[arco-theme='dark']
  .arco-tag-checked.arco-tag-magenta
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgba(var(--magenta-6), 0.35);
}
body[arco-theme='dark']
  .arco-tag-checkable.arco-tag-checked.arco-tag-magenta:hover {
  background-color: rgba(var(--magenta-6), 0.35);
}
body[arco-theme='dark'] .arco-tag-checked.arco-tag-gray {
  background-color: rgba(var(--gray-6), 0.2);
}
body[arco-theme='dark']
  .arco-tag-checked.arco-tag-gray
  .arco-icon-hover.arco-tag-icon-hover:hover:before {
  background-color: rgba(var(--gray-6), 0.35);
}
body[arco-theme='dark']
  .arco-tag-checkable.arco-tag-checked.arco-tag-gray:hover {
  background-color: rgba(var(--gray-6), 0.35);
}
.arco-tag-rtl {
  direction: rtl;
}
.arco-tag-rtl .arco-tag-icon {
  margin-left: 4px;
  margin-right: 0;
}
.arco-tag-rtl .arco-tag-close-btn,
.arco-tag-rtl .arco-tag-loading-icon {
  margin-left: 0;
  margin-right: 4px;
}
@keyframes arco-draggable-item-blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  to {
    opacity: 1;
  }
}
.arco-draggable-item {
  box-sizing: border-box;
  list-style: none;
  user-select: none;
}
.arco-draggable-item-dragging {
  opacity: 0.3;
}
.arco-draggable-item-dragover.arco-draggable-item-gap-left {
  box-shadow: -1px 0 rgb(var(--primary-6));
}
.arco-draggable-item-dragover.arco-draggable-item-gap-right {
  box-shadow: 1px 0 rgb(var(--primary-6));
}
.arco-draggable-item-dragover.arco-draggable-item-gap-top {
  box-shadow: 0 -1px rgb(var(--primary-6));
}
.arco-draggable-item-dragover.arco-draggable-item-gap-bottom {
  box-shadow: 0 1px rgb(var(--primary-6));
}
.arco-draggable-item-dragged {
  animation: arco-draggable-item-blink 0.8s;
  animation-timing-function: cubic-bezier(0, 0, 1, 1);
}
.arco-input-tag {
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
  padding-left: 4px;
  padding-right: 4px;
  border-radius: var(--border-radius-small);
  vertical-align: top;
  cursor: text;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
  background-color: var(--color-fill-2);
  color: var(--color-text-1);
  border: 1px solid transparent;
}
.arco-input-tag-view {
  display: flex;
  width: 100%;
}
.arco-input-tag-inner {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  flex-grow: 1;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
}
.arco-input-tag-prefix,
.arco-input-tag-suffix {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  min-width: 22px;
}
.arco-input-tag-prefix {
  padding-left: 8px;
  padding-right: 4px;
}
.arco-input-tag-suffix {
  padding-right: 8px;
}
.arco-input-tag .arco-input-tag-clear-icon {
  display: none;
  font-size: 12px;
  color: var(--color-text-2);
  cursor: pointer;
}
.arco-input-tag .arco-input-tag-clear-icon > svg {
  transition: color 0.1s cubic-bezier(0, 0, 1, 1);
  position: relative;
}
.arco-input-tag:hover .arco-input-tag-clear-icon {
  display: block;
}
.arco-input-tag:hover .arco-input-tag-clear-icon ~ * {
  display: none;
}
.arco-input-tag:not(.arco-input-tag-focus)
  .arco-input-tag-clear-icon:hover:before {
  background-color: var(--color-fill-4);
}
.arco-input-tag:not(.arco-input-tag-focus) .arco-draggable-item {
  cursor: move;
}
.arco-input-tag-input {
  width: 4px;
  max-width: 100%;
  padding: 0;
  border: none;
  outline: none;
  background: none;
  font-size: inherit;
  cursor: inherit;
  color: inherit;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-input-tag-input:first-child {
  width: 100%;
}
.arco-tag + .arco-input-tag-input[disabled] {
  width: 0;
}
.arco-input-tag-input-mirror {
  position: absolute;
  top: 0;
  left: 0;
  visibility: hidden;
  pointer-events: none;
}
.arco-input-tag-input::placeholder {
  color: var(--color-text-3);
}
.arco-input-tag-tag {
  max-width: 100%;
  margin-right: 4px;
  font-size: 12px;
}
.arco-input-tag-tag-ellipsis {
  font-size: 12px;
  margin: 0 4px;
}
.arco-input-tag .arco-icon-hover {
  cursor: pointer;
}
.arco-input-tag .arco-icon-hover.arco-icon-hover-disabled {
  cursor: not-allowed;
}
.arco-input-tag:hover {
  background-color: var(--color-fill-3);
  border: 1px solid transparent;
}
.arco-input-tag.arco-input-tag-focus {
  background-color: var(--color-bg-2);
  border: 1px solid rgb(var(--primary-6));
  box-shadow: 0 0 0 0 rgb(var(--primary-2));
}
.arco-input-tag .arco-input-tag-tag {
  color: var(--color-text-1);
  border-color: var(--color-fill-3);
  background-color: var(--color-bg-2);
}
.arco-input-tag .arco-icon-hover:hover:before {
  background-color: var(--color-fill-2);
}
.arco-input-tag.arco-input-tag-focus .arco-input-tag-tag {
  border-color: var(--color-fill-2);
  background-color: var(--color-fill-2);
}
.arco-input-tag.arco-input-tag-focus .arco-icon-hover:hover:before {
  background-color: var(--color-fill-3);
}
.arco-input-tag.arco-input-tag-disabled .arco-input-tag-tag {
  color: var(--color-text-4);
  border-color: var(--color-fill-3);
  background-color: var(--color-fill-2);
}
.arco-input-tag-warning {
  background-color: var(--color-warning-light-1);
  color: var(--color-text-1);
  border: 1px solid transparent;
}
.arco-input-tag-warning:hover {
  background-color: var(--color-warning-light-2);
  border: 1px solid transparent;
}
.arco-input-tag-warning.arco-input-tag-focus {
  background-color: var(--color-bg-2);
  border: 1px solid rgb(var(--warning-6));
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-input-tag-error {
  background-color: rgb(var(--danger-1));
  color: var(--color-text-1);
  border: 1px solid transparent;
}
.arco-input-tag-error:hover {
  background-color: rgb(var(--danger-2));
  border: 1px solid transparent;
}
.arco-input-tag-error.arco-input-tag-focus {
  background-color: var(--color-bg-2);
  border: 1px solid rgb(var(--danger-6));
  box-shadow: 0 0 0 0 rgb(var(--danger-2));
}
.arco-input-tag-disabled {
  background-color: var(--color-fill-2);
  color: var(--color-text-4);
  border: 1px solid transparent;
  cursor: not-allowed;
}
.arco-input-tag-disabled:hover {
  background-color: var(--color-fill-2);
  border: 1px solid transparent;
}
.arco-input-tag-disabled .arco-input-tag-input::placeholder {
  color: var(--color-text-4);
}
.arco-input-tag-readonly {
  cursor: default;
}
.arco-input-tag-wrapper {
  display: inline-flex;
  align-items: stretch;
  width: 100%;
}
.arco-input-tag-wrapper .arco-input-tag {
  min-width: 0;
}
.arco-input-tag-wrapper
  .arco-input-tag:not(.arco-input-tag-focused):not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.arco-input-tag-wrapper
  .arco-input-tag:not(.arco-input-tag-focused):not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.arco-input-tag-addafter,
.arco-input-tag-addbefore {
  display: flex;
  align-items: center;
  padding: 0 12px;
  color: var(--color-text-1);
  background-color: var(--color-fill-2);
  white-space: nowrap;
  border: 1px solid transparent;
}
.arco-input-tag-addbefore {
  border-right: 1px solid var(--color-border-2);
  border-top-left-radius: var(--border-radius-small);
  border-bottom-left-radius: var(--border-radius-small);
}
.arco-input-tag-addafter {
  border-left: 1px solid var(--color-border-2);
  border-top-right-radius: var(--border-radius-small);
  border-bottom-right-radius: var(--border-radius-small);
}
.arco-input-tag-size-mini {
  font-size: 12px;
}
.arco-input-tag-size-mini .arco-input-tag-view {
  min-height: 22px;
}
.arco-input-tag-size-mini .arco-input-tag-inner {
  padding-top: -1px;
  padding-bottom: -1px;
}
.arco-input-tag-size-mini .arco-input-tag-tag,
.arco-input-tag-size-mini .arco-input-tag-tag + .arco-input-tag-input {
  margin-top: 1px;
  margin-bottom: 1px;
  height: 20px;
  line-height: 18px;
}
.arco-input-tag-size-mini.arco-input-tag-has-placeholder input,
.arco-input-tag-size-mini.arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  box-sizing: border-box;
  padding-left: 4px;
}
.arco-input-tag-size-default {
  font-size: 14px;
}
.arco-input-tag-size-default .arco-input-tag-view {
  min-height: 30px;
}
.arco-input-tag-size-default .arco-input-tag-inner {
  padding-top: 1px;
  padding-bottom: 1px;
}
.arco-input-tag-size-default .arco-input-tag-tag,
.arco-input-tag-size-default .arco-input-tag-tag + .arco-input-tag-input {
  margin-top: 1px;
  margin-bottom: 1px;
  height: 24px;
  line-height: 22px;
}
.arco-input-tag-size-default.arco-input-tag-has-placeholder input,
.arco-input-tag-size-default.arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  box-sizing: border-box;
  padding-left: 8px;
}
.arco-input-tag-size-small {
  font-size: 14px;
}
.arco-input-tag-size-small .arco-input-tag-view {
  min-height: 26px;
}
.arco-input-tag-size-small .arco-input-tag-inner {
  padding-top: 1px;
  padding-bottom: 1px;
}
.arco-input-tag-size-small .arco-input-tag-tag,
.arco-input-tag-size-small .arco-input-tag-tag + .arco-input-tag-input {
  margin-top: 1px;
  margin-bottom: 1px;
  height: 20px;
  line-height: 18px;
}
.arco-input-tag-size-small.arco-input-tag-has-placeholder input,
.arco-input-tag-size-small.arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  box-sizing: border-box;
  padding-left: 8px;
}
.arco-input-tag-size-large {
  font-size: 16px;
}
.arco-input-tag-size-large .arco-input-tag-view {
  min-height: 34px;
}
.arco-input-tag-size-large .arco-input-tag-inner {
  padding-top: 1px;
  padding-bottom: 1px;
}
.arco-input-tag-size-large .arco-input-tag-tag,
.arco-input-tag-size-large .arco-input-tag-tag + .arco-input-tag-input {
  margin-top: 1px;
  margin-bottom: 1px;
  height: 28px;
  line-height: 26px;
}
.arco-input-tag-size-large.arco-input-tag-has-placeholder input,
.arco-input-tag-size-large.arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  box-sizing: border-box;
  padding-left: 12px;
}
.arco-input-tag-rtl {
  direction: rtl;
  padding-right: 4px;
  padding-left: 4px;
}
.arco-input-tag-rtl .arco-input-tag-prefix {
  padding-right: 8px;
  padding-left: 4px;
}
.arco-input-tag-rtl .arco-input-tag-suffix {
  padding-right: 0;
  padding-left: 8px;
}
.arco-input-tag-rtl .arco-input-tag-tag {
  margin-right: 0;
  margin-left: 4px;
}
.arco-input-tag-rtl .arco-input-tag-input-mirror {
  right: 0;
  left: initial;
}
.arco-input-tag-rtl.arco-input-tag-size-mini.arco-input-tag-has-placeholder
  input,
.arco-input-tag-rtl.arco-input-tag-size-mini.arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  padding-right: 4px;
}
.arco-input-tag-rtl.arco-input-tag-size-default.arco-input-tag-has-placeholder
  input,
.arco-input-tag-rtl.arco-input-tag-size-default.arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror,
.arco-input-tag-rtl.arco-input-tag-size-small.arco-input-tag-has-placeholder
  input,
.arco-input-tag-rtl.arco-input-tag-size-small.arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  padding-right: 8px;
}
.arco-input-tag-rtl.arco-input-tag-size-large.arco-input-tag-has-placeholder
  input,
.arco-input-tag-rtl.arco-input-tag-size-large.arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  padding-right: 12px;
}
.arco-input-tag-wrapper-rtl .arco-input-tag-addbefore {
  border-right: unset;
  border-left: 1px solid var(--color-border-2);
}
.arco-input-tag-wrapper-rtl .arco-input-tag-addafter {
  border-left: unset;
  border-right: 1px solid var(--color-border-2);
}
.arco-select .arco-select-view {
  color: var(--color-text-1);
  background-color: var(--color-fill-2);
  border: 1px solid transparent;
}
.arco-select:hover .arco-select-view {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-select.arco-select-focused .arco-select-view {
  color: var(--color-text-1);
  background-color: var(--color-bg-2);
  border-color: rgb(var(--primary-6));
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-select .arco-select-suffix-icon,
.arco-select .arco-select-loading-icon,
.arco-select .arco-select-search-icon,
.arco-select .arco-select-clear-icon,
.arco-select .arco-select-arrow-icon,
.arco-select .arco-select-expand-icon {
  color: var(--color-text-2);
}
.arco-select-error .arco-select-view {
  background-color: var(--color-danger-light-1);
  border: 1px solid transparent;
}
.arco-select-error:hover .arco-select-view {
  background-color: var(--color-danger-light-2);
  border-color: transparent;
}
.arco-select-error.arco-select-focused .arco-select-view {
  color: var(--color-text-1);
  background-color: var(--color-bg-2);
  border-color: rgb(var(--danger-6));
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-select-warning .arco-select-view {
  background-color: var(--color-warning-light-1);
  border: 1px solid transparent;
}
.arco-select-warning:hover .arco-select-view {
  background-color: var(--color-warning-light-2);
  border-color: transparent;
}
.arco-select-warning.arco-select-focused .arco-select-view {
  color: var(--color-text-1);
  background-color: var(--color-bg-2);
  border-color: rgb(var(--warning-6));
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-select-disabled .arco-select-view {
  color: var(--color-text-4);
  background-color: var(--color-fill-2);
  border: 1px solid transparent;
}
.arco-select-disabled:hover .arco-select-view {
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-select-disabled .arco-select-suffix-icon,
.arco-select-disabled .arco-select-loading-icon,
.arco-select-disabled .arco-select-search-icon,
.arco-select-disabled .arco-select-clear-icon,
.arco-select-disabled .arco-select-arrow-icon,
.arco-select-disabled .arco-select-expand-icon {
  color: var(--color-text-4);
}
.arco-select-no-border .arco-select-view {
  border: none !important;
  background: none !important;
}
.arco-select-size-mini.arco-select-multiple .arco-select-view {
  height: auto;
  font-size: 12px;
  padding: 0 3px;
  line-height: 0;
}
.arco-select-size-mini.arco-select-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-select-size-mini.arco-select-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  box-sizing: border-box;
  padding-left: 4px;
}
.arco-select-size-mini.arco-select-multiple .arco-select-suffix {
  padding-right: 4px;
}
.arco-select-size-mini.arco-select-multiple input {
  font-size: 12px;
}
.arco-select-size-mini.arco-select-single .arco-select-view {
  height: 24px;
  line-height: 22px;
  font-size: 12px;
  padding: 0 7px;
}
.arco-select-size-mini.arco-select-single input {
  font-size: 12px;
}
.arco-select-size-mini.arco-select-multiple .arco-select-view-with-prefix {
  padding-left: 7px;
}
.arco-select-size-small.arco-select-multiple .arco-select-view {
  height: auto;
  font-size: 14px;
  padding: 0 3px;
  line-height: 0;
}
.arco-select-size-small.arco-select-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-select-size-small.arco-select-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  box-sizing: border-box;
  padding-left: 8px;
}
.arco-select-size-small.arco-select-multiple .arco-select-suffix {
  padding-right: 8px;
}
.arco-select-size-small.arco-select-multiple input {
  font-size: 14px;
}
.arco-select-size-small.arco-select-single .arco-select-view {
  height: 28px;
  line-height: 26px;
  font-size: 14px;
  padding: 0 11px;
}
.arco-select-size-small.arco-select-single input {
  font-size: 14px;
}
.arco-select-size-small.arco-select-multiple .arco-select-view-with-prefix {
  padding-left: 11px;
}
.arco-select-size-default.arco-select-multiple .arco-select-view {
  height: auto;
  font-size: 14px;
  padding: 0 3px;
  line-height: 0;
}
.arco-select-size-default.arco-select-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-select-size-default.arco-select-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  box-sizing: border-box;
  padding-left: 8px;
}
.arco-select-size-default.arco-select-multiple .arco-select-suffix {
  padding-right: 8px;
}
.arco-select-size-default.arco-select-multiple input {
  font-size: 14px;
}
.arco-select-size-default.arco-select-single .arco-select-view {
  height: 32px;
  line-height: 30px;
  font-size: 14px;
  padding: 0 11px;
}
.arco-select-size-default.arco-select-single input {
  font-size: 14px;
}
.arco-select-size-default.arco-select-multiple .arco-select-view-with-prefix {
  padding-left: 11px;
}
.arco-select-size-large.arco-select-multiple .arco-select-view {
  height: auto;
  font-size: 16px;
  padding: 0 3px;
  line-height: 0;
}
.arco-select-size-large.arco-select-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-select-size-large.arco-select-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  box-sizing: border-box;
  padding-left: 12px;
}
.arco-select-size-large.arco-select-multiple .arco-select-suffix {
  padding-right: 12px;
}
.arco-select-size-large.arco-select-multiple input {
  font-size: 16px;
}
.arco-select-size-large.arco-select-single .arco-select-view {
  height: 36px;
  line-height: 34px;
  font-size: 16px;
  padding: 0 15px;
}
.arco-select-size-large.arco-select-single input {
  font-size: 16px;
}
.arco-select-size-large.arco-select-multiple .arco-select-view-with-prefix {
  padding-left: 15px;
}
.arco-select {
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  cursor: pointer;
}
.arco-select-view {
  display: flex;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  border-radius: var(--border-radius-small);
  outline: none;
  user-select: none;
  text-align: left;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1), padding 0s linear;
}
.arco-select-view input {
  color: inherit;
  cursor: inherit;
}
.arco-select-view input::placeholder {
  color: var(--color-text-3);
}
.arco-select-view input[disabled] {
  pointer-events: none;
}
.arco-select-multiple,
.arco-select-show-search {
  cursor: text;
}
.arco-select-disabled {
  cursor: not-allowed;
}
.arco-select-disabled .arco-select-view input::placeholder {
  color: var(--color-text-4);
}
.arco-select-single .arco-select-view-input {
  box-sizing: border-box;
  width: 100%;
  padding: 0;
  border: none;
  outline: none;
  background: transparent;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-select-single .arco-select-view-selector {
  position: relative;
  display: inline-flex;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
}
.arco-select-single .arco-select-view-selector .arco-select-view-input {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}
.arco-select-single .arco-select-view-selector .arco-select-view-value-mirror {
  opacity: 0;
}
.arco-select-single .arco-select-view-value,
.arco-select-single .arco-select-view-value-mirror {
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-select-single .arco-select-view-value:after,
.arco-select-single .arco-select-view-value-mirror:after {
  content: '.';
  font-size: 0;
  line-height: 0;
  visibility: hidden;
}
.arco-select-single .arco-select-view .arco-select-hidden {
  opacity: 0;
  position: absolute;
  z-index: -1;
}
.arco-select-multiple {
  vertical-align: top;
}
.arco-select-multiple .arco-select-view {
  padding: 0 4px;
  line-height: 0;
}
.arco-select-multiple .arco-select-view-with-prefix {
  padding-left: 12px;
}
.arco-select-multiple .arco-input-tag {
  flex: 1;
  padding: 0;
  border: none !important;
  background: none !important;
  box-shadow: none !important;
  overflow: hidden;
}
.arco-select-multiple .arco-tag {
  max-width: 100%;
}
.arco-select-multiple:not(.arco-select-focused)
  .arco-input-tag
  input:not(:first-child)[value=''] {
  opacity: 0;
  position: absolute;
  z-index: -1;
}
.arco-select-prefix {
  display: flex;
  align-items: center;
  margin-right: 12px;
  white-space: nowrap;
  color: var(--color-text-2);
}
.arco-select-suffix {
  display: flex;
  align-items: center;
  margin-left: 4px;
}
.arco-select-suffix-icon,
.arco-select-search-icon,
.arco-select-loading-icon,
.arco-select-expand-icon,
.arco-select-clear-icon {
  font-size: 12px;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-select-arrow-icon {
  font-size: 12px;
}
.arco-select-open .arco-select-arrow-icon svg {
  transform: rotate(180deg);
}
.arco-select .arco-select-clear-icon {
  display: none;
  cursor: pointer;
}
.arco-select .arco-select-clear-icon > svg {
  position: relative;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-select:hover .arco-select-clear-icon {
  display: block;
}
.arco-select:hover .arco-select-clear-icon ~ * {
  display: none;
}
.arco-select-wrapper {
  display: inline-flex;
  align-items: stretch;
  width: 100%;
}
.arco-select-wrapper .arco-select {
  min-width: 0;
}
.arco-select-wrapper
  .arco-select:not(.arco-select-focused):not(:first-child)
  .arco-select-view {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.arco-select-wrapper
  .arco-select:not(.arco-select-focused):not(:last-child)
  .arco-select-view {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.arco-select-addbefore {
  display: flex;
  align-items: center;
  padding: 0 12px;
  color: var(--color-text-1);
  background-color: var(--color-fill-2);
  white-space: nowrap;
  border: 1px solid transparent;
}
.arco-select-addbefore {
  border-right: 1px solid var(--color-border-2);
  border-top-left-radius: var(--border-radius-small);
  border-bottom-left-radius: var(--border-radius-small);
}
.arco-select-popup {
  top: 4px;
  box-sizing: border-box;
  padding: 4px 0;
  border: 1px solid var(--color-fill-3);
  border-radius: var(--border-radius-medium);
  background-color: var(--color-bg-popup);
  box-shadow: 0 4px 10px #0000001a;
  overflow: hidden;
}
.arco-select-popup-hidden {
  display: none;
}
.arco-select-popup .arco-select-popup-inner {
  width: 100%;
  max-height: 200px;
  list-style: none;
}
.arco-select-popup .arco-select-option {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  padding: 0 12px;
  font-size: 14px;
  text-align: left;
  cursor: pointer;
  line-height: 36px;
  border-radius: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: var(--color-text-1);
  background-color: var(--color-bg-popup);
}
.arco-select-popup .arco-select-option-selected {
  color: var(--color-text-1);
  background-color: var(--color-bg-popup);
}
.arco-select-popup .arco-select-option-hover {
  color: var(--color-text-1);
  background-color: var(--color-fill-2);
}
.arco-select-popup .arco-select-option-disabled {
  color: var(--color-text-4);
  background-color: var(--color-bg-popup);
}
.arco-select-popup .arco-select-option-disabled {
  cursor: not-allowed;
}
.arco-select-popup .arco-select-option-selected {
  font-weight: 500;
}
.arco-select-popup .arco-select-option-empty {
  height: 36px;
}
.arco-select-popup .arco-select-option-rtl {
  text-align: right;
}
.arco-select-option-wrapper {
  display: flex;
  align-items: center;
  padding: 0 7px;
}
.arco-select-option-wrapper .arco-select-checkbox {
  padding: 0 5px;
}
.arco-select-option-wrapper .arco-select-checkbox input {
  display: none;
}
.arco-select-option-wrapper .arco-select-option {
  flex: 1;
  margin-left: 1px;
  padding: 0 4px;
  border-radius: var(--border-radius-small);
}
.arco-select-group-title {
  box-sizing: border-box;
  width: 100%;
  padding: 8px 12px 0;
  line-height: 20px;
  font-size: 12px;
  color: var(--color-text-3);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.arco-select-group-title:first-child {
  padding-top: 4px;
}
.arco-select-highlight {
  font-weight: 500;
  color: var(--color-text-1);
}
.arco-select-rtl {
  direction: rtl;
}
.arco-select-rtl.arco-select-size-mini.arco-select-multiple
  .arco-select-view-with-prefix {
  padding-left: 0;
  padding-right: 7px;
}
.arco-select-rtl.arco-select-size-mini.arco-select-multiple
  .arco-select-suffix {
  padding-right: 0;
  padding-left: 4px;
}
.arco-select-rtl.arco-select-size-mini.arco-select-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-select-rtl.arco-select-size-mini.arco-select-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  padding-left: 0;
  padding-right: 4px;
}
.arco-select-rtl.arco-select-size-small.arco-select-multiple
  .arco-select-view-with-prefix {
  padding-left: 0;
  padding-right: 11px;
}
.arco-select-rtl.arco-select-size-small.arco-select-multiple
  .arco-select-suffix {
  padding-right: 0;
  padding-left: 8px;
}
.arco-select-rtl.arco-select-size-small.arco-select-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-select-rtl.arco-select-size-small.arco-select-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  padding-left: 0;
  padding-right: 8px;
}
.arco-select-rtl.arco-select-size-default.arco-select-multiple
  .arco-select-view-with-prefix {
  padding-left: 0;
  padding-right: 11px;
}
.arco-select-rtl.arco-select-size-default.arco-select-multiple
  .arco-select-suffix {
  padding-right: 0;
  padding-left: 8px;
}
.arco-select-rtl.arco-select-size-default.arco-select-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-select-rtl.arco-select-size-default.arco-select-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  padding-left: 0;
  padding-right: 8px;
}
.arco-select-rtl.arco-select-size-large.arco-select-multiple
  .arco-select-view-with-prefix {
  padding-left: 0;
  padding-right: 15px;
}
.arco-select-rtl.arco-select-size-large.arco-select-multiple
  .arco-select-suffix {
  padding-right: 0;
  padding-left: 12px;
}
.arco-select-rtl.arco-select-size-large.arco-select-multiple
  .arco-input-tag-has-placeholder
  input,
.arco-select-rtl.arco-select-size-large.arco-select-multiple
  .arco-input-tag-has-placeholder
  .arco-input-tag-input-mirror {
  padding-left: 0;
  padding-right: 12px;
}
.arco-select-wrapper-rtl .arco-select-addbefore {
  border-right: unset;
  border-left: 1px solid var(--color-border-2);
}
.arco-select-rtl .arco-select-view {
  text-align: right;
}
.arco-select-rtl .arco-select-multiple .arco-select-view-with-prefix {
  padding-left: 0;
  padding-right: 12px;
}
.arco-select-rtl .arco-select-prefix {
  margin-right: 0;
  margin-left: 12px;
}
.arco-select-rtl .arco-select-suffix {
  margin-left: 0;
  margin-right: 4px;
}
.arco-popover-content {
  padding: 12px 16px;
}
.arco-popover-title {
  font-size: 16px;
  color: var(--color-text-1);
  font-weight: 500;
}
.arco-popover-title + .arco-popover-inner-content {
  margin-top: 4px;
}
.arco-popover-content,
.arco-popconfirm-content {
  color: var(--color-text-2);
  background-color: var(--color-bg-popup);
  box-shadow: 0 4px 10px #0000001a;
  max-width: none;
  width: 100%;
  font-size: 14px;
  border-radius: var(--border-radius-medium);
  line-height: 1.5715;
  box-sizing: border-box;
  border: 1px solid var(--color-neutral-3);
}
.arco-popover-content-inner,
.arco-popconfirm-content-inner {
  word-wrap: break-word;
  text-align: left;
}
.arco-popover-arrow.arco-trigger-arrow,
.arco-popconfirm-arrow.arco-trigger-arrow {
  background-color: var(--color-bg-popup);
  border: 1px solid var(--color-neutral-3);
  z-index: 1;
}
.arco-popover-inner-rtl {
  direction: rtl;
  text-align: right;
}
.arco-avatar {
  display: inline-flex;
  align-items: center;
  position: relative;
  background-color: var(--color-fill-4);
  white-space: nowrap;
  color: var(--color-white);
  box-sizing: border-box;
  vertical-align: middle;
  width: 40px;
  height: 40px;
  font-size: 20px;
}
.arco-avatar-circle {
  border-radius: var(--border-radius-circle);
}
.arco-avatar-circle .arco-avatar-image {
  border-radius: var(--border-radius-circle);
  overflow: hidden;
}
.arco-avatar-square {
  border-radius: var(--border-radius-medium);
}
.arco-avatar-square .arco-avatar-image {
  border-radius: var(--border-radius-medium);
  overflow: hidden;
}
.arco-avatar-text {
  position: absolute;
  left: 50%;
  transform-origin: 0 center;
  transform: translate(-50%);
  font-weight: 500;
  line-height: 1;
}
.arco-avatar-image {
  display: inline-flex;
  width: 100%;
  height: 100%;
}
.arco-avatar-image img,
.arco-avatar-image picture {
  width: 100%;
  height: 100%;
}
.arco-avatar-trigger-icon-button {
  position: absolute;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  bottom: -4px;
  right: -4px;
  color: var(--color-fill-4);
  font-size: 12px;
  border-radius: var(--border-radius-circle);
  width: 20px;
  height: 20px;
  line-height: 20px;
  background-color: var(--color-neutral-2);
  transition: background-color 0.1s cubic-bezier(0, 0, 1, 1);
  z-index: 1;
}
.arco-avatar-trigger-icon-mask {
  position: absolute;
  display: flex;
  opacity: 0;
  z-index: 0;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  font-size: 16px;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
  border-radius: var(--border-radius-medium);
  background-color: #1d212999;
  color: var(--color-white);
}
.arco-avatar-circle .arco-avatar-trigger-icon-mask {
  border-radius: var(--border-radius-circle);
}
.arco-avatar-with-trigger-icon {
  cursor: pointer;
}
.arco-avatar-with-trigger-icon:hover .arco-avatar-trigger-icon-mask {
  z-index: 2;
  opacity: 1;
}
.arco-avatar-with-trigger-icon:hover .arco-avatar-trigger-icon-button {
  background-color: var(--color-neutral-3);
}
.arco-avatar-rtl {
  direction: rtl;
}
.arco-avatar-rtl .arco-avatar-trigger-icon-button {
  right: unset;
  left: -4px;
}
.arco-avatar-group {
  display: inline-block;
  line-height: 0;
}
.arco-avatar-group-max-count-avatar {
  cursor: default;
  color: var(--color-white);
  font-size: 20px;
}
.arco-avatar-group-rtl {
  direction: rtl;
}
.arco-avatar-group .arco-avatar {
  border: 2px solid var(--color-bg-2);
}
.arco-avatar-group .arco-avatar:not(:first-child) {
  margin-left: -10px;
}
.arco-avatar-group-popover .arco-avatar:not(:first-child) {
  margin-left: 4px;
}
.arco-input {
  line-height: 1.5715;
  outline: none;
  appearance: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  width: 100%;
  border-radius: var(--border-radius-small);
  color: var(--color-text-1);
  padding: 4px 12px;
  font-size: 14px;
  box-sizing: border-box;
  transition: color 0.1s cubic-bezier(0, 0, 1, 1),
    border-color 0.1s cubic-bezier(0, 0, 1, 1),
    background-color 0.1s cubic-bezier(0, 0, 1, 1);
  border: 1px solid transparent;
  background-color: var(--color-fill-2);
}
.arco-input::placeholder {
  color: var(--color-text-3);
}
.arco-input:hover {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-input:focus,
.arco-input.arco-input-focus {
  border-color: rgb(var(--primary-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-input-error {
  border-color: transparent;
  background-color: var(--color-danger-light-1);
}
.arco-input-error:hover {
  border-color: transparent;
  background-color: var(--color-danger-light-2);
}
.arco-input-error .arco-input,
.arco-input-error .arco-input:hover {
  background: none;
  box-shadow: none;
}
.arco-input-error.arco-input-focus,
.arco-input-error.arco-input-focus:hover {
  border-color: rgb(var(--danger-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-input-error:focus,
.arco-input-error:focus:hover {
  border-color: rgb(var(--danger-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-input-warning {
  border-color: transparent;
  background-color: var(--color-warning-light-1);
}
.arco-input-warning:hover {
  border-color: transparent;
  background-color: var(--color-warning-light-2);
}
.arco-input-warning .arco-input,
.arco-input-warning .arco-input:hover {
  background: none;
  box-shadow: none;
}
.arco-input-warning.arco-input-focus,
.arco-input-warning.arco-input-focus:hover {
  border-color: rgb(var(--warning-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-input-warning:focus,
.arco-input-warning:focus:hover {
  border-color: rgb(var(--warning-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-input-autowidth {
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}
.arco-input-autowidth:hover {
  text-overflow: unset;
}
.arco-input-disabled {
  background-color: var(--color-fill-2);
  cursor: not-allowed;
  color: var(--color-text-4);
  -webkit-text-fill-color: var(--color-text-4);
  border-color: transparent;
}
.arco-input-disabled:hover {
  border-color: transparent;
  background-color: var(--color-fill-2);
  color: var(--color-text-4);
}
.arco-input-disabled::placeholder {
  color: var(--color-text-4);
}
.arco-input input:disabled {
  color: var(--color-text-4);
  opacity: 1;
  -webkit-text-fill-color: var(--color-text-4);
}
.arco-input-word-limit {
  font-size: 12px;
  color: var(--color-text-3);
  padding-left: 8px;
}
.arco-input-word-limit-error {
  color: rgb(var(--danger-6));
}
.arco-input-size-mini {
  line-height: 1.667;
  font-size: 12px;
  padding-top: 1px;
  padding-bottom: 1px;
}
.arco-input-size-small {
  padding-top: 2px;
  padding-bottom: 2px;
  font-size: 14px;
}
.arco-input-size-large {
  padding-top: 6px;
  padding-bottom: 6px;
  font-size: 14px;
}
.arco-input-group-wrapper-mini .arco-input-group-addbefore,
.arco-input-group-wrapper-mini .arco-input-group-addafter,
.arco-input-inner-wrapper.arco-input-inner-wrapper-mini,
.arco-input-size-mini {
  padding-left: 8px;
  padding-right: 8px;
}
.arco-input-group-wrapper-small .arco-input-group-addbefore,
.arco-input-group-wrapper-small .arco-input-group-addafter,
.arco-input-inner-wrapper.arco-input-inner-wrapper-small,
.arco-input-size-small {
  padding-left: 12px;
  padding-right: 12px;
}
.arco-input-group-wrapper-large .arco-input-group-addbefore,
.arco-input-group-wrapper-large .arco-input-group-addafter,
.arco-input-inner-wrapper.arco-input-inner-wrapper-large,
.arco-input-size-large {
  padding-left: 16px;
  padding-right: 16px;
}
.arco-input-clear-icon {
  font-size: 12px;
  cursor: pointer;
  color: var(--color-text-2);
}
.arco-input-clear-icon:focus-visible:before {
  box-shadow: 0 0 0 2px rgb(var(--primary-6));
}
.arco-input-clear-icon > svg {
  transition: color 0.1s cubic-bezier(0, 0, 1, 1);
  position: relative;
}
.arco-input-inner-wrapper {
  outline: none;
  appearance: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  border-radius: var(--border-radius-small);
  color: var(--color-text-1);
  padding-left: 12px;
  padding-right: 12px;
  font-size: 14px;
  box-sizing: border-box;
  transition: color 0.1s cubic-bezier(0, 0, 1, 1),
    border-color 0.1s cubic-bezier(0, 0, 1, 1),
    background-color 0.1s cubic-bezier(0, 0, 1, 1);
  border: 1px solid transparent;
  background-color: var(--color-fill-2);
  display: inline-flex;
  width: 100%;
  position: relative;
  align-items: center;
}
.arco-input-inner-wrapper::placeholder {
  color: var(--color-text-3);
}
.arco-input-inner-wrapper:hover {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-input-inner-wrapper:focus,
.arco-input-inner-wrapper.arco-input-inner-wrapper-focus {
  border-color: rgb(var(--primary-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-input-inner-wrapper-error {
  border-color: transparent;
  background-color: var(--color-danger-light-1);
}
.arco-input-inner-wrapper-error:hover {
  border-color: transparent;
  background-color: var(--color-danger-light-2);
}
.arco-input-inner-wrapper-error .arco-input,
.arco-input-inner-wrapper-error .arco-input:hover {
  background: none;
  box-shadow: none;
}
.arco-input-inner-wrapper-error.arco-input-inner-wrapper-focus,
.arco-input-inner-wrapper-error.arco-input-inner-wrapper-focus:hover {
  border-color: rgb(var(--danger-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-input-inner-wrapper-error:focus,
.arco-input-inner-wrapper-error:focus:hover {
  border-color: rgb(var(--danger-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-input-inner-wrapper-warning {
  border-color: transparent;
  background-color: var(--color-warning-light-1);
}
.arco-input-inner-wrapper-warning:hover {
  border-color: transparent;
  background-color: var(--color-warning-light-2);
}
.arco-input-inner-wrapper-warning .arco-input,
.arco-input-inner-wrapper-warning .arco-input:hover {
  background: none;
  box-shadow: none;
}
.arco-input-inner-wrapper-warning.arco-input-inner-wrapper-focus,
.arco-input-inner-wrapper-warning.arco-input-inner-wrapper-focus:hover {
  border-color: rgb(var(--warning-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-input-inner-wrapper-warning:focus,
.arco-input-inner-wrapper-warning:focus:hover {
  border-color: rgb(var(--warning-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-input-inner-wrapper .arco-input {
  padding-left: 0;
  padding-right: 0;
  border-radius: 0;
  border: none;
  background: none;
}
.arco-input-inner-wrapper .arco-input:hover,
.arco-input-inner-wrapper .arco-input:focus {
  background: none;
  box-shadow: none;
}
.arco-input-inner-wrapper-has-prefix > .arco-input-clear-wrapper .arco-input,
.arco-input-inner-wrapper-has-prefix > .arco-input {
  padding-left: 12px;
}
.arco-input-inner-wrapper .arco-input-group-prefix,
.arco-input-inner-wrapper .arco-input-group-suffix {
  user-select: none;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  height: 100%;
}
.arco-input-inner-wrapper .arco-input-group-prefix > svg,
.arco-input-inner-wrapper .arco-input-group-suffix > svg {
  font-size: 14px;
}
.arco-input-inner-wrapper .arco-input-group-prefix,
.arco-input-inner-wrapper .arco-input-group-suffix {
  color: var(--color-text-2);
}
.arco-input-inner-wrapper-disabled {
  background-color: var(--color-fill-2);
  cursor: not-allowed;
  color: var(--color-text-4);
  -webkit-text-fill-color: var(--color-text-4);
  border-color: transparent;
}
.arco-input-inner-wrapper-disabled:hover {
  border-color: transparent;
  background-color: var(--color-fill-2);
  color: var(--color-text-4);
}
.arco-input-inner-wrapper-disabled::placeholder {
  color: var(--color-text-4);
}
.arco-input-inner-wrapper-disabled .arco-input-group-prefix,
.arco-input-inner-wrapper-disabled .arco-input-group-suffix {
  color: inherit;
}
.arco-input-inner-wrapper .arco-input-clear-icon {
  visibility: hidden;
}
.arco-input-inner-wrapper:hover .arco-input-clear-icon {
  visibility: visible;
}
.arco-input-inner-wrapper:hover
  .arco-input-clear-icon
  ~ .arco-input-group-suffix {
  margin-left: 4px;
}
.arco-input-inner-wrapper:not(.arco-input-inner-wrapper-focus)
  .arco-input-clear-icon:hover:before {
  background-color: var(--color-fill-4);
}
.arco-input-group-wrapper-autowidth .arco-input-group {
  display: flex;
  align-items: stretch;
}
.arco-input-group-wrapper-autowidth .arco-input-group-addbefore,
.arco-input-group-wrapper-autowidth .arco-input-group-after {
  display: inline-flex;
  height: unset;
  flex-shrink: 0;
  flex-grow: 0;
  width: auto;
  align-items: center;
}
.arco-input-group-wrapper-autowidth .arco-input-inner-wrapper {
  overflow: hidden;
}
.arco-input-group-wrapper-autowidth .arco-input {
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}
.arco-input-group-wrapper-autowidth .arco-input:hover {
  text-overflow: unset;
}
.arco-input-group {
  display: table;
  width: 100%;
  height: 100%;
  line-height: 0;
}
.arco-input-group > .arco-input-inner-wrapper,
.arco-input-group > .arco-input {
  border-radius: 0;
}
.arco-input-group > .arco-input-inner-wrapper-focus,
.arco-input-group > .arco-input-focus {
  border-radius: var(--border-radius-small);
}
.arco-input-group > :first-child {
  border-top-left-radius: var(--border-radius-small);
  border-bottom-left-radius: var(--border-radius-small);
}
.arco-input-group > :last-child {
  border-top-right-radius: var(--border-radius-small);
  border-bottom-right-radius: var(--border-radius-small);
}
.arco-input-group-addbefore,
.arco-input-group-addafter {
  width: 1px;
  display: table-cell;
  white-space: nowrap;
  height: 100%;
  vertical-align: middle;
  box-sizing: border-box;
  padding: 0 12px;
  color: var(--color-text-1);
  background-color: var(--color-fill-2);
  border: 1px solid transparent;
}
.arco-input-group-addbefore > svg,
.arco-input-group-addafter > svg {
  font-size: 14px;
}
.arco-input-group-addafter {
  border-left: 1px solid var(--color-neutral-3);
}
.arco-input-group-addafter .item-style {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.arco-input-group-addafter .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
  border-color: transparent;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.arco-input-group-addafter .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.arco-input-group-addafter .arco-select .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.arco-input-group-addafter .arco-select.arco-select-single .arco-select-view {
  height: 100%;
}
.arco-input-group-addbefore {
  border-right: 1px solid var(--color-neutral-3);
}
.arco-input-group-addbefore .item-style {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.arco-input-group-addbefore .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
  border-color: transparent;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.arco-input-group-addbefore .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.arco-input-group-addbefore .arco-select .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.arco-input-group-addbefore .arco-select.arco-select-single .arco-select-view {
  height: 100%;
}
.arco-input-group-wrapper {
  width: 100%;
  display: inline-block;
  vertical-align: top;
}
.arco-input-group-wrapper.arco-input-group-wrapper-mini .arco-input-group,
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-inner-wrapper
  .arco-input-group-prefix,
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-inner-wrapper
  .arco-input-group-suffix {
  font-size: 12px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-inner-wrapper
  .arco-input-group-prefix
  > svg,
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-inner-wrapper
  .arco-input-group-suffix
  > svg {
  font-size: 12px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-group-addbefore,
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-group-addafter {
  font-size: 12px;
  height: 22px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-group-addbefore
  > svg,
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-group-addafter
  > svg {
  font-size: 12px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-group-addafter
  .item-style {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-group-addafter
  .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
  border-color: transparent;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-group-addafter
  .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-group-addafter
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-group-addafter
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-group-addbefore
  .item-style {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-group-addbefore
  .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
  border-color: transparent;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-group-addbefore
  .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-group-addbefore
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.arco-input-group-wrapper.arco-input-group-wrapper-mini
  .arco-input-group-addbefore
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.arco-input-group-wrapper.arco-input-group-wrapper-small .arco-input-group,
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-inner-wrapper
  .arco-input-group-prefix,
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-inner-wrapper
  .arco-input-group-suffix {
  font-size: 14px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-inner-wrapper
  .arco-input-group-prefix
  > svg,
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-inner-wrapper
  .arco-input-group-suffix
  > svg {
  font-size: 14px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-group-addbefore,
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-group-addafter {
  font-size: 14px;
  height: 26px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-group-addbefore
  > svg,
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-group-addafter
  > svg {
  font-size: 14px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-group-addafter
  .item-style {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-group-addafter
  .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
  border-color: transparent;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-group-addafter
  .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-group-addafter
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-group-addafter
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-group-addbefore
  .item-style {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-group-addbefore
  .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
  border-color: transparent;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-group-addbefore
  .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-group-addbefore
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.arco-input-group-wrapper.arco-input-group-wrapper-small
  .arco-input-group-addbefore
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.arco-input-group-wrapper.arco-input-group-wrapper-large .arco-input-group,
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-inner-wrapper
  .arco-input-group-prefix,
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-inner-wrapper
  .arco-input-group-suffix {
  font-size: 14px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-inner-wrapper
  .arco-input-group-prefix
  > svg,
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-inner-wrapper
  .arco-input-group-suffix
  > svg {
  font-size: 14px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-group-addbefore,
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-group-addafter {
  font-size: 14px;
  height: 34px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-group-addbefore
  > svg,
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-group-addafter
  > svg {
  font-size: 14px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-group-addafter
  .item-style {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-group-addafter
  .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
  border-color: transparent;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-group-addafter
  .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-group-addafter
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-group-addafter
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-group-addbefore
  .item-style {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-group-addbefore
  .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
  border-color: transparent;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-group-addbefore
  .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-group-addbefore
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.arco-input-group-wrapper.arco-input-group-wrapper-large
  .arco-input-group-addbefore
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.arco-input-group-wrapper.arco-input-custom-height .arco-input-group,
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-inner-wrapper
  .arco-input-group-prefix,
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-inner-wrapper
  .arco-input-group-suffix {
  font-size: 14px;
}
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-inner-wrapper
  .arco-input-group-prefix
  > svg,
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-inner-wrapper
  .arco-input-group-suffix
  > svg {
  font-size: 14px;
}
.arco-input-group-wrapper.arco-input-custom-height .arco-input-group-addbefore,
.arco-input-group-wrapper.arco-input-custom-height .arco-input-group-addafter {
  font-size: 14px;
  height: 22px;
}
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-group-addbefore
  > svg,
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-group-addafter
  > svg {
  font-size: 14px;
}
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-group-addafter
  .item-style {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-group-addafter
  .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
  border-color: transparent;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-group-addafter
  .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-group-addafter
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-group-addafter
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-group-addbefore
  .item-style {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-group-addbefore
  .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
  border-color: transparent;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-group-addbefore
  .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-group-addbefore
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-group-addbefore
  .arco-select.arco-select-single
  .arco-select-view,
.arco-input-group-wrapper.arco-input-custom-height .arco-input-inner-wrapper,
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-inner-wrapper
  .arco-input,
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-inner-wrapper
  .arco-input-clear-wrapper,
.arco-input-group-wrapper.arco-input-custom-height
  .arco-input-inner-wrapper
  .arco-input-clear-wrapper
  .arco-input,
.arco-input-group-wrapper .arco-input-inner-wrapper {
  height: 100%;
}
.arco-input-group-wrapper.arco-input-disabled {
  cursor: not-allowed;
}
.arco-input-mirror {
  position: absolute;
  top: 0;
  left: 0;
  visibility: hidden;
}
.arco-textarea {
  outline: none;
  appearance: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  width: 100%;
  border-radius: var(--border-radius-small);
  color: var(--color-text-1);
  box-sizing: border-box;
  transition: color 0.1s cubic-bezier(0, 0, 1, 1),
    border-color 0.1s cubic-bezier(0, 0, 1, 1),
    background-color 0.1s cubic-bezier(0, 0, 1, 1);
  border: 1px solid transparent;
  background-color: var(--color-fill-2);
  font-size: 14px;
  vertical-align: top;
  position: relative;
  padding: 4px 12px;
  max-width: 100%;
  min-height: 32px;
  height: auto;
  line-height: 1.5715;
  resize: vertical;
  overflow: auto;
}
.arco-textarea::placeholder {
  color: var(--color-text-3);
}
.arco-textarea:hover {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-textarea:focus,
.arco-textarea.arco-textarea-focus {
  border-color: rgb(var(--primary-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-textarea-error {
  border-color: transparent;
  background-color: var(--color-danger-light-1);
}
.arco-textarea-error:hover {
  border-color: transparent;
  background-color: var(--color-danger-light-2);
}
.arco-textarea-error .arco-input,
.arco-textarea-error .arco-input:hover {
  background: none;
  box-shadow: none;
}
.arco-textarea-error.arco-textarea-focus,
.arco-textarea-error.arco-textarea-focus:hover {
  border-color: rgb(var(--danger-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-textarea-error:focus,
.arco-textarea-error:focus:hover {
  border-color: rgb(var(--danger-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-textarea-warning {
  border-color: transparent;
  background-color: var(--color-warning-light-1);
}
.arco-textarea-warning:hover {
  border-color: transparent;
  background-color: var(--color-warning-light-2);
}
.arco-textarea-warning .arco-input,
.arco-textarea-warning .arco-input:hover {
  background: none;
  box-shadow: none;
}
.arco-textarea-warning.arco-textarea-focus,
.arco-textarea-warning.arco-textarea-focus:hover {
  border-color: rgb(var(--warning-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-textarea-warning:focus,
.arco-textarea-warning:focus:hover {
  border-color: rgb(var(--warning-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-textarea-disabled {
  background-color: var(--color-fill-2);
  cursor: not-allowed;
  color: var(--color-text-4);
  -webkit-text-fill-color: var(--color-text-4);
  border-color: transparent;
}
.arco-textarea-disabled:hover {
  border-color: transparent;
  background-color: var(--color-fill-2);
  color: var(--color-text-4);
}
.arco-textarea-disabled::placeholder {
  color: var(--color-text-4);
}
.arco-input-group.arco-input-group-compact > .arco-select {
  vertical-align: unset;
}
.arco-input-group.arco-input-group-compact > .arco-select .arco-select-view {
  border-radius: 0;
}
.arco-input-group.arco-input-group-compact > * {
  border-radius: 0;
}
.arco-input-group.arco-input-group-compact > * .arco-input-group > :last-child,
.arco-input-group.arco-input-group-compact
  > *
  .arco-input-group
  > :first-child {
  border-radius: 0;
}
.arco-input-group.arco-input-group-compact > *:not(:last-child) {
  position: relative;
  border-right: 1px solid var(--color-neutral-3);
  box-sizing: border-box;
}
.arco-input-group.arco-input-group-compact > *:first-child,
.arco-input-group.arco-input-group-compact
  > *:first-child
  .arco-input-group
  > *:first-child {
  border-top-left-radius: var(--border-radius-small);
  border-bottom-left-radius: var(--border-radius-small);
}
.arco-input-group.arco-input-group-compact
  > *:first-child
  .arco-select
  .arco-select-view,
.arco-input-group.arco-input-group-compact
  > *:first-child
  .arco-input-group
  > *:first-child
  .arco-select
  .arco-select-view {
  border-top-left-radius: var(--border-radius-small);
  border-bottom-left-radius: var(--border-radius-small);
}
.arco-input-group.arco-input-group-compact > *:last-child,
.arco-input-group.arco-input-group-compact
  > *:last-child
  .arco-input-group
  > *:last-child {
  border-top-right-radius: var(--border-radius-small);
  border-bottom-right-radius: var(--border-radius-small);
}
.arco-input-group.arco-input-group-compact
  > *:last-child
  .arco-select
  .arco-select-view,
.arco-input-group.arco-input-group-compact
  > *:last-child
  .arco-input-group
  > *:last-child
  .arco-select
  .arco-select-view {
  border-top-right-radius: var(--border-radius-small);
  border-bottom-right-radius: var(--border-radius-small);
}
.arco-input-group.arco-input-group-compact > .arco-input:not(:last-child) {
  border-right-color: var(--color-neutral-3);
}
.arco-input-group.arco-input-group-compact
  > .arco-input:not(:last-child):focus {
  border-right-color: rgb(var(--primary-6));
}
.size-height-size-mini {
  line-height: 1.667;
  font-size: 12px;
  padding-top: 1px;
  padding-bottom: 1px;
}
.size-height-size-small {
  padding-top: 2px;
  padding-bottom: 2px;
  font-size: 14px;
}
.size-height-size-large {
  padding-top: 6px;
  padding-bottom: 6px;
  font-size: 14px;
}
.size-height-group-wrapper-mini .arco-input-group-addbefore,
.size-height-group-wrapper-mini .arco-input-group-addafter,
.size-height-inner-wrapper.size-height-inner-wrapper-mini,
.size-height-size-mini {
  padding-left: 8px;
  padding-right: 8px;
}
.size-height-group-wrapper-small .arco-input-group-addbefore,
.size-height-group-wrapper-small .arco-input-group-addafter,
.size-height-inner-wrapper.size-height-inner-wrapper-small,
.size-height-size-small {
  padding-left: 12px;
  padding-right: 12px;
}
.size-height-group-wrapper-large .arco-input-group-addbefore,
.size-height-group-wrapper-large .arco-input-group-addafter,
.size-height-inner-wrapper.size-height-inner-wrapper-large,
.size-height-size-large {
  padding-left: 16px;
  padding-right: 16px;
}
.group-size.group-size-mini .arco-input-group,
.group-size.group-size-mini .arco-input-inner-wrapper .arco-input-group-prefix,
.group-size.group-size-mini .arco-input-inner-wrapper .arco-input-group-suffix {
  font-size: 12px;
}
.group-size.group-size-mini
  .arco-input-inner-wrapper
  .arco-input-group-prefix
  > svg,
.group-size.group-size-mini
  .arco-input-inner-wrapper
  .arco-input-group-suffix
  > svg {
  font-size: 12px;
}
.group-size.group-size-mini .arco-input-group-addbefore,
.group-size.group-size-mini .arco-input-group-addafter {
  font-size: 12px;
  height: 22px;
}
.group-size.group-size-mini .arco-input-group-addbefore > svg,
.group-size.group-size-mini .arco-input-group-addafter > svg {
  font-size: 12px;
}
.group-size.group-size-mini .arco-input-group-addafter .item-style {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.group-size.group-size-mini .arco-input-group-addafter .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
  border-color: transparent;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.group-size.group-size-mini .arco-input-group-addafter .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.group-size.group-size-mini
  .arco-input-group-addafter
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.group-size.group-size-mini
  .arco-input-group-addafter
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.group-size.group-size-mini .arco-input-group-addbefore .item-style {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.group-size.group-size-mini .arco-input-group-addbefore .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
  border-color: transparent;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.group-size.group-size-mini .arco-input-group-addbefore .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.group-size.group-size-mini
  .arco-input-group-addbefore
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.group-size.group-size-mini
  .arco-input-group-addbefore
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.group-size.group-size-small .arco-input-group,
.group-size.group-size-small .arco-input-inner-wrapper .arco-input-group-prefix,
.group-size.group-size-small
  .arco-input-inner-wrapper
  .arco-input-group-suffix {
  font-size: 14px;
}
.group-size.group-size-small
  .arco-input-inner-wrapper
  .arco-input-group-prefix
  > svg,
.group-size.group-size-small
  .arco-input-inner-wrapper
  .arco-input-group-suffix
  > svg {
  font-size: 14px;
}
.group-size.group-size-small .arco-input-group-addbefore,
.group-size.group-size-small .arco-input-group-addafter {
  font-size: 14px;
  height: 26px;
}
.group-size.group-size-small .arco-input-group-addbefore > svg,
.group-size.group-size-small .arco-input-group-addafter > svg {
  font-size: 14px;
}
.group-size.group-size-small .arco-input-group-addafter .item-style {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.group-size.group-size-small .arco-input-group-addafter .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
  border-color: transparent;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.group-size.group-size-small .arco-input-group-addafter .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.group-size.group-size-small
  .arco-input-group-addafter
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.group-size.group-size-small
  .arco-input-group-addafter
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.group-size.group-size-small .arco-input-group-addbefore .item-style {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.group-size.group-size-small .arco-input-group-addbefore .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
  border-color: transparent;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.group-size.group-size-small .arco-input-group-addbefore .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.group-size.group-size-small
  .arco-input-group-addbefore
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.group-size.group-size-small
  .arco-input-group-addbefore
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.group-size.group-size-large .arco-input-group,
.group-size.group-size-large .arco-input-inner-wrapper .arco-input-group-prefix,
.group-size.group-size-large
  .arco-input-inner-wrapper
  .arco-input-group-suffix {
  font-size: 14px;
}
.group-size.group-size-large
  .arco-input-inner-wrapper
  .arco-input-group-prefix
  > svg,
.group-size.group-size-large
  .arco-input-inner-wrapper
  .arco-input-group-suffix
  > svg {
  font-size: 14px;
}
.group-size.group-size-large .arco-input-group-addbefore,
.group-size.group-size-large .arco-input-group-addafter {
  font-size: 14px;
  height: 34px;
}
.group-size.group-size-large .arco-input-group-addbefore > svg,
.group-size.group-size-large .arco-input-group-addafter > svg {
  font-size: 14px;
}
.group-size.group-size-large .arco-input-group-addafter .item-style {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.group-size.group-size-large .arco-input-group-addafter .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
  border-color: transparent;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.group-size.group-size-large .arco-input-group-addafter .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.group-size.group-size-large
  .arco-input-group-addafter
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.group-size.group-size-large
  .arco-input-group-addafter
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.group-size.group-size-large .arco-input-group-addbefore .item-style {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.group-size.group-size-large .arco-input-group-addbefore .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
  border-color: transparent;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.group-size.group-size-large .arco-input-group-addbefore .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.group-size.group-size-large
  .arco-input-group-addbefore
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.group-size.group-size-large
  .arco-input-group-addbefore
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.group-size.arco-input-custom-height .arco-input-group,
.group-size.arco-input-custom-height
  .arco-input-inner-wrapper
  .arco-input-group-prefix,
.group-size.arco-input-custom-height
  .arco-input-inner-wrapper
  .arco-input-group-suffix {
  font-size: 14px;
}
.group-size.arco-input-custom-height
  .arco-input-inner-wrapper
  .arco-input-group-prefix
  > svg,
.group-size.arco-input-custom-height
  .arco-input-inner-wrapper
  .arco-input-group-suffix
  > svg {
  font-size: 14px;
}
.group-size.arco-input-custom-height .arco-input-group-addbefore,
.group-size.arco-input-custom-height .arco-input-group-addafter {
  font-size: 14px;
  height: 22px;
}
.group-size.arco-input-custom-height .arco-input-group-addbefore > svg,
.group-size.arco-input-custom-height .arco-input-group-addafter > svg {
  font-size: 14px;
}
.group-size.arco-input-custom-height .arco-input-group-addafter .item-style {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.group-size.arco-input-custom-height .arco-input-group-addafter .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
  border-color: transparent;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.group-size.arco-input-custom-height .arco-input-group-addafter .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.group-size.arco-input-custom-height
  .arco-input-group-addafter
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.group-size.arco-input-custom-height
  .arco-input-group-addafter
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.group-size.arco-input-custom-height .arco-input-group-addbefore .item-style {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.group-size.arco-input-custom-height .arco-input-group-addbefore .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
  border-color: transparent;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.group-size.arco-input-custom-height .arco-input-group-addbefore .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.group-size.arco-input-custom-height
  .arco-input-group-addbefore
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.group-size.arco-input-custom-height
  .arco-input-group-addbefore
  .arco-select.arco-select-single
  .arco-select-view,
.group-size.arco-input-custom-height .arco-input-inner-wrapper,
.group-size.arco-input-custom-height .arco-input-inner-wrapper .arco-input,
.group-size.arco-input-custom-height
  .arco-input-inner-wrapper
  .arco-input-clear-wrapper,
.group-size.arco-input-custom-height
  .arco-input-inner-wrapper
  .arco-input-clear-wrapper
  .arco-input {
  height: 100%;
}
.arco-textarea-wrapper {
  display: inline-block;
  position: relative;
  width: 100%;
}
.arco-textarea-clear-wrapper:hover .arco-textarea-clear-icon {
  display: inline-block;
}
.arco-textarea-clear-wrapper .arco-textarea {
  padding-right: 20px;
}
.arco-textarea-word-limit {
  position: absolute;
  font-size: 12px;
  bottom: 6px;
  right: 10px;
  color: var(--color-text-3);
  user-select: none;
}
.arco-textarea-word-limit-error {
  color: rgb(var(--danger-6));
}
.arco-textarea-clear-icon {
  display: none;
  position: absolute;
  right: 10px;
  top: 10px;
  font-size: 12px;
  color: var(--color-text-2);
}
.arco-textarea-clear-icon > svg {
  transition: color 0.1s cubic-bezier(0, 0, 1, 1);
  position: relative;
}
.arco-input-search.arco-input-group-wrapper .arco-input-group-addbefore {
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-input-search.arco-input-group-wrapper .arco-input-group-addafter {
  padding: 0;
  border: none;
}
.arco-input-search.arco-input-group-wrapper .arco-input-group-suffix {
  color: var(--color-text-2);
  font-size: 14px;
}
.arco-input-search.arco-input-group-wrapper:not(.arco-input-disabled)
  .arco-input-group-addbefore {
  cursor: pointer;
  color: var(--color-text-2);
  font-size: 14px;
}
.arco-input-search.arco-input-group-wrapper .arco-input-search-btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  height: 100%;
  font-size: 14px;
  color: var(--color-white);
}
.arco-input-search-button
  .arco-input-inner-wrapper:not(.arco-input-inner-wrapper-rtl) {
  border-right: none;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.arco-input-password.arco-input-group-wrapper:not(.arco-input-disabled)
  .arco-input-group-suffix {
  cursor: pointer;
  color: var(--color-text-2);
  font-size: 12px;
}
.arco-input-password.arco-input-group-wrapper
  .arco-input-password-visibility-icon:focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--primary-6));
  border-radius: var(--border-radius-small);
}
.arco-input-group-wrapper-rtl {
  direction: rtl;
}
.arco-input-group-wrapper-rtl .arco-input-word-limit {
  padding-left: 0;
  padding-right: input-padding-word-limit-left;
}
.arco-input-group-wrapper-rtl.arco-input-clear-wrapper .arco-input {
  padding-right: 0;
  padding-left: 24px;
}
.arco-input-group-wrapper-rtl .arco-input-group > :first-child {
  border-radius: 0 var(--border-radius-small) var(--border-radius-small) 0;
}
.arco-input-group-wrapper-rtl .arco-input-group > :last-child {
  border-radius: var(--border-radius-small) 0 0 var(--border-radius-small);
}
.arco-input-group-wrapper-rtl .arco-input-group-addafter {
  border-left: none;
  border-right: 1px solid var(--color-neutral-3);
}
.arco-input-group-wrapper-rtl .arco-input-group-addafter .item-style {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.arco-input-group-wrapper-rtl .arco-input-group-addafter .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
  border-color: transparent;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.arco-input-group-wrapper-rtl .arco-input-group-addafter .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -12px -1px -13px;
}
.arco-input-group-wrapper-rtl
  .arco-input-group-addafter
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.arco-input-group-wrapper-rtl
  .arco-input-group-addafter
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.arco-input-group-wrapper-rtl .arco-input-group-addbefore {
  border-right: none;
  border-left: 1px solid var(--color-neutral-3);
}
.arco-input-group-wrapper-rtl .arco-input-group-addbefore .item-style {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.arco-input-group-wrapper-rtl .arco-input-group-addbefore .arco-input {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
  border-color: transparent;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.arco-input-group-wrapper-rtl .arco-input-group-addbefore .arco-select {
  width: auto;
  height: 100%;
  margin: -1px -13px -1px -12px;
}
.arco-input-group-wrapper-rtl
  .arco-input-group-addbefore
  .arco-select
  .arco-select-view {
  border-radius: 0;
  background-color: inherit;
  border-color: transparent;
}
.arco-input-group-wrapper-rtl
  .arco-input-group-addbefore
  .arco-select.arco-select-single
  .arco-select-view {
  height: 100%;
}
.arco-input-group-wrapper-rtl
  .arco-input-group.arco-input-group-compact
  > *:not(:last-child) {
  border-right: none;
  border-left: 1px solid var(--color-neutral-3);
}
.arco-input-group-wrapper-rtl
  .arco-input-group.arco-input-group-compact
  > *:first-child,
.arco-input-group-wrapper-rtl
  .arco-input-group.arco-input-group-compact
  > *:first-child
  .arco-input-group
  > *:first-child {
  border-radius: 0 var(--border-radius-small) var(--border-radius-small) 0;
}
.arco-input-group-wrapper-rtl
  .arco-input-group.arco-input-group-compact
  > *:first-child
  .arco-select
  .arco-select-view,
.arco-input-group-wrapper-rtl
  .arco-input-group.arco-input-group-compact
  > *:first-child
  .arco-input-group
  > *:first-child
  .arco-select
  .arco-select-view {
  border-radius: 0 var(--border-radius-small) var(--border-radius-small) 0;
}
.arco-input-group-wrapper-rtl
  .arco-input-group.arco-input-group-compact
  > *:last-child,
.arco-input-group-wrapper-rtl
  .arco-input-group.arco-input-group-compact
  > *:last-child
  .arco-input-group
  > *:last-child {
  border-radius: var(--border-radius-small) 0 0 var(--border-radius-small);
}
.arco-input-group-wrapper-rtl
  .arco-input-group.arco-input-group-compact
  > *:last-child
  .arco-select
  .arco-select-view,
.arco-input-group-wrapper-rtl
  .arco-input-group.arco-input-group-compact
  > *:last-child
  .arco-input-group
  > *:last-child
  .arco-select
  .arco-select-view {
  border-radius: var(--border-radius-small) 0 0 var(--border-radius-small);
}
.arco-input-group-wrapper-rtl
  .arco-input-group.arco-input-group-compact
  > .arco-input:not(:last-child) {
  border-left-color: var(--color-neutral-3);
}
.arco-input-group-wrapper-rtl
  .arco-input-group.arco-input-group-compact
  > .arco-input:not(:last-child):focus {
  border-left-color: rgb(var(--primary-6));
}
.arco-input-group-wrapper-rtl.arco-input-search .arco-input-search-btn {
  border-radius: var(--border-radius-small) 0 0 var(--border-radius-small);
}
.arco-input-inner-wrapper-rtl {
  direction: rtl;
}
.arco-input-inner-wrapper-rtl.arco-input-inner-wrapper-has-prefix
  > .arco-input-clear-wrapper
  .arco-input,
.arco-input-inner-wrapper-rtl.arco-input-inner-wrapper-has-prefix
  > .arco-input {
  padding-left: 0;
  padding-right: 12px;
}
.arco-input-inner-wrapper-rtl > .arco-input-clear-wrapper .arco-input {
  padding-right: 0;
  padding-left: 12px;
}
.arco-input-inner-wrapper-rtl
  > .arco-input-clear-wrapper
  .arco-input-clear-icon {
  right: initial;
  left: 8px;
}
.arco-input-inner-wrapper-rtl:hover
  .arco-input-clear-icon
  ~ .arco-input-group-suffix {
  margin-left: 4px;
}
.arco-input-search-button .arco-input-inner-wrapper-rtl {
  border-left: none;
  border-radius: 0 var(--border-radius-small) var(--border-radius-small) 0;
}
.arco-textarea-wrapper-rtl {
  direction: rtl;
}
.arco-textarea-wrapper-rtl .arco-textarea {
  padding-left: 20px;
}
.arco-textarea-wrapper-rtl .arco-textarea-word-limit,
.arco-textarea-wrapper-rtl .arco-textarea-clear-icon {
  right: initial;
  left: 10px;
}
.arco-icon-hover.arco-tabs-icon-hover:before {
  width: 16px;
  height: 16px;
}
.arco-tabs .arco-tabs-icon-hover {
  font-size: 12px;
  color: var(--color-text-2);
  user-select: none;
}
.arco-tabs-dropdown-icon {
  font-size: 12px;
  margin-left: 6px;
  user-select: none;
}
.arco-tabs-close-icon {
  margin-left: 8px;
  user-select: none;
}
.arco-tabs-close-icon:focus-visible .arco-icon-hover:before {
  box-shadow: 0 0 0 2px var(--color-primary-light-3);
}
.arco-tabs-add-icon {
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  user-select: none;
  justify-content: center;
  padding: 0 8px;
}
.arco-tabs-add-icon:focus-visible .arco-icon-hover:before {
  box-shadow: 0 0 0 2px var(--color-primary-light-3);
}
.arco-tabs-add {
  position: relative;
}
.arco-tabs-left-icon {
  margin-left: 10px;
  margin-right: 6px;
}
.arco-tabs-right-icon {
  margin-right: 10px;
  margin-left: 6px;
}
.arco-tabs-up-icon {
  margin-bottom: 10px;
}
.arco-tabs-down-icon {
  margin-top: 10px;
}
.arco-tabs .arco-tabs-nav-icon-disabled {
  cursor: not-allowed;
  color: var(--color-text-4);
}
.arco-tabs {
  position: relative;
  overflow: hidden;
}
.arco-tabs-header-nav {
  position: relative;
}
.arco-tabs-header-nav:before {
  content: '';
  clear: both;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--color-neutral-3);
  display: block;
}
.arco-tabs-header-nav-bottom:before {
  top: 0;
}
.arco-tabs-header-nav-bottom .arco-tabs-header-ink {
  top: 0;
}
.arco-tabs-header-nav-bottom .arco-tabs-header-ink .arco-tabs-header-ink-inner {
  bottom: unset;
  top: 0;
}
.arco-tabs-header-wrapper {
  display: flex;
  overflow: hidden;
  flex: 1;
}
.arco-tabs-header {
  position: relative;
  display: inline-block;
  transition: transform 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
  white-space: nowrap;
}
.arco-tabs-header-extra {
  width: auto;
  display: flex;
  align-items: center;
  line-height: 32px;
  flex-shrink: 0;
}
.arco-tabs-header-extra .arco-tabs-add-icon {
  padding-left: 0;
}
.arco-tabs-header-title {
  box-sizing: border-box;
  font-size: 14px;
  cursor: pointer;
  color: var(--color-text-2);
  transition: color 0.2s cubic-bezier(0, 0, 1, 1);
  display: inline-flex;
  align-items: center;
  line-height: 1.5715;
  padding: 4px 0;
}
.arco-tabs-header-title-text {
  display: inline-block;
}
.arco-tabs-header-title:hover {
  color: var(--color-text-2);
  font-weight: 400;
}
.arco-tabs-header-title-disabled,
.arco-tabs-header-title-disabled:hover {
  color: var(--color-text-4);
  cursor: not-allowed;
}
.arco-tabs-header-title-active,
.arco-tabs-header-title-active:hover {
  color: rgb(var(--primary-6));
  font-weight: 500;
}
.arco-tabs-header-title-active.arco-tabs-header-title-disabled,
.arco-tabs-header-title-active:hover.arco-tabs-header-title-disabled {
  color: var(--color-primary-light-3);
}
.arco-tabs-header-ink {
  position: absolute;
  bottom: 0;
  right: initial;
  top: initial;
  height: 2px;
  background-color: rgb(var(--primary-6));
  transition: left 0.2s cubic-bezier(0.34, 0.69, 0.1, 1),
    width 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-tabs-header-ink.arco-tabs-header-ink-no-animation {
  transition: none;
}
.arco-tabs-header-ink.arco-tabs-header-ink-custom {
  background-color: transparent;
}
.arco-tabs-header-ink.arco-tabs-header-ink-custom .arco-tabs-header-ink-inner {
  background-color: rgb(var(--primary-6));
  position: absolute;
  left: 50%;
  bottom: 0;
  width: 100%;
  height: 100%;
  transform: translate(-50%);
}
.arco-tabs-header-ink-disabled {
  background-color: var(--color-primary-light-3);
}
.arco-tabs-header-nav-line .arco-tabs-header-extra {
  line-height: 40px;
}
.arco-tabs-header-nav-line .arco-tabs-header-title {
  line-height: 1.5715;
  margin: 0 16px;
  padding: 8px 0;
}
.arco-tabs-header-nav-line .arco-tabs-header-title-text {
  display: inline-block;
  position: relative;
  padding: 1px 0;
}
.arco-tabs-header-nav-line .arco-tabs-header-title-text:before {
  content: '';
  z-index: -1;
  opacity: 1;
  transition: all 0.2s cubic-bezier(0, 0, 1, 1);
  border-radius: var(--border-radius-small);
  position: absolute;
  top: 0;
  bottom: 0;
  left: -8px;
  right: -8px;
  background-color: transparent;
}
.arco-tabs-header-nav-line
  .arco-tabs-header-title:hover
  .arco-tabs-header-title-text:before {
  background-color: var(--color-fill-2);
}
.arco-tabs-header-nav-line
  .arco-tabs-header-title:focus-visible
  .arco-tabs-header-title-text:before {
  box-shadow: inset 0 0 0 2px var(--color-primary-light-3);
}
.arco-tabs-header-nav-line
  .arco-tabs-header-title-active
  .arco-tabs-header-title-text:before,
.arco-tabs-header-nav-line
  .arco-tabs-header-title-active:hover
  .arco-tabs-header-title-text:before {
  background-color: transparent;
}
.arco-tabs-header-nav-line
  .arco-tabs-header-title-disabled
  .arco-tabs-header-title-text:before,
.arco-tabs-header-nav-line
  .arco-tabs-header-title-disabled:hover
  .arco-tabs-header-title-text:before {
  opacity: 0;
}
.arco-tabs-header-nav-line.arco-tabs-header-nav-horizontal
  > .arco-tabs-header-scroll
  .arco-tabs-header-title:first-of-type {
  margin-left: 16px;
}
.arco-tabs-header-nav-line.arco-tabs-header-nav-horizontal
  .arco-tabs-header-no-padding
  > .arco-tabs-header-title:first-of-type,
.arco-tabs-header-nav-text.arco-tabs-header-nav-horizontal
  .arco-tabs-header-no-padding
  > .arco-tabs-header-title:first-of-type {
  margin-left: 0;
}
.arco-tabs-header-nav-card .arco-tabs-header-title,
.arco-tabs-header-nav-card-gutter .arco-tabs-header-title {
  font-size: 14px;
  border: 1px solid var(--color-neutral-3);
  transition: padding 0.2s cubic-bezier(0, 0, 1, 1),
    color 0.2s cubic-bezier(0, 0, 1, 1);
  padding: 4px 16px;
}
.arco-tabs-header-nav-card .arco-tabs-header-title:focus-visible,
.arco-tabs-header-nav-card-gutter .arco-tabs-header-title:focus-visible {
  box-shadow: inset 0 0 0 2px var(--color-primary-light-3);
}
.arco-tabs-header-nav-card .arco-tabs-header-title-editable,
.arco-tabs-header-nav-card-gutter .arco-tabs-header-title-editable {
  padding-right: 12px;
}
.arco-tabs-header-nav-card
  .arco-tabs-header-title-editable:not(.arco-tabs-header-title-active):hover
  .arco-icon-hover:hover:before,
.arco-tabs-header-nav-card-gutter
  .arco-tabs-header-title-editable:not(.arco-tabs-header-title-active):hover
  .arco-icon-hover:hover:before {
  background-color: var(--color-fill-4);
}
.arco-tabs-header-nav-card .arco-tabs-add-icon,
.arco-tabs-header-nav-card-gutter .arco-tabs-add-icon {
  color: var(--color-text-2);
  height: 32px;
}
.arco-tabs-header-nav-card .arco-tabs-header-title {
  border-right: none;
  background-color: transparent;
}
.arco-tabs-header-nav-card .arco-tabs-header-title:last-child {
  border-right: 1px solid var(--color-neutral-3);
  border-top-right-radius: var(--border-radius-small);
}
.arco-tabs-header-nav-card .arco-tabs-header-title:first-child {
  border-top-left-radius: var(--border-radius-small);
}
.arco-tabs-header-nav-card .arco-tabs-header-title:hover {
  background-color: var(--color-fill-3);
}
.arco-tabs-header-nav-card .arco-tabs-header-title-disabled,
.arco-tabs-header-nav-card .arco-tabs-header-title-disabled:hover {
  background-color: transparent;
}
.arco-tabs-header-nav-card .arco-tabs-header-title-active,
.arco-tabs-header-nav-card .arco-tabs-header-title-active:hover {
  border-bottom-color: var(--color-bg-2);
  background-color: transparent;
}
.arco-tabs-header-nav-card.arco-tabs-header-nav-bottom
  .arco-tabs-header-title-active,
.arco-tabs-header-nav-card.arco-tabs-header-nav-bottom
  .arco-tabs-header-title-active:hover {
  border-top-color: var(--color-bg-2);
  border-bottom-color: var(--color-neutral-3);
}
.arco-tabs-header-nav-card-gutter .arco-tabs-header-title {
  margin-left: 4px;
  border-right: 1px solid var(--color-neutral-3);
  background-color: var(--color-fill-1);
  border-radius: var(--border-radius-small) var(--border-radius-small) 0 0;
}
.arco-tabs-header-nav-card-gutter .arco-tabs-header-title:hover {
  background-color: var(--color-fill-3);
}
.arco-tabs-header-nav-card-gutter .arco-tabs-header-title-disabled,
.arco-tabs-header-nav-card-gutter .arco-tabs-header-title-disabled:hover {
  background-color: var(--color-fill-1);
}
.arco-tabs-header-nav-card-gutter .arco-tabs-header-title-active,
.arco-tabs-header-nav-card-gutter .arco-tabs-header-title-active:hover {
  border-bottom-color: var(--color-bg-2);
  background-color: transparent;
}
.arco-tabs-header-nav-card-gutter .arco-tabs-header-title:first-child {
  margin-left: 0;
}
.arco-tabs-header-nav-card-gutter.arco-tabs-header-nav-bottom
  .arco-tabs-header-title-active,
.arco-tabs-header-nav-card-gutter.arco-tabs-header-nav-bottom
  .arco-tabs-header-title-active:hover {
  border-top-color: var(--color-bg-2);
  border-bottom-color: var(--color-neutral-3);
}
.arco-tabs-header-nav-text:before {
  display: none;
}
.arco-tabs-header-nav-text .arco-tabs-header-title {
  position: relative;
  line-height: 1.5715;
  margin: 0 9px;
  font-size: 14px;
  padding: 5px 0;
}
.arco-tabs-header-nav-text .arco-tabs-header-title:not(:first-of-type):before {
  height: 12px;
  position: absolute;
  width: 2px;
  background-color: var(--color-fill-3);
  content: '';
  display: block;
  top: 50%;
  transform: translateY(-50%);
  left: -9px;
}
.arco-tabs-header-nav-text .arco-tabs-header-title-text {
  padding-left: 8px;
  padding-right: 8px;
  background-color: transparent;
}
.arco-tabs-header-nav-text .arco-tabs-header-title-text:hover {
  background-color: var(--color-fill-2);
}
.arco-tabs-header-nav-text
  .arco-tabs-header-title:focus-visible
  .arco-tabs-header-title-text {
  box-shadow: inset 0 0 0 2px var(--color-primary-light-3);
}
.arco-tabs-header-nav-text
  .arco-tabs-header-title-active
  .arco-tabs-header-title-text,
.arco-tabs-header-nav-text
  .arco-tabs-header-title-active
  .arco-tabs-header-title-text:hover,
.arco-tabs-header-nav-text
  .arco-tabs-header-title-disabled
  .arco-tabs-header-title-text,
.arco-tabs-header-nav-text
  .arco-tabs-header-title-disabled
  .arco-tabs-header-title-text:hover {
  background-color: transparent;
}
.arco-tabs-header-nav-text
  .arco-tabs-header-title-active.arco-tabs-header-nav-text
  .arco-tabs-header-title-disabled
  .arco-tabs-header-title-text,
.arco-tabs-header-nav-text
  .arco-tabs-header-title-active.arco-tabs-header-nav-text
  .arco-tabs-header-title-disabled
  .arco-tabs-header-title-text:hover {
  background-color: var(--color-primary-light-3);
}
.arco-tabs-header-nav-rounded:before {
  display: none;
}
.arco-tabs-header-nav-rounded .arco-tabs-header-title {
  padding: 5px 16px;
  margin: 0 6px;
  font-size: 14px;
  background-color: transparent;
  border-radius: 32px;
}
.arco-tabs-header-nav-rounded .arco-tabs-header-title:hover {
  background-color: var(--color-fill-2);
}
.arco-tabs-header-nav-rounded .arco-tabs-header-title:focus-visible {
  box-shadow: inset 0 0 0 2px var(--color-primary-light-3);
}
.arco-tabs-header-nav-rounded .arco-tabs-header-title-disabled:hover {
  background-color: transparent;
}
.arco-tabs-header-nav-rounded .arco-tabs-header-title-active,
.arco-tabs-header-nav-rounded .arco-tabs-header-title-active:hover {
  background-color: var(--color-fill-2);
}
.arco-tabs-header-nav-capsule:before {
  display: none;
}
.arco-tabs-header-nav-capsule .arco-tabs-header-wrapper {
  justify-content: flex-end;
}
.arco-tabs-header-nav-capsule .arco-tabs-header {
  line-height: 1;
  background-color: var(--color-fill-2);
  border-radius: var(--border-radius-small);
  padding: 3px;
}
.arco-tabs-header-nav-capsule .arco-tabs-header-title {
  line-height: 26px;
  padding: 0 12px;
  background-color: transparent;
  position: relative;
  font-size: 14px;
  border-radius: var(--border-radius-small);
}
.arco-tabs-header-nav-capsule .arco-tabs-header-title:focus-visible {
  box-shadow: inset 0 0 0 2px var(--color-primary-light-3);
}
.arco-tabs-header-nav-capsule .arco-tabs-header-title:hover {
  background-color: var(--color-bg-2);
}
.arco-tabs-header-nav-capsule .arco-tabs-header-title-disabled:hover {
  background-color: unset;
}
.arco-tabs-header-nav-capsule .arco-tabs-header-title-active,
.arco-tabs-header-nav-capsule .arco-tabs-header-title-active:hover {
  background-color: var(--color-bg-2);
}
.arco-tabs-header-nav-capsule .arco-tabs-header-title-active:before,
.arco-tabs-header-nav-capsule .arco-tabs-header-title-active:hover:before,
.arco-tabs-header-nav-capsule
  .arco-tabs-header-title-active
  + .arco-tabs-header-title:before,
.arco-tabs-header-nav-capsule
  .arco-tabs-header-title-active:hover
  + .arco-tabs-header-title:before {
  opacity: 0;
}
.arco-tabs-header-nav-capsule.arco-tabs-header-nav-horizontal
  .arco-tabs-header-title:not(:first-of-type) {
  margin-left: 3px;
}
.arco-tabs-header-nav-capsule.arco-tabs-header-nav-horizontal
  .arco-tabs-header-title:not(:first-of-type):before {
  position: absolute;
  top: 50%;
  left: -2px;
  transform: translateY(-50%);
  display: block;
  height: 14px;
  width: 1px;
  background-color: var(--color-fill-3);
  content: '';
  transition: all 0.2s cubic-bezier(0, 0, 1, 1);
}
.arco-tabs-header-scroll {
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
}
.arco-tabs-content {
  width: 100%;
  overflow: hidden;
  padding-top: 16px;
  box-sizing: border-box;
}
.arco-tabs-content .arco-tabs-content-inner {
  display: flex;
  width: 100%;
}
.arco-tabs-content .arco-tabs-content-item {
  width: 100%;
  overflow: hidden;
  height: 0;
  flex-shrink: 0;
}
.arco-tabs-content .arco-tabs-content-item.arco-tabs-content-item-active {
  height: auto;
}
.arco-tabs-card > .arco-tabs-content,
.arco-tabs-card-gutter > .arco-tabs-content {
  border: 1px solid var(--color-neutral-3);
  border-top: none;
}
.arco-tabs-card.arco-tabs-bottom > .arco-tabs-content,
.arco-tabs-card-gutter.arco-tabs-bottom > .arco-tabs-content {
  border-top: 1px solid var(--color-neutral-3);
  border-bottom: none;
}
.arco-tabs-animation.arco-tabs-content-inner {
  transition: all 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-tabs-horizontal.arco-tabs-justify {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.arco-tabs-horizontal.arco-tabs-justify .arco-tabs-content,
.arco-tabs-horizontal.arco-tabs-justify .arco-tabs-content-inner,
.arco-tabs-horizontal.arco-tabs-justify .arco-tabs-pane {
  height: 100%;
}
.arco-tabs-header-size-mini.arco-tabs-header-nav-line .arco-tabs-header-title {
  font-size: 12px;
  padding-top: 6px;
  padding-bottom: 6px;
}
.arco-tabs-header-size-mini.arco-tabs-header-nav-line .arco-tabs-header-extra {
  line-height: 32px;
  font-size: 12px;
}
.arco-tabs-header-size-mini.arco-tabs-header-nav-card .arco-tabs-header-title,
.arco-tabs-header-size-mini.arco-tabs-header-nav-card-gutter
  .arco-tabs-header-title {
  font-size: 12px;
  padding-top: 2px;
  padding-bottom: 2px;
}
.arco-tabs-header-size-mini.arco-tabs-header-nav-card .arco-tabs-header-extra,
.arco-tabs-header-size-mini.arco-tabs-header-nav-card-gutter
  .arco-tabs-header-extra {
  line-height: 24px;
  font-size: 12px;
}
.arco-tabs-header-size-mini.arco-tabs-header-nav-card .arco-tabs-add-icon,
.arco-tabs-header-size-mini.arco-tabs-header-nav-card-gutter
  .arco-tabs-add-icon {
  height: 24px;
}
.arco-tabs-header-size-mini.arco-tabs-header-nav-capsule
  .arco-tabs-header-title {
  font-size: 12px;
  line-height: 18px;
}
.arco-tabs-header-size-mini.arco-tabs-header-nav-capsule
  .arco-tabs-header-extra {
  line-height: 24px;
  font-size: 12px;
}
.arco-tabs-header-size-mini.arco-tabs-header-nav-rounded
  .arco-tabs-header-title {
  font-size: 12px;
  padding-top: 3px;
  padding-bottom: 3px;
}
.arco-tabs-header-size-mini.arco-tabs-header-nav-rounded
  .arco-tabs-header-extra {
  line-height: 24px;
  font-size: 12px;
}
.arco-tabs-header-size-small.arco-tabs-header-nav-line .arco-tabs-header-title {
  font-size: 14px;
  padding-top: 6px;
  padding-bottom: 6px;
}
.arco-tabs-header-size-small.arco-tabs-header-nav-line .arco-tabs-header-extra {
  line-height: 36px;
  font-size: 14px;
}
.arco-tabs-header-size-small.arco-tabs-header-nav-card .arco-tabs-header-title,
.arco-tabs-header-size-small.arco-tabs-header-nav-card-gutter
  .arco-tabs-header-title {
  font-size: 14px;
  padding-top: 2px;
  padding-bottom: 2px;
}
.arco-tabs-header-size-small.arco-tabs-header-nav-card .arco-tabs-header-extra,
.arco-tabs-header-size-small.arco-tabs-header-nav-card-gutter
  .arco-tabs-header-extra {
  line-height: 28px;
  font-size: 14px;
}
.arco-tabs-header-size-small.arco-tabs-header-nav-card .arco-tabs-add-icon,
.arco-tabs-header-size-small.arco-tabs-header-nav-card-gutter
  .arco-tabs-add-icon {
  height: 28px;
}
.arco-tabs-header-size-small.arco-tabs-header-nav-capsule
  .arco-tabs-header-title {
  font-size: 14px;
  line-height: 22px;
}
.arco-tabs-header-size-small.arco-tabs-header-nav-capsule
  .arco-tabs-header-extra {
  line-height: 28px;
  font-size: 14px;
}
.arco-tabs-header-size-small.arco-tabs-header-nav-rounded
  .arco-tabs-header-title {
  font-size: 14px;
  padding-top: 3px;
  padding-bottom: 3px;
}
.arco-tabs-header-size-small.arco-tabs-header-nav-rounded
  .arco-tabs-header-extra {
  line-height: 28px;
  font-size: 14px;
}
.arco-tabs-header-size-large.arco-tabs-header-nav-line .arco-tabs-header-title {
  font-size: 14px;
  padding-top: 10px;
  padding-bottom: 10px;
}
.arco-tabs-header-size-large.arco-tabs-header-nav-line .arco-tabs-header-extra {
  line-height: 44px;
  font-size: 14px;
}
.arco-tabs-header-size-large.arco-tabs-header-nav-card .arco-tabs-header-title,
.arco-tabs-header-size-large.arco-tabs-header-nav-card-gutter
  .arco-tabs-header-title {
  font-size: 14px;
  padding-top: 6px;
  padding-bottom: 6px;
}
.arco-tabs-header-size-large.arco-tabs-header-nav-card .arco-tabs-header-extra,
.arco-tabs-header-size-large.arco-tabs-header-nav-card-gutter
  .arco-tabs-header-extra {
  line-height: 36px;
  font-size: 14px;
}
.arco-tabs-header-size-large.arco-tabs-header-nav-card .arco-tabs-add-icon,
.arco-tabs-header-size-large.arco-tabs-header-nav-card-gutter
  .arco-tabs-add-icon {
  height: 36px;
}
.arco-tabs-header-size-large.arco-tabs-header-nav-capsule
  .arco-tabs-header-title {
  font-size: 14px;
  line-height: 30px;
}
.arco-tabs-header-size-large.arco-tabs-header-nav-capsule
  .arco-tabs-header-extra {
  line-height: 36px;
  font-size: 14px;
}
.arco-tabs-header-size-large.arco-tabs-header-nav-rounded
  .arco-tabs-header-title {
  font-size: 14px;
  padding-top: 7px;
  padding-bottom: 7px;
}
.arco-tabs-header-size-large.arco-tabs-header-nav-rounded
  .arco-tabs-header-extra {
  line-height: 36px;
  font-size: 14px;
}
.arco-tabs-right {
  flex-direction: row-reverse;
}
.arco-tabs-vertical {
  display: flex;
  flex-wrap: nowrap;
}
.arco-tabs-header-nav-vertical {
  flex-grow: 0;
  flex-shrink: 0;
}
.arco-tabs-header-nav-vertical:before {
  position: absolute;
  left: initial;
  bottom: 0;
  right: 0;
  top: 0;
  width: 1px;
  height: 100%;
  clear: both;
}
.arco-tabs-header-nav-vertical .arco-tabs-add-icon {
  margin-left: 0;
  margin-top: 8px;
  padding: 0 16px;
  height: auto;
}
.arco-tabs-header-nav-vertical .arco-tabs-header {
  height: auto;
}
.arco-tabs-header-nav-vertical .arco-tabs-header-scroll {
  flex-direction: column;
}
.arco-tabs-header-nav-vertical .arco-tabs-header-overflow-scroll {
  padding: 6px 0;
}
.arco-tabs-header-nav-vertical .arco-tabs-header-wrapper {
  height: 100%;
  flex-direction: column;
}
.arco-tabs-header-nav-vertical .arco-tabs-header-ink {
  position: absolute;
  left: initial;
  right: 0;
  bottom: initial;
  width: 2px;
  transition: top 0.2s cubic-bezier(0.34, 0.69, 0.1, 1),
    height 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-tabs-header-nav-vertical
  .arco-tabs-header-ink.arco-tabs-header-ink-custom
  .arco-tabs-header-ink-inner {
  left: unset;
  bottom: unset;
  right: 0;
  transform: translateY(-50%);
  top: 50%;
}
.arco-tabs-header-nav-vertical .arco-tabs-header-title {
  display: block;
  white-space: nowrap;
  margin: 12px 0 0;
}
.arco-tabs-header-nav-vertical .arco-tabs-header-title:first-of-type {
  margin-top: 0;
}
.arco-tabs-header-nav-right:before {
  right: unset;
  left: 0;
}
.arco-tabs-header-nav-right .arco-tabs-header-ink,
.arco-tabs-header-nav-right .arco-tabs-header-ink .arco-tabs-header-ink-inner {
  left: 0;
  right: unset;
}
.arco-tabs-header-nav-vertical .arco-tabs-header-scroll {
  position: relative;
  height: 100%;
  box-sizing: border-box;
}
.arco-tabs-header-nav-line.arco-tabs-header-nav-vertical
  .arco-tabs-header-title {
  padding: 0 20px;
}
.arco-tabs-header-nav-vertical.arco-tabs-header-nav-card
  .arco-tabs-header-title {
  margin: 0;
  position: relative;
  border: 1px solid var(--color-neutral-3);
  border-bottom-color: transparent;
}
.arco-tabs-header-nav-vertical.arco-tabs-header-nav-card
  .arco-tabs-header-title:first-child {
  border-top-left-radius: var(--border-radius-small);
}
.arco-tabs-header-nav-vertical.arco-tabs-header-nav-card
  .arco-tabs-header-title-active,
.arco-tabs-header-nav-vertical.arco-tabs-header-nav-card
  .arco-tabs-header-title-active:hover {
  border-bottom-color: transparent;
  border-right-color: var(--color-bg-2);
}
.arco-tabs-header-nav-vertical.arco-tabs-header-nav-card
  .arco-tabs-header-title:last-child {
  border-bottom: 1px solid var(--color-neutral-3);
  border-bottom-left-radius: var(--border-radius-small);
}
.arco-tabs-header-nav-vertical.arco-tabs-header-nav-card.arco-tabs-header-nav-right
  .arco-tabs-header-title-active,
.arco-tabs-header-nav-vertical.arco-tabs-header-nav-card.arco-tabs-header-nav-right
  .arco-tabs-header-title-active:hover {
  border-right-color: var(--color-neutral-3);
  border-left-color: var(--color-bg-2);
}
.arco-tabs-header-nav-vertical.arco-tabs-header-nav-card-gutter
  .arco-tabs-header-title {
  margin-left: 0;
  border-radius: var(--border-radius-small) 0 0 var(--border-radius-small);
  position: relative;
}
.arco-tabs-header-nav-vertical.arco-tabs-header-nav-card-gutter
  .arco-tabs-header-title:not(:first-of-type) {
  margin-top: 4px;
}
.arco-tabs-header-nav-vertical.arco-tabs-header-nav-card-gutter
  .arco-tabs-header-title-active,
.arco-tabs-header-nav-vertical.arco-tabs-header-nav-card-gutter
  .arco-tabs-header-title-active:hover {
  border-right-color: var(--color-bg-2);
  border-bottom-color: var(--color-neutral-3);
}
.arco-tabs-header-nav-vertical.arco-tabs-header-nav-card-gutter.arco-tabs-header-nav-right
  .arco-tabs-header-title-active,
.arco-tabs-header-nav-vertical.arco-tabs-header-nav-card-gutter.arco-tabs-header-nav-right
  .arco-tabs-header-title-active:hover {
  border-right-color: var(--color-neutral-3);
  border-left-color: var(--color-bg-2);
}
.arco-tabs-content-vertical {
  width: auto;
  height: 100%;
  padding: 0;
  flex: auto;
}
.arco-tabs-right .arco-tabs-content-vertical {
  padding-right: 16px;
}
.arco-tabs-left .arco-tabs-content-vertical {
  padding-left: 16px;
}
.arco-tabs-card > .arco-tabs-content-vertical,
.arco-tabs-card-gutter > .arco-tabs-content-vertical {
  border: 1px solid var(--color-neutral-3);
  border-left: none;
}
.arco-tabs-card.arco-tabs-right > .arco-tabs-content-vertical,
.arco-tabs-card-gutter.arco-tabs-right > .arco-tabs-content-vertical {
  border-left: 1px solid var(--color-neutral-3);
  border-right: none;
}
.arco-tabs-rtl {
  direction: rtl;
}
.arco-tabs-rtl.arco-tabs-left {
  flex-direction: row-reverse;
}
.arco-tabs-rtl.arco-tabs-right {
  flex-direction: row;
}
.arco-tabs-rtl .arco-tabs-close-icon {
  margin-left: 0;
  margin-right: 8px;
}
.arco-tabs-rtl .arco-tabs-left-icon,
.arco-tabs-rtl .arco-tabs-right-icon {
  margin-left: 6px;
  margin-right: 10px;
}
.arco-tabs-rtl
  .arco-tabs-header-nav-line
  > .arco-tabs-header-nav-horizontal
  .arco-tabs-header-scroll
  > .arco-tabs-header-title:first-of-type {
  margin-right: 0;
}
.arco-tabs-rtl
  .arco-tabs-header-nav-line
  > .arco-tabs-header-nav-horizontal
  .arco-tabs-header-no-padding
  > .arco-tabs-header-title:first-of-type,
.arco-tabs-rtl
  .arco-tabs-header-nav-text
  > .arco-tabs-header-nav-horizontal
  .arco-tabs-header-no-padding
  > .arco-tabs-header-title:first-of-type {
  margin-right: 0;
}
.arco-tabs-rtl .arco-tabs-header-nav-card .arco-tabs-header-title-editable,
.arco-tabs-rtl
  .arco-tabs-header-nav-card-gutter
  .arco-tabs-header-title-editable {
  padding-left: 12px;
  padding-right: 16px;
}
.arco-tabs-rtl .arco-tabs-header-nav-card .arco-tabs-header-title {
  border-right: 1px solid var(--color-neutral-3);
  border-left: none;
}
.arco-tabs-rtl .arco-tabs-header-nav-card .arco-tabs-header-title:last-child {
  border-left: 1px solid var(--color-neutral-3);
  border-top-left-radius: var(--border-radius-small);
  border-top-right-radius: 0;
}
.arco-tabs-rtl .arco-tabs-header-nav-card .arco-tabs-header-title:first-child {
  border-top-left-radius: 0;
  border-top-right-radius: var(--border-radius-small);
}
.arco-tabs-rtl .arco-tabs-header-nav-card-gutter .arco-tabs-header-title {
  margin-right: 4px;
}
.arco-tabs-rtl
  .arco-tabs-header-nav-card-gutter
  .arco-tabs-header-title:first-child {
  margin-right: 0;
}
.arco-tabs-rtl
  .arco-tabs-header-nav-text
  .arco-tabs-header-title:not(:first-of-type):before {
  left: initial;
  right: -9px;
}
.arco-tabs-rtl
  .arco-tabs-header-nav-capsule.arco-tabs-header-nav-horizontal
  .arco-tabs-header-title:not(:first-of-type) {
  margin-left: 0;
  margin-right: 3px;
}
.arco-tabs-rtl
  .arco-tabs-header-nav-capsule.arco-tabs-header-nav-horizontal
  .arco-tabs-header-title:not(:first-of-type):before {
  right: -2px;
  left: initial;
}
body[arco-theme='dark']
  .arco-tabs-header-nav-capsule
  .arco-tabs-header-title-active {
  background-color: var(--color-fill-3);
}
body[arco-theme='dark']
  .arco-tabs-header-nav-capsule
  .arco-tabs-header-title:not(
    body[arco-theme='dark']
      .arco-tabs-header-nav-capsule
      .arco-tabs-header-title-disabled
  ):hover {
  background-color: var(--color-fill-3);
}
.arco-badge {
  display: inline-block;
  position: relative;
  line-height: 1;
}
.arco-badge-rtl {
  direction: rtl;
}
.arco-badge-number,
.arco-badge-dot,
.arco-badge-text,
.arco-badge-custom-dot {
  position: absolute;
  z-index: 2;
  transform: translate(50%, -50%);
  transform-origin: 100% 0%;
  border-radius: 20px;
  box-sizing: border-box;
  text-align: center;
  top: 2px;
  right: 2px;
  overflow: hidden;
}
.arco-badge-rtl .arco-badge-number,
.arco-badge-rtl .arco-badge-dot,
.arco-badge-rtl .arco-badge-text,
.arco-badge-rtl .arco-badge-custom-dot {
  right: unset;
}
.arco-badge-custom-dot {
  background-color: var(--color-bg-2);
}
.arco-badge-number,
.arco-badge-text {
  height: 20px;
  min-width: 20px;
  line-height: 20px;
  font-weight: 500;
  padding: 0 6px;
  font-size: 12px;
  color: var(--color-white);
  background-color: rgb(var(--danger-6));
  box-shadow: 0 0 0 2px var(--color-bg-2);
}
.arco-badge-dot {
  width: 6px;
  height: 6px;
  background-color: rgb(var(--danger-6));
  border-radius: var(--border-radius-circle);
  box-shadow: 0 0 0 2px var(--color-bg-2);
}
.arco-badge-no-children .arco-badge-dot,
.arco-badge-no-children .arco-badge-number,
.arco-badge-no-children .arco-badge-text {
  position: relative;
  display: inline-block;
  transform: none;
  top: unset;
  right: unset;
}
.arco-badge-status-wrapper {
  display: inline-flex;
  align-items: center;
}
.arco-badge-status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: var(--border-radius-circle);
}
.arco-badge-status-default {
  background-color: var(--color-fill-4);
}
.arco-badge-status-processing {
  background-color: rgb(var(--primary-6));
}
.arco-badge-status-success {
  background-color: rgb(var(--success-6));
}
.arco-badge-status-warning {
  background-color: rgb(var(--warning-6));
}
.arco-badge-status-error,
.arco-badge-color-red {
  background-color: rgb(var(--danger-6));
}
.arco-badge-color-orangered {
  background-color: #f77234;
}
.arco-badge-color-orange {
  background-color: rgb(var(--orange-6));
}
.arco-badge-color-gold {
  background-color: rgb(var(--gold-6));
}
.arco-badge-color-lime {
  background-color: rgb(var(--lime-6));
}
.arco-badge-color-green {
  background-color: rgb(var(--success-6));
}
.arco-badge-color-cyan {
  background-color: rgb(var(--cyan-6));
}
.arco-badge-color-arcoblue {
  background-color: rgb(var(--primary-6));
}
.arco-badge-color-purple {
  background-color: rgb(var(--purple-6));
}
.arco-badge-color-pinkpurple {
  background-color: rgb(var(--pinkpurple-6));
}
.arco-badge-color-magenta {
  background-color: rgb(var(--magenta-6));
}
.arco-badge-color-gray {
  background-color: rgb(var(--gray-4));
}
.arco-badge-status-text {
  color: var(--color-text-1);
  margin-left: 8px;
  font-size: 12px;
  line-height: 1.5715;
}
.arco-badge-rtl .arco-badge-status-text {
  margin-left: 0;
  margin-right: 8px;
}
.arco-badge-number-text {
  display: inline-block;
  animation: arco-badge-scale 0.5s cubic-bezier(0.3, 1.3, 0.3, 1);
}
@keyframes arco-badge-scale {
  0% {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
.badge-zoom-enter,
.badge-zoom-appear {
  transform-origin: center;
  transform: translate(50%, -50%) scale(0.2);
}
.badge-zoom-enter-active,
.badge-zoom-appear-active {
  opacity: 1;
  transform-origin: center;
  transform: translate(50%, -50%) scale(1);
  transition: opacity 0.3s cubic-bezier(0.3, 1.3, 0.3, 1),
    transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.badge-zoom-exit {
  opacity: 1;
  transform-origin: center;
  transform: translate(50%, -50%) scale(1);
}
.badge-zoom-exit-active {
  opacity: 0;
  transform-origin: center;
  transform: translate(50%, -50%) scale(0.2);
  transition: opacity 0.3s cubic-bezier(0.3, 1.3, 0.3, 1),
    transform 0.3s cubic-bezier(0.3, 1.3, 0.3, 1);
}
.arco-result {
  width: 100%;
  box-sizing: border-box;
  padding: 32px 32px 24px;
}
.arco-result-is-404,
.arco-result-is-403,
.arco-result-is-500 {
  padding-top: 24px;
}
.arco-result-is-404 .arco-result-icon-tip,
.arco-result-is-403 .arco-result-icon-tip,
.arco-result-is-500 .arco-result-icon-tip {
  height: 92px;
  width: 92px;
  line-height: 92px;
}
.arco-result-icon {
  margin-bottom: 16px;
  text-align: center;
  font-size: 20px;
}
.arco-result-icon-tip {
  display: inline-block;
  height: 45px;
  width: 45px;
  text-align: center;
  border-radius: 50%;
  line-height: 45px;
}
.arco-result-icon-custom {
  width: unset;
  height: unset;
  line-height: inherit;
}
.arco-result-icon-custom > .arco-icon {
  font-size: 45px;
  color: inherit;
}
.arco-result-icon-success {
  color: rgb(var(--success-6));
  background-color: var(--color-success-light-1);
}
.arco-result-icon-error {
  color: rgb(var(--danger-6));
  background-color: var(--color-danger-light-1);
}
.arco-result-icon-info {
  color: rgb(var(--primary-6));
  background-color: var(--color-primary-light-1);
}
.arco-result-icon-warning {
  color: rgb(var(--warning-6));
  background-color: var(--color-warning-light-1);
}
.arco-result-title {
  text-align: center;
  line-height: 1.5715;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-1);
}
.arco-result-subtitle {
  text-align: center;
  line-height: 1.5715;
  font-size: 14px;
  color: var(--color-text-2);
}
.arco-result-extra {
  margin-top: 20px;
  text-align: center;
}
.arco-result-content {
  margin-top: 20px;
}
.arco-space {
  display: inline-flex;
}
.arco-space-vertical {
  flex-direction: column;
}
.arco-space-align-baseline {
  align-items: baseline;
}
.arco-space-align-start {
  align-items: flex-start;
}
.arco-space-align-end {
  align-items: flex-end;
}
.arco-space-align-center {
  align-items: center;
}
.arco-space-wrap {
  flex-wrap: wrap;
}
.arco-space-rtl {
  direction: rtl;
}
.arco-typography {
  color: var(--color-text-1);
  line-height: 1.5715;
  word-break: break-all;
  white-space: normal;
}
h1.arco-typography,
.arco-typography-h1,
h2.arco-typography,
.arco-typography-h2,
h3.arco-typography,
.arco-typography-h3,
h4.arco-typography,
.arco-typography-h4,
h5.arco-typography,
.arco-typography-h5,
h6.arco-typography,
.arco-typography-h6 {
  font-weight: 500;
  margin-top: 0;
  margin-bottom: 16px;
}
h1.arco-typography,
.arco-typography-h1 {
  font-size: 36px;
  line-height: 1.23;
}
h2.arco-typography,
.arco-typography-h2 {
  font-size: 32px;
  line-height: 1.25;
}
h3.arco-typography,
.arco-typography-h3 {
  font-size: 28px;
  line-height: 1.29;
}
h4.arco-typography,
.arco-typography-h4 {
  font-size: 24px;
  line-height: 1.33;
}
h5.arco-typography,
.arco-typography-h5 {
  font-size: 20px;
  line-height: 1.4;
}
h6.arco-typography,
.arco-typography-h6 {
  font-size: 16px;
  line-height: 1.5;
}
div.arco-typography,
p.arco-typography {
  margin-top: 0;
  margin-bottom: 1em;
}
.arco-typography-simple-ellipsis {
  display: flex;
  align-items: flex-end;
  white-space: nowrap;
}
.arco-typography-primary {
  color: rgb(var(--primary-6));
}
.arco-typography-secondary {
  color: var(--color-text-2);
}
.arco-typography-success {
  color: rgb(var(--success-6));
}
.arco-typography-warning {
  color: rgb(var(--warning-6));
}
.arco-typography-error {
  color: rgb(var(--danger-6));
}
.arco-typography-disabled {
  color: var(--color-text-4);
  cursor: not-allowed;
}
.arco-typography mark {
  background-color: rgb(var(--yellow-4));
}
.arco-typography u {
  text-decoration: underline;
}
.arco-typography del {
  text-decoration: line-through;
}
.arco-typography b {
  font-weight: 500;
}
.arco-typography code {
  font-size: 85%;
  color: var(--color-text-2);
  border: 1px solid var(--color-neutral-3);
  background-color: var(--color-neutral-2);
  padding: 2px 8px;
  margin: 0 2px;
  border-radius: 2px;
}
.arco-typography blockquote {
  margin: 0 0 1em;
  border-left: 2px solid var(--color-neutral-6);
  background-color: var(--color-bg-2);
  padding-left: 8px;
}
.arco-typography ol,
.arco-typography ul {
  padding: 0;
  margin: 0;
}
.arco-typography ul li,
.arco-typography ol li {
  margin-left: 20px;
}
.arco-typography ul {
  list-style: circle;
}
.arco-typography-spacing-close {
  line-height: 1.3;
}
.arco-typography-operation-copy,
.arco-typography-operation-copied {
  padding: 2px;
  margin-left: 2px;
}
.arco-typography-operation-copy {
  cursor: pointer;
  color: var(--color-text-2);
  background-color: transparent;
  border-radius: 2px;
  transition: background-color 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-typography-operation-copy:hover {
  color: var(--color-text-2);
  background-color: var(--color-fill-2);
}
.arco-typography-operation-copy:focus-visible {
  box-shadow: 0 0 0 2px var(--color-primary-light-3);
}
.arco-typography-operation-copied {
  color: rgb(var(--success-6));
}
.arco-typography-operation-edit {
  padding: 2px;
  margin-left: 2px;
  cursor: pointer;
  color: var(--color-text-2);
  background-color: transparent;
  border-radius: 2px;
  transition: background-color 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-typography-operation-edit:hover {
  color: var(--color-text-2);
  background-color: var(--color-fill-2);
}
.arco-typography-operation-edit:focus-visible {
  box-shadow: 0 0 0 2px var(--color-primary-light-3);
}
.arco-typography-operation-expand {
  color: rgb(var(--primary-6));
  margin: 0 4px;
  cursor: pointer;
}
.arco-typography-operation-expand:hover {
  color: rgb(var(--primary-5));
}
.arco-typography-operation-expand:focus-visible {
  box-shadow: 0 0 0 2px var(--color-primary-light-3);
  border-radius: var(--border-radius-small);
}
.arco-typography-edit-content {
  position: relative;
  left: -13px;
  margin-right: -13px;
  margin-top: -5px;
  margin-bottom: calc(1em - 5px);
}
.arco-typography-edit-content-textarea {
  font-weight: inherit;
  font-size: inherit;
  line-height: inherit;
}
.arco-typography-rtl {
  direction: rtl;
}
.arco-typography-rtl blockquote {
  border-right: 2px solid var(--color-neutral-6);
  padding-right: 8px;
  padding-left: 0;
}
.arco-typography-rtl ul li,
.arco-typography-rtl ol li {
  margin-left: 0;
  margin-right: 20px;
}
.arco-typography-rtl .arco-typography-operation-copy,
.arco-typography-rtl .arco-typography-operation-copied,
.arco-typography-rtl .arco-typography-operation-edit {
  margin-right: 2px;
  margin-left: 0;
}
.arco-typography-rtl .arco-typography-edit-content {
  left: initial;
  right: -13px;
  margin-left: -13px;
  margin-right: 0;
}
.arco-ellipsis {
  display: flex;
  position: relative;
}
.arco-ellipsis-content.arco-ellipsis-multiple:before {
  content: ' ';
  float: right;
  height: 100%;
  margin-bottom: -21px;
}
.arco-ellipsis-content .arco-ellipsis-action {
  display: inline-block;
}
.arco-ellipsis-content .arco-ellipsis-action-collapsed {
  float: right;
  clear: both;
}
.arco-ellipsis-content .arco-ellipsis-action-text {
  color: rgb(var(--primary-6));
  margin: 0 4px;
  cursor: pointer;
}
.arco-ellipsis-content .arco-ellipsis-action-text:hover {
  color: rgb(var(--primary-5));
}
.arco-ellipsis-single {
  display: block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.arco-ellipsis-multiple {
  display: block;
  word-break: break-all;
}
.arco-ellipsis-collapsed {
  display: -webkit-box;
  display: -moz-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;
}
.arco-ellipsis-content-mirror {
  position: absolute;
  width: 100%;
  visibility: hidden;
}
.arco-pagination {
  display: flex;
  align-items: center;
  font-size: 14px;
}
.arco-pagination-list {
  margin: 0;
  padding: 0;
  list-style: none;
  display: inline-block;
}
.arco-pagination-item {
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  list-style: none;
  box-sizing: border-box;
  cursor: pointer;
  outline: 0;
  user-select: none;
  border-radius: var(--border-radius-small);
  color: var(--color-text-2);
  border: 0 solid transparent;
  background-color: transparent;
  font-size: 14px;
  min-width: 32px;
  height: 32px;
  line-height: 32px;
}
.arco-pagination-item-prev,
.arco-pagination-item-next {
  font-size: 12px;
}
.arco-pagination-item-disabled {
  cursor: not-allowed;
  background-color: transparent;
  color: var(--color-text-4);
  border-color: transparent;
}
.arco-pagination-item:not(.arco-pagination-item-disabled):not(
    .arco-pagination-item-active
  ):hover {
  background-color: var(--color-fill-1);
  border-color: transparent;
  color: var(--color-text-2);
}
.arco-pagination-item-active {
  transition: color 0.2s cubic-bezier(0, 0, 1, 1),
    background-color 0.2s cubic-bezier(0, 0, 1, 1);
  color: rgb(var(--primary-6));
  background-color: var(--color-primary-light-1);
  border-color: transparent;
}
.arco-pagination-item:not(:last-child) {
  margin-right: 8px;
}
.arco-pagination-item:focus-visible {
  box-shadow: 0 0 0 2px var(--color-primary-light-3);
}
.arco-pagination-item-prev,
.arco-pagination-item-next {
  font-size: 12px;
  color: var(--color-text-2);
  background-color: transparent;
}
.arco-pagination-item-prev:not(.arco-pagination-item-disabled):hover,
.arco-pagination-item-next:not(.arco-pagination-item-disabled):hover {
  background-color: var(--color-fill-1);
  color: rgb(var(--primary-6));
}
.arco-pagination-item-prev:after,
.arco-pagination-item-next:after {
  display: inline-block;
  content: '.';
  font-size: 0;
  vertical-align: middle;
}
.arco-pagination .arco-pagination-item-prev.arco-pagination-item-disabled,
.arco-pagination .arco-pagination-item-next.arco-pagination-item-disabled {
  color: var(--color-text-4);
  background-color: transparent;
}
.arco-pagination-item-jumper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}
.arco-pagination-jumper {
  margin-left: 8px;
}
.arco-pagination-jumper > span {
  font-size: 14px;
}
.arco-pagination-jumper-text-goto,
.arco-pagination-jumper-text-goto-suffix {
  color: var(--color-text-3);
}
.arco-pagination .arco-pagination-jumper-input {
  width: 40px;
  text-align: center;
  margin-left: 8px;
  margin-right: 8px;
  padding-left: 2px;
  padding-right: 2px;
}
.arco-pagination-option {
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  min-width: 0;
  position: relative;
  margin-left: 8px;
  font-size: 14px;
  height: 32px;
  line-height: 0;
}
.arco-pagination-option .arco-select {
  width: auto;
}
.arco-pagination-option .arco-select-view-value {
  overflow: inherit;
  padding-right: 6px;
}
.arco-pagination-total-text {
  display: inline-block;
  height: 100%;
  color: var(--color-text-1);
  margin-right: 8px;
  font-size: 14px;
  line-height: 32px;
}
.arco-pagination-item-simple-pager {
  display: inline-block;
  vertical-align: middle;
  margin: 0 12px 0 4px;
}
.arco-pagination-item-simple-pager .arco-pagination-jumper-separator {
  padding: 0 12px;
}
.arco-pagination-simple .arco-pagination-item {
  margin-right: 0;
}
.arco-pagination-simple .arco-pagination-jumper {
  color: var(--color-text-1);
}
.arco-pagination-simple .arco-pagination-jumper {
  margin-left: 0;
}
.arco-pagination-simple .arco-pagination-jumper .arco-pagination-jumper-input {
  width: 40px;
  margin-left: 0;
}
.arco-pagination-simple .arco-pagination-item-prev,
.arco-pagination-simple .arco-pagination-item-next {
  color: var(--color-text-2);
  background-color: transparent;
}
.arco-pagination-simple
  .arco-pagination-item-prev:not(.arco-pagination-item-disabled):hover,
.arco-pagination-simple
  .arco-pagination-item-next:not(.arco-pagination-item-disabled):hover {
  background-color: var(--color-fill-1);
  color: rgb(var(--primary-6));
}
.arco-pagination-simple
  .arco-pagination-item-prev.arco-pagination-item-disabled,
.arco-pagination-simple
  .arco-pagination-item-next.arco-pagination-item-disabled {
  color: var(--color-text-4);
  background-color: transparent;
}
.arco-pagination-disabled {
  cursor: not-allowed;
}
.arco-pagination-disabled .arco-pagination-item,
.arco-pagination-disabled
  .arco-pagination-item:not(.arco-pagination-item-disabled):not(
    .arco-pagination-item-active
  ):hover {
  cursor: not-allowed;
  background-color: transparent;
  border-color: transparent;
  color: var(--color-text-4);
}
.arco-pagination.arco-pagination-disabled .arco-pagination-item-active {
  background-color: var(--color-fill-1);
  border-color: transparent;
  color: var(--color-primary-light-3);
}
.arco-pagination-size-mini .arco-pagination-item {
  font-size: 12px;
  min-width: 24px;
  height: 24px;
  line-height: 24px;
}
.arco-pagination-size-mini .arco-pagination-item-prev,
.arco-pagination-size-mini .arco-pagination-item-next {
  font-size: 12px;
}
.arco-pagination-size-mini .arco-pagination-total-text {
  font-size: 12px;
  line-height: 24px;
}
.arco-pagination-size-mini .arco-pagination-option {
  font-size: 12px;
  height: 24px;
  line-height: 0;
}
.arco-pagination-size-mini .arco-pagination-jumper > span {
  font-size: 12px;
}
.arco-pagination-size-small .arco-pagination-item {
  font-size: 14px;
  min-width: 28px;
  height: 28px;
  line-height: 28px;
}
.arco-pagination-size-small .arco-pagination-item-prev,
.arco-pagination-size-small .arco-pagination-item-next {
  font-size: 12px;
}
.arco-pagination-size-small .arco-pagination-total-text {
  font-size: 14px;
  line-height: 28px;
}
.arco-pagination-size-small .arco-pagination-option {
  font-size: 14px;
  height: 28px;
  line-height: 0;
}
.arco-pagination-size-small .arco-pagination-jumper > span {
  font-size: 14px;
}
.arco-pagination-size-large .arco-pagination-item {
  font-size: 14px;
  min-width: 36px;
  height: 36px;
  line-height: 36px;
}
.arco-pagination-size-large .arco-pagination-item-prev,
.arco-pagination-size-large .arco-pagination-item-next {
  font-size: 14px;
}
.arco-pagination-size-large .arco-pagination-total-text {
  font-size: 14px;
  line-height: 36px;
}
.arco-pagination-size-large .arco-pagination-option {
  font-size: 14px;
  height: 36px;
  line-height: 0;
}
.arco-pagination-size-large .arco-pagination-jumper > span {
  font-size: 14px;
}
.arco-pagination-rtl {
  direction: rtl;
}
.arco-pagination-rtl .arco-pagination-item:not(:last-child) {
  margin-right: 0;
  margin-left: 8px;
}
.arco-pagination-rtl .arco-pagination-item-simple-pager {
  margin: 0 4px 0 12px;
}
.arco-pagination-rtl
  .arco-pagination-item-simple-pager
  .arco-pagination-jumper-separator {
  padding: 0 12px;
}
.arco-pagination-rtl .arco-pagination-jumper,
.arco-pagination-rtl .arco-pagination-jumper-input,
.arco-pagination-rtl .arco-pagination-option {
  margin-left: 0;
  margin-right: 8px;
}
.arco-pagination-rtl .arco-pagination-option .arco-select-view-value {
  padding-right: 0;
  padding-left: 6px;
}
.arco-pagination-rtl .arco-pagination-total-text {
  margin-right: 0;
  margin-left: 8px;
}
.arco-pagination-rtl.arco-pagination-simple .arco-pagination-item,
.arco-pagination-rtl.arco-pagination-simple .arco-pagination-jumper,
.arco-pagination-rtl.arco-pagination-simple .arco-pagination-jumper-input {
  margin-right: 0;
}
.arco-row {
  display: flex;
  flex-flow: row wrap;
}
.arco-row-align-start {
  align-items: flex-start;
}
.arco-row-align-center {
  align-items: center;
}
.arco-row-align-end {
  align-items: flex-end;
}
.arco-row-justify-start {
  justify-content: flex-start;
}
.arco-row-justify-center {
  justify-content: center;
}
.arco-row-justify-end {
  justify-content: flex-end;
}
.arco-row-justify-space-around {
  justify-content: space-around;
}
.arco-row-justify-space-between {
  justify-content: space-between;
}
.arco-row-rtl {
  direction: rtl;
}
.arco-col {
  position: relative;
  box-sizing: border-box;
}
.arco-col-rtl {
  direction: rtl;
}
.arco-col-0 {
  display: none;
}
.arco-col-1 {
  display: block;
  width: 4.16666667%;
  flex: 0 0 4.16666667%;
}
.arco-col-2 {
  display: block;
  width: 8.33333333%;
  flex: 0 0 8.33333333%;
}
.arco-col-3 {
  display: block;
  width: 12.5%;
  flex: 0 0 12.5%;
}
.arco-col-4 {
  display: block;
  width: 16.66666667%;
  flex: 0 0 16.66666667%;
}
.arco-col-5 {
  display: block;
  width: 20.83333333%;
  flex: 0 0 20.83333333%;
}
.arco-col-6 {
  display: block;
  width: 25%;
  flex: 0 0 25%;
}
.arco-col-7 {
  display: block;
  width: 29.16666667%;
  flex: 0 0 29.16666667%;
}
.arco-col-8 {
  display: block;
  width: 33.33333333%;
  flex: 0 0 33.33333333%;
}
.arco-col-9 {
  display: block;
  width: 37.5%;
  flex: 0 0 37.5%;
}
.arco-col-10 {
  display: block;
  width: 41.66666667%;
  flex: 0 0 41.66666667%;
}
.arco-col-11 {
  display: block;
  width: 45.83333333%;
  flex: 0 0 45.83333333%;
}
.arco-col-12 {
  display: block;
  width: 50%;
  flex: 0 0 50%;
}
.arco-col-13 {
  display: block;
  width: 54.16666667%;
  flex: 0 0 54.16666667%;
}
.arco-col-14 {
  display: block;
  width: 58.33333333%;
  flex: 0 0 58.33333333%;
}
.arco-col-15 {
  display: block;
  width: 62.5%;
  flex: 0 0 62.5%;
}
.arco-col-16 {
  display: block;
  width: 66.66666667%;
  flex: 0 0 66.66666667%;
}
.arco-col-17 {
  display: block;
  width: 70.83333333%;
  flex: 0 0 70.83333333%;
}
.arco-col-18 {
  display: block;
  width: 75%;
  flex: 0 0 75%;
}
.arco-col-19 {
  display: block;
  width: 79.16666667%;
  flex: 0 0 79.16666667%;
}
.arco-col-20 {
  display: block;
  width: 83.33333333%;
  flex: 0 0 83.33333333%;
}
.arco-col-21 {
  display: block;
  width: 87.5%;
  flex: 0 0 87.5%;
}
.arco-col-22 {
  display: block;
  width: 91.66666667%;
  flex: 0 0 91.66666667%;
}
.arco-col-23 {
  display: block;
  width: 95.83333333%;
  flex: 0 0 95.83333333%;
}
.arco-col-24 {
  display: block;
  width: 100%;
  flex: 0 0 100%;
}
.arco-col-offset-0 {
  margin-left: 0%;
}
.arco-col-offset-0.arco-col-rtl {
  margin-left: 0;
  margin-right: 0%;
}
.arco-col-offset-1 {
  margin-left: 4.16666667%;
}
.arco-col-offset-1.arco-col-rtl {
  margin-left: 0;
  margin-right: 4.16666667%;
}
.arco-col-offset-2 {
  margin-left: 8.33333333%;
}
.arco-col-offset-2.arco-col-rtl {
  margin-left: 0;
  margin-right: 8.33333333%;
}
.arco-col-offset-3 {
  margin-left: 12.5%;
}
.arco-col-offset-3.arco-col-rtl {
  margin-left: 0;
  margin-right: 12.5%;
}
.arco-col-offset-4 {
  margin-left: 16.66666667%;
}
.arco-col-offset-4.arco-col-rtl {
  margin-left: 0;
  margin-right: 16.66666667%;
}
.arco-col-offset-5 {
  margin-left: 20.83333333%;
}
.arco-col-offset-5.arco-col-rtl {
  margin-left: 0;
  margin-right: 20.83333333%;
}
.arco-col-offset-6 {
  margin-left: 25%;
}
.arco-col-offset-6.arco-col-rtl {
  margin-left: 0;
  margin-right: 25%;
}
.arco-col-offset-7 {
  margin-left: 29.16666667%;
}
.arco-col-offset-7.arco-col-rtl {
  margin-left: 0;
  margin-right: 29.16666667%;
}
.arco-col-offset-8 {
  margin-left: 33.33333333%;
}
.arco-col-offset-8.arco-col-rtl {
  margin-left: 0;
  margin-right: 33.33333333%;
}
.arco-col-offset-9 {
  margin-left: 37.5%;
}
.arco-col-offset-9.arco-col-rtl {
  margin-left: 0;
  margin-right: 37.5%;
}
.arco-col-offset-10 {
  margin-left: 41.66666667%;
}
.arco-col-offset-10.arco-col-rtl {
  margin-left: 0;
  margin-right: 41.66666667%;
}
.arco-col-offset-11 {
  margin-left: 45.83333333%;
}
.arco-col-offset-11.arco-col-rtl {
  margin-left: 0;
  margin-right: 45.83333333%;
}
.arco-col-offset-12 {
  margin-left: 50%;
}
.arco-col-offset-12.arco-col-rtl {
  margin-left: 0;
  margin-right: 50%;
}
.arco-col-offset-13 {
  margin-left: 54.16666667%;
}
.arco-col-offset-13.arco-col-rtl {
  margin-left: 0;
  margin-right: 54.16666667%;
}
.arco-col-offset-14 {
  margin-left: 58.33333333%;
}
.arco-col-offset-14.arco-col-rtl {
  margin-left: 0;
  margin-right: 58.33333333%;
}
.arco-col-offset-15 {
  margin-left: 62.5%;
}
.arco-col-offset-15.arco-col-rtl {
  margin-left: 0;
  margin-right: 62.5%;
}
.arco-col-offset-16 {
  margin-left: 66.66666667%;
}
.arco-col-offset-16.arco-col-rtl {
  margin-left: 0;
  margin-right: 66.66666667%;
}
.arco-col-offset-17 {
  margin-left: 70.83333333%;
}
.arco-col-offset-17.arco-col-rtl {
  margin-left: 0;
  margin-right: 70.83333333%;
}
.arco-col-offset-18 {
  margin-left: 75%;
}
.arco-col-offset-18.arco-col-rtl {
  margin-left: 0;
  margin-right: 75%;
}
.arco-col-offset-19 {
  margin-left: 79.16666667%;
}
.arco-col-offset-19.arco-col-rtl {
  margin-left: 0;
  margin-right: 79.16666667%;
}
.arco-col-offset-20 {
  margin-left: 83.33333333%;
}
.arco-col-offset-20.arco-col-rtl {
  margin-left: 0;
  margin-right: 83.33333333%;
}
.arco-col-offset-21 {
  margin-left: 87.5%;
}
.arco-col-offset-21.arco-col-rtl {
  margin-left: 0;
  margin-right: 87.5%;
}
.arco-col-offset-22 {
  margin-left: 91.66666667%;
}
.arco-col-offset-22.arco-col-rtl {
  margin-left: 0;
  margin-right: 91.66666667%;
}
.arco-col-offset-23 {
  margin-left: 95.83333333%;
}
.arco-col-offset-23.arco-col-rtl {
  margin-left: 0;
  margin-right: 95.83333333%;
}
.arco-col-order-0 {
  order: 0;
}
.arco-col-order-1 {
  order: 1;
}
.arco-col-order-2 {
  order: 2;
}
.arco-col-order-3 {
  order: 3;
}
.arco-col-order-4 {
  order: 4;
}
.arco-col-order-5 {
  order: 5;
}
.arco-col-order-6 {
  order: 6;
}
.arco-col-order-7 {
  order: 7;
}
.arco-col-order-8 {
  order: 8;
}
.arco-col-order-9 {
  order: 9;
}
.arco-col-order-10 {
  order: 10;
}
.arco-col-order-11 {
  order: 11;
}
.arco-col-order-12 {
  order: 12;
}
.arco-col-order-13 {
  order: 13;
}
.arco-col-order-14 {
  order: 14;
}
.arco-col-order-15 {
  order: 15;
}
.arco-col-order-16 {
  order: 16;
}
.arco-col-order-17 {
  order: 17;
}
.arco-col-order-18 {
  order: 18;
}
.arco-col-order-19 {
  order: 19;
}
.arco-col-order-20 {
  order: 20;
}
.arco-col-order-21 {
  order: 21;
}
.arco-col-order-22 {
  order: 22;
}
.arco-col-order-23 {
  order: 23;
}
.arco-col-order-24 {
  order: 24;
}
.arco-col-pull-0 {
  right: 0%;
}
.arco-col-pull-0.arco-col-rtl {
  right: unset;
  left: 0%;
}
.arco-col-pull-1 {
  right: 4.16666667%;
}
.arco-col-pull-1.arco-col-rtl {
  right: unset;
  left: 4.16666667%;
}
.arco-col-pull-2 {
  right: 8.33333333%;
}
.arco-col-pull-2.arco-col-rtl {
  right: unset;
  left: 8.33333333%;
}
.arco-col-pull-3 {
  right: 12.5%;
}
.arco-col-pull-3.arco-col-rtl {
  right: unset;
  left: 12.5%;
}
.arco-col-pull-4 {
  right: 16.66666667%;
}
.arco-col-pull-4.arco-col-rtl {
  right: unset;
  left: 16.66666667%;
}
.arco-col-pull-5 {
  right: 20.83333333%;
}
.arco-col-pull-5.arco-col-rtl {
  right: unset;
  left: 20.83333333%;
}
.arco-col-pull-6 {
  right: 25%;
}
.arco-col-pull-6.arco-col-rtl {
  right: unset;
  left: 25%;
}
.arco-col-pull-7 {
  right: 29.16666667%;
}
.arco-col-pull-7.arco-col-rtl {
  right: unset;
  left: 29.16666667%;
}
.arco-col-pull-8 {
  right: 33.33333333%;
}
.arco-col-pull-8.arco-col-rtl {
  right: unset;
  left: 33.33333333%;
}
.arco-col-pull-9 {
  right: 37.5%;
}
.arco-col-pull-9.arco-col-rtl {
  right: unset;
  left: 37.5%;
}
.arco-col-pull-10 {
  right: 41.66666667%;
}
.arco-col-pull-10.arco-col-rtl {
  right: unset;
  left: 41.66666667%;
}
.arco-col-pull-11 {
  right: 45.83333333%;
}
.arco-col-pull-11.arco-col-rtl {
  right: unset;
  left: 45.83333333%;
}
.arco-col-pull-12 {
  right: 50%;
}
.arco-col-pull-12.arco-col-rtl {
  right: unset;
  left: 50%;
}
.arco-col-pull-13 {
  right: 54.16666667%;
}
.arco-col-pull-13.arco-col-rtl {
  right: unset;
  left: 54.16666667%;
}
.arco-col-pull-14 {
  right: 58.33333333%;
}
.arco-col-pull-14.arco-col-rtl {
  right: unset;
  left: 58.33333333%;
}
.arco-col-pull-15 {
  right: 62.5%;
}
.arco-col-pull-15.arco-col-rtl {
  right: unset;
  left: 62.5%;
}
.arco-col-pull-16 {
  right: 66.66666667%;
}
.arco-col-pull-16.arco-col-rtl {
  right: unset;
  left: 66.66666667%;
}
.arco-col-pull-17 {
  right: 70.83333333%;
}
.arco-col-pull-17.arco-col-rtl {
  right: unset;
  left: 70.83333333%;
}
.arco-col-pull-18 {
  right: 75%;
}
.arco-col-pull-18.arco-col-rtl {
  right: unset;
  left: 75%;
}
.arco-col-pull-19 {
  right: 79.16666667%;
}
.arco-col-pull-19.arco-col-rtl {
  right: unset;
  left: 79.16666667%;
}
.arco-col-pull-20 {
  right: 83.33333333%;
}
.arco-col-pull-20.arco-col-rtl {
  right: unset;
  left: 83.33333333%;
}
.arco-col-pull-21 {
  right: 87.5%;
}
.arco-col-pull-21.arco-col-rtl {
  right: unset;
  left: 87.5%;
}
.arco-col-pull-22 {
  right: 91.66666667%;
}
.arco-col-pull-22.arco-col-rtl {
  right: unset;
  left: 91.66666667%;
}
.arco-col-pull-23 {
  right: 95.83333333%;
}
.arco-col-pull-23.arco-col-rtl {
  right: unset;
  left: 95.83333333%;
}
.arco-col-pull-24 {
  right: 100%;
}
.arco-col-pull-24.arco-col-rtl {
  right: unset;
  left: 100%;
}
.arco-col-push-0 {
  left: 0%;
}
.arco-col-push-0.arco-col-rtl {
  left: unset;
  right: 0%;
}
.arco-col-push-1 {
  left: 4.16666667%;
}
.arco-col-push-1.arco-col-rtl {
  left: unset;
  right: 4.16666667%;
}
.arco-col-push-2 {
  left: 8.33333333%;
}
.arco-col-push-2.arco-col-rtl {
  left: unset;
  right: 8.33333333%;
}
.arco-col-push-3 {
  left: 12.5%;
}
.arco-col-push-3.arco-col-rtl {
  left: unset;
  right: 12.5%;
}
.arco-col-push-4 {
  left: 16.66666667%;
}
.arco-col-push-4.arco-col-rtl {
  left: unset;
  right: 16.66666667%;
}
.arco-col-push-5 {
  left: 20.83333333%;
}
.arco-col-push-5.arco-col-rtl {
  left: unset;
  right: 20.83333333%;
}
.arco-col-push-6 {
  left: 25%;
}
.arco-col-push-6.arco-col-rtl {
  left: unset;
  right: 25%;
}
.arco-col-push-7 {
  left: 29.16666667%;
}
.arco-col-push-7.arco-col-rtl {
  left: unset;
  right: 29.16666667%;
}
.arco-col-push-8 {
  left: 33.33333333%;
}
.arco-col-push-8.arco-col-rtl {
  left: unset;
  right: 33.33333333%;
}
.arco-col-push-9 {
  left: 37.5%;
}
.arco-col-push-9.arco-col-rtl {
  left: unset;
  right: 37.5%;
}
.arco-col-push-10 {
  left: 41.66666667%;
}
.arco-col-push-10.arco-col-rtl {
  left: unset;
  right: 41.66666667%;
}
.arco-col-push-11 {
  left: 45.83333333%;
}
.arco-col-push-11.arco-col-rtl {
  left: unset;
  right: 45.83333333%;
}
.arco-col-push-12 {
  left: 50%;
}
.arco-col-push-12.arco-col-rtl {
  left: unset;
  right: 50%;
}
.arco-col-push-13 {
  left: 54.16666667%;
}
.arco-col-push-13.arco-col-rtl {
  left: unset;
  right: 54.16666667%;
}
.arco-col-push-14 {
  left: 58.33333333%;
}
.arco-col-push-14.arco-col-rtl {
  left: unset;
  right: 58.33333333%;
}
.arco-col-push-15 {
  left: 62.5%;
}
.arco-col-push-15.arco-col-rtl {
  left: unset;
  right: 62.5%;
}
.arco-col-push-16 {
  left: 66.66666667%;
}
.arco-col-push-16.arco-col-rtl {
  left: unset;
  right: 66.66666667%;
}
.arco-col-push-17 {
  left: 70.83333333%;
}
.arco-col-push-17.arco-col-rtl {
  left: unset;
  right: 70.83333333%;
}
.arco-col-push-18 {
  left: 75%;
}
.arco-col-push-18.arco-col-rtl {
  left: unset;
  right: 75%;
}
.arco-col-push-19 {
  left: 79.16666667%;
}
.arco-col-push-19.arco-col-rtl {
  left: unset;
  right: 79.16666667%;
}
.arco-col-push-20 {
  left: 83.33333333%;
}
.arco-col-push-20.arco-col-rtl {
  left: unset;
  right: 83.33333333%;
}
.arco-col-push-21 {
  left: 87.5%;
}
.arco-col-push-21.arco-col-rtl {
  left: unset;
  right: 87.5%;
}
.arco-col-push-22 {
  left: 91.66666667%;
}
.arco-col-push-22.arco-col-rtl {
  left: unset;
  right: 91.66666667%;
}
.arco-col-push-23 {
  left: 95.83333333%;
}
.arco-col-push-23.arco-col-rtl {
  left: unset;
  right: 95.83333333%;
}
.arco-col-push-24 {
  left: 100%;
}
.arco-col-push-24.arco-col-rtl {
  left: unset;
  right: 100%;
}
.arco-col-xs-0 {
  display: none;
}
.arco-col-xs-1 {
  display: block;
  width: 4.16666667%;
  flex: 0 0 4.16666667%;
}
.arco-col-xs-2 {
  display: block;
  width: 8.33333333%;
  flex: 0 0 8.33333333%;
}
.arco-col-xs-3 {
  display: block;
  width: 12.5%;
  flex: 0 0 12.5%;
}
.arco-col-xs-4 {
  display: block;
  width: 16.66666667%;
  flex: 0 0 16.66666667%;
}
.arco-col-xs-5 {
  display: block;
  width: 20.83333333%;
  flex: 0 0 20.83333333%;
}
.arco-col-xs-6 {
  display: block;
  width: 25%;
  flex: 0 0 25%;
}
.arco-col-xs-7 {
  display: block;
  width: 29.16666667%;
  flex: 0 0 29.16666667%;
}
.arco-col-xs-8 {
  display: block;
  width: 33.33333333%;
  flex: 0 0 33.33333333%;
}
.arco-col-xs-9 {
  display: block;
  width: 37.5%;
  flex: 0 0 37.5%;
}
.arco-col-xs-10 {
  display: block;
  width: 41.66666667%;
  flex: 0 0 41.66666667%;
}
.arco-col-xs-11 {
  display: block;
  width: 45.83333333%;
  flex: 0 0 45.83333333%;
}
.arco-col-xs-12 {
  display: block;
  width: 50%;
  flex: 0 0 50%;
}
.arco-col-xs-13 {
  display: block;
  width: 54.16666667%;
  flex: 0 0 54.16666667%;
}
.arco-col-xs-14 {
  display: block;
  width: 58.33333333%;
  flex: 0 0 58.33333333%;
}
.arco-col-xs-15 {
  display: block;
  width: 62.5%;
  flex: 0 0 62.5%;
}
.arco-col-xs-16 {
  display: block;
  width: 66.66666667%;
  flex: 0 0 66.66666667%;
}
.arco-col-xs-17 {
  display: block;
  width: 70.83333333%;
  flex: 0 0 70.83333333%;
}
.arco-col-xs-18 {
  display: block;
  width: 75%;
  flex: 0 0 75%;
}
.arco-col-xs-19 {
  display: block;
  width: 79.16666667%;
  flex: 0 0 79.16666667%;
}
.arco-col-xs-20 {
  display: block;
  width: 83.33333333%;
  flex: 0 0 83.33333333%;
}
.arco-col-xs-21 {
  display: block;
  width: 87.5%;
  flex: 0 0 87.5%;
}
.arco-col-xs-22 {
  display: block;
  width: 91.66666667%;
  flex: 0 0 91.66666667%;
}
.arco-col-xs-23 {
  display: block;
  width: 95.83333333%;
  flex: 0 0 95.83333333%;
}
.arco-col-xs-24 {
  display: block;
  width: 100%;
  flex: 0 0 100%;
}
.arco-col-xs-offset-0 {
  margin-left: 0%;
}
.arco-col-xs-offset-0.arco-col-rtl {
  margin-left: 0;
  margin-right: 0%;
}
.arco-col-xs-offset-1 {
  margin-left: 4.16666667%;
}
.arco-col-xs-offset-1.arco-col-rtl {
  margin-left: 0;
  margin-right: 4.16666667%;
}
.arco-col-xs-offset-2 {
  margin-left: 8.33333333%;
}
.arco-col-xs-offset-2.arco-col-rtl {
  margin-left: 0;
  margin-right: 8.33333333%;
}
.arco-col-xs-offset-3 {
  margin-left: 12.5%;
}
.arco-col-xs-offset-3.arco-col-rtl {
  margin-left: 0;
  margin-right: 12.5%;
}
.arco-col-xs-offset-4 {
  margin-left: 16.66666667%;
}
.arco-col-xs-offset-4.arco-col-rtl {
  margin-left: 0;
  margin-right: 16.66666667%;
}
.arco-col-xs-offset-5 {
  margin-left: 20.83333333%;
}
.arco-col-xs-offset-5.arco-col-rtl {
  margin-left: 0;
  margin-right: 20.83333333%;
}
.arco-col-xs-offset-6 {
  margin-left: 25%;
}
.arco-col-xs-offset-6.arco-col-rtl {
  margin-left: 0;
  margin-right: 25%;
}
.arco-col-xs-offset-7 {
  margin-left: 29.16666667%;
}
.arco-col-xs-offset-7.arco-col-rtl {
  margin-left: 0;
  margin-right: 29.16666667%;
}
.arco-col-xs-offset-8 {
  margin-left: 33.33333333%;
}
.arco-col-xs-offset-8.arco-col-rtl {
  margin-left: 0;
  margin-right: 33.33333333%;
}
.arco-col-xs-offset-9 {
  margin-left: 37.5%;
}
.arco-col-xs-offset-9.arco-col-rtl {
  margin-left: 0;
  margin-right: 37.5%;
}
.arco-col-xs-offset-10 {
  margin-left: 41.66666667%;
}
.arco-col-xs-offset-10.arco-col-rtl {
  margin-left: 0;
  margin-right: 41.66666667%;
}
.arco-col-xs-offset-11 {
  margin-left: 45.83333333%;
}
.arco-col-xs-offset-11.arco-col-rtl {
  margin-left: 0;
  margin-right: 45.83333333%;
}
.arco-col-xs-offset-12 {
  margin-left: 50%;
}
.arco-col-xs-offset-12.arco-col-rtl {
  margin-left: 0;
  margin-right: 50%;
}
.arco-col-xs-offset-13 {
  margin-left: 54.16666667%;
}
.arco-col-xs-offset-13.arco-col-rtl {
  margin-left: 0;
  margin-right: 54.16666667%;
}
.arco-col-xs-offset-14 {
  margin-left: 58.33333333%;
}
.arco-col-xs-offset-14.arco-col-rtl {
  margin-left: 0;
  margin-right: 58.33333333%;
}
.arco-col-xs-offset-15 {
  margin-left: 62.5%;
}
.arco-col-xs-offset-15.arco-col-rtl {
  margin-left: 0;
  margin-right: 62.5%;
}
.arco-col-xs-offset-16 {
  margin-left: 66.66666667%;
}
.arco-col-xs-offset-16.arco-col-rtl {
  margin-left: 0;
  margin-right: 66.66666667%;
}
.arco-col-xs-offset-17 {
  margin-left: 70.83333333%;
}
.arco-col-xs-offset-17.arco-col-rtl {
  margin-left: 0;
  margin-right: 70.83333333%;
}
.arco-col-xs-offset-18 {
  margin-left: 75%;
}
.arco-col-xs-offset-18.arco-col-rtl {
  margin-left: 0;
  margin-right: 75%;
}
.arco-col-xs-offset-19 {
  margin-left: 79.16666667%;
}
.arco-col-xs-offset-19.arco-col-rtl {
  margin-left: 0;
  margin-right: 79.16666667%;
}
.arco-col-xs-offset-20 {
  margin-left: 83.33333333%;
}
.arco-col-xs-offset-20.arco-col-rtl {
  margin-left: 0;
  margin-right: 83.33333333%;
}
.arco-col-xs-offset-21 {
  margin-left: 87.5%;
}
.arco-col-xs-offset-21.arco-col-rtl {
  margin-left: 0;
  margin-right: 87.5%;
}
.arco-col-xs-offset-22 {
  margin-left: 91.66666667%;
}
.arco-col-xs-offset-22.arco-col-rtl {
  margin-left: 0;
  margin-right: 91.66666667%;
}
.arco-col-xs-offset-23 {
  margin-left: 95.83333333%;
}
.arco-col-xs-offset-23.arco-col-rtl {
  margin-left: 0;
  margin-right: 95.83333333%;
}
.arco-col-xs-order-0 {
  order: 0;
}
.arco-col-xs-order-1 {
  order: 1;
}
.arco-col-xs-order-2 {
  order: 2;
}
.arco-col-xs-order-3 {
  order: 3;
}
.arco-col-xs-order-4 {
  order: 4;
}
.arco-col-xs-order-5 {
  order: 5;
}
.arco-col-xs-order-6 {
  order: 6;
}
.arco-col-xs-order-7 {
  order: 7;
}
.arco-col-xs-order-8 {
  order: 8;
}
.arco-col-xs-order-9 {
  order: 9;
}
.arco-col-xs-order-10 {
  order: 10;
}
.arco-col-xs-order-11 {
  order: 11;
}
.arco-col-xs-order-12 {
  order: 12;
}
.arco-col-xs-order-13 {
  order: 13;
}
.arco-col-xs-order-14 {
  order: 14;
}
.arco-col-xs-order-15 {
  order: 15;
}
.arco-col-xs-order-16 {
  order: 16;
}
.arco-col-xs-order-17 {
  order: 17;
}
.arco-col-xs-order-18 {
  order: 18;
}
.arco-col-xs-order-19 {
  order: 19;
}
.arco-col-xs-order-20 {
  order: 20;
}
.arco-col-xs-order-21 {
  order: 21;
}
.arco-col-xs-order-22 {
  order: 22;
}
.arco-col-xs-order-23 {
  order: 23;
}
.arco-col-xs-order-24 {
  order: 24;
}
.arco-col-xs-pull-0 {
  right: 0%;
}
.arco-col-xs-pull-0.arco-col-rtl {
  right: unset;
  left: 0%;
}
.arco-col-xs-pull-1 {
  right: 4.16666667%;
}
.arco-col-xs-pull-1.arco-col-rtl {
  right: unset;
  left: 4.16666667%;
}
.arco-col-xs-pull-2 {
  right: 8.33333333%;
}
.arco-col-xs-pull-2.arco-col-rtl {
  right: unset;
  left: 8.33333333%;
}
.arco-col-xs-pull-3 {
  right: 12.5%;
}
.arco-col-xs-pull-3.arco-col-rtl {
  right: unset;
  left: 12.5%;
}
.arco-col-xs-pull-4 {
  right: 16.66666667%;
}
.arco-col-xs-pull-4.arco-col-rtl {
  right: unset;
  left: 16.66666667%;
}
.arco-col-xs-pull-5 {
  right: 20.83333333%;
}
.arco-col-xs-pull-5.arco-col-rtl {
  right: unset;
  left: 20.83333333%;
}
.arco-col-xs-pull-6 {
  right: 25%;
}
.arco-col-xs-pull-6.arco-col-rtl {
  right: unset;
  left: 25%;
}
.arco-col-xs-pull-7 {
  right: 29.16666667%;
}
.arco-col-xs-pull-7.arco-col-rtl {
  right: unset;
  left: 29.16666667%;
}
.arco-col-xs-pull-8 {
  right: 33.33333333%;
}
.arco-col-xs-pull-8.arco-col-rtl {
  right: unset;
  left: 33.33333333%;
}
.arco-col-xs-pull-9 {
  right: 37.5%;
}
.arco-col-xs-pull-9.arco-col-rtl {
  right: unset;
  left: 37.5%;
}
.arco-col-xs-pull-10 {
  right: 41.66666667%;
}
.arco-col-xs-pull-10.arco-col-rtl {
  right: unset;
  left: 41.66666667%;
}
.arco-col-xs-pull-11 {
  right: 45.83333333%;
}
.arco-col-xs-pull-11.arco-col-rtl {
  right: unset;
  left: 45.83333333%;
}
.arco-col-xs-pull-12 {
  right: 50%;
}
.arco-col-xs-pull-12.arco-col-rtl {
  right: unset;
  left: 50%;
}
.arco-col-xs-pull-13 {
  right: 54.16666667%;
}
.arco-col-xs-pull-13.arco-col-rtl {
  right: unset;
  left: 54.16666667%;
}
.arco-col-xs-pull-14 {
  right: 58.33333333%;
}
.arco-col-xs-pull-14.arco-col-rtl {
  right: unset;
  left: 58.33333333%;
}
.arco-col-xs-pull-15 {
  right: 62.5%;
}
.arco-col-xs-pull-15.arco-col-rtl {
  right: unset;
  left: 62.5%;
}
.arco-col-xs-pull-16 {
  right: 66.66666667%;
}
.arco-col-xs-pull-16.arco-col-rtl {
  right: unset;
  left: 66.66666667%;
}
.arco-col-xs-pull-17 {
  right: 70.83333333%;
}
.arco-col-xs-pull-17.arco-col-rtl {
  right: unset;
  left: 70.83333333%;
}
.arco-col-xs-pull-18 {
  right: 75%;
}
.arco-col-xs-pull-18.arco-col-rtl {
  right: unset;
  left: 75%;
}
.arco-col-xs-pull-19 {
  right: 79.16666667%;
}
.arco-col-xs-pull-19.arco-col-rtl {
  right: unset;
  left: 79.16666667%;
}
.arco-col-xs-pull-20 {
  right: 83.33333333%;
}
.arco-col-xs-pull-20.arco-col-rtl {
  right: unset;
  left: 83.33333333%;
}
.arco-col-xs-pull-21 {
  right: 87.5%;
}
.arco-col-xs-pull-21.arco-col-rtl {
  right: unset;
  left: 87.5%;
}
.arco-col-xs-pull-22 {
  right: 91.66666667%;
}
.arco-col-xs-pull-22.arco-col-rtl {
  right: unset;
  left: 91.66666667%;
}
.arco-col-xs-pull-23 {
  right: 95.83333333%;
}
.arco-col-xs-pull-23.arco-col-rtl {
  right: unset;
  left: 95.83333333%;
}
.arco-col-xs-pull-24 {
  right: 100%;
}
.arco-col-xs-pull-24.arco-col-rtl {
  right: unset;
  left: 100%;
}
.arco-col-xs-push-0 {
  left: 0%;
}
.arco-col-xs-push-0.arco-col-rtl {
  left: unset;
  right: 0%;
}
.arco-col-xs-push-1 {
  left: 4.16666667%;
}
.arco-col-xs-push-1.arco-col-rtl {
  left: unset;
  right: 4.16666667%;
}
.arco-col-xs-push-2 {
  left: 8.33333333%;
}
.arco-col-xs-push-2.arco-col-rtl {
  left: unset;
  right: 8.33333333%;
}
.arco-col-xs-push-3 {
  left: 12.5%;
}
.arco-col-xs-push-3.arco-col-rtl {
  left: unset;
  right: 12.5%;
}
.arco-col-xs-push-4 {
  left: 16.66666667%;
}
.arco-col-xs-push-4.arco-col-rtl {
  left: unset;
  right: 16.66666667%;
}
.arco-col-xs-push-5 {
  left: 20.83333333%;
}
.arco-col-xs-push-5.arco-col-rtl {
  left: unset;
  right: 20.83333333%;
}
.arco-col-xs-push-6 {
  left: 25%;
}
.arco-col-xs-push-6.arco-col-rtl {
  left: unset;
  right: 25%;
}
.arco-col-xs-push-7 {
  left: 29.16666667%;
}
.arco-col-xs-push-7.arco-col-rtl {
  left: unset;
  right: 29.16666667%;
}
.arco-col-xs-push-8 {
  left: 33.33333333%;
}
.arco-col-xs-push-8.arco-col-rtl {
  left: unset;
  right: 33.33333333%;
}
.arco-col-xs-push-9 {
  left: 37.5%;
}
.arco-col-xs-push-9.arco-col-rtl {
  left: unset;
  right: 37.5%;
}
.arco-col-xs-push-10 {
  left: 41.66666667%;
}
.arco-col-xs-push-10.arco-col-rtl {
  left: unset;
  right: 41.66666667%;
}
.arco-col-xs-push-11 {
  left: 45.83333333%;
}
.arco-col-xs-push-11.arco-col-rtl {
  left: unset;
  right: 45.83333333%;
}
.arco-col-xs-push-12 {
  left: 50%;
}
.arco-col-xs-push-12.arco-col-rtl {
  left: unset;
  right: 50%;
}
.arco-col-xs-push-13 {
  left: 54.16666667%;
}
.arco-col-xs-push-13.arco-col-rtl {
  left: unset;
  right: 54.16666667%;
}
.arco-col-xs-push-14 {
  left: 58.33333333%;
}
.arco-col-xs-push-14.arco-col-rtl {
  left: unset;
  right: 58.33333333%;
}
.arco-col-xs-push-15 {
  left: 62.5%;
}
.arco-col-xs-push-15.arco-col-rtl {
  left: unset;
  right: 62.5%;
}
.arco-col-xs-push-16 {
  left: 66.66666667%;
}
.arco-col-xs-push-16.arco-col-rtl {
  left: unset;
  right: 66.66666667%;
}
.arco-col-xs-push-17 {
  left: 70.83333333%;
}
.arco-col-xs-push-17.arco-col-rtl {
  left: unset;
  right: 70.83333333%;
}
.arco-col-xs-push-18 {
  left: 75%;
}
.arco-col-xs-push-18.arco-col-rtl {
  left: unset;
  right: 75%;
}
.arco-col-xs-push-19 {
  left: 79.16666667%;
}
.arco-col-xs-push-19.arco-col-rtl {
  left: unset;
  right: 79.16666667%;
}
.arco-col-xs-push-20 {
  left: 83.33333333%;
}
.arco-col-xs-push-20.arco-col-rtl {
  left: unset;
  right: 83.33333333%;
}
.arco-col-xs-push-21 {
  left: 87.5%;
}
.arco-col-xs-push-21.arco-col-rtl {
  left: unset;
  right: 87.5%;
}
.arco-col-xs-push-22 {
  left: 91.66666667%;
}
.arco-col-xs-push-22.arco-col-rtl {
  left: unset;
  right: 91.66666667%;
}
.arco-col-xs-push-23 {
  left: 95.83333333%;
}
.arco-col-xs-push-23.arco-col-rtl {
  left: unset;
  right: 95.83333333%;
}
.arco-col-xs-push-24 {
  left: 100%;
}
.arco-col-xs-push-24.arco-col-rtl {
  left: unset;
  right: 100%;
}
@media (min-width: 576px) {
  .arco-col-sm-0 {
    display: none;
  }
  .arco-col-sm-1 {
    display: block;
    width: 4.16666667%;
    flex: 0 0 4.16666667%;
  }
  .arco-col-sm-2 {
    display: block;
    width: 8.33333333%;
    flex: 0 0 8.33333333%;
  }
  .arco-col-sm-3 {
    display: block;
    width: 12.5%;
    flex: 0 0 12.5%;
  }
  .arco-col-sm-4 {
    display: block;
    width: 16.66666667%;
    flex: 0 0 16.66666667%;
  }
  .arco-col-sm-5 {
    display: block;
    width: 20.83333333%;
    flex: 0 0 20.83333333%;
  }
  .arco-col-sm-6 {
    display: block;
    width: 25%;
    flex: 0 0 25%;
  }
  .arco-col-sm-7 {
    display: block;
    width: 29.16666667%;
    flex: 0 0 29.16666667%;
  }
  .arco-col-sm-8 {
    display: block;
    width: 33.33333333%;
    flex: 0 0 33.33333333%;
  }
  .arco-col-sm-9 {
    display: block;
    width: 37.5%;
    flex: 0 0 37.5%;
  }
  .arco-col-sm-10 {
    display: block;
    width: 41.66666667%;
    flex: 0 0 41.66666667%;
  }
  .arco-col-sm-11 {
    display: block;
    width: 45.83333333%;
    flex: 0 0 45.83333333%;
  }
  .arco-col-sm-12 {
    display: block;
    width: 50%;
    flex: 0 0 50%;
  }
  .arco-col-sm-13 {
    display: block;
    width: 54.16666667%;
    flex: 0 0 54.16666667%;
  }
  .arco-col-sm-14 {
    display: block;
    width: 58.33333333%;
    flex: 0 0 58.33333333%;
  }
  .arco-col-sm-15 {
    display: block;
    width: 62.5%;
    flex: 0 0 62.5%;
  }
  .arco-col-sm-16 {
    display: block;
    width: 66.66666667%;
    flex: 0 0 66.66666667%;
  }
  .arco-col-sm-17 {
    display: block;
    width: 70.83333333%;
    flex: 0 0 70.83333333%;
  }
  .arco-col-sm-18 {
    display: block;
    width: 75%;
    flex: 0 0 75%;
  }
  .arco-col-sm-19 {
    display: block;
    width: 79.16666667%;
    flex: 0 0 79.16666667%;
  }
  .arco-col-sm-20 {
    display: block;
    width: 83.33333333%;
    flex: 0 0 83.33333333%;
  }
  .arco-col-sm-21 {
    display: block;
    width: 87.5%;
    flex: 0 0 87.5%;
  }
  .arco-col-sm-22 {
    display: block;
    width: 91.66666667%;
    flex: 0 0 91.66666667%;
  }
  .arco-col-sm-23 {
    display: block;
    width: 95.83333333%;
    flex: 0 0 95.83333333%;
  }
  .arco-col-sm-24 {
    display: block;
    width: 100%;
    flex: 0 0 100%;
  }
  .arco-col-sm-offset-0 {
    margin-left: 0%;
  }
  .arco-col-sm-offset-0.arco-col-rtl {
    margin-left: 0;
    margin-right: 0%;
  }
  .arco-col-sm-offset-1 {
    margin-left: 4.16666667%;
  }
  .arco-col-sm-offset-1.arco-col-rtl {
    margin-left: 0;
    margin-right: 4.16666667%;
  }
  .arco-col-sm-offset-2 {
    margin-left: 8.33333333%;
  }
  .arco-col-sm-offset-2.arco-col-rtl {
    margin-left: 0;
    margin-right: 8.33333333%;
  }
  .arco-col-sm-offset-3 {
    margin-left: 12.5%;
  }
  .arco-col-sm-offset-3.arco-col-rtl {
    margin-left: 0;
    margin-right: 12.5%;
  }
  .arco-col-sm-offset-4 {
    margin-left: 16.66666667%;
  }
  .arco-col-sm-offset-4.arco-col-rtl {
    margin-left: 0;
    margin-right: 16.66666667%;
  }
  .arco-col-sm-offset-5 {
    margin-left: 20.83333333%;
  }
  .arco-col-sm-offset-5.arco-col-rtl {
    margin-left: 0;
    margin-right: 20.83333333%;
  }
  .arco-col-sm-offset-6 {
    margin-left: 25%;
  }
  .arco-col-sm-offset-6.arco-col-rtl {
    margin-left: 0;
    margin-right: 25%;
  }
  .arco-col-sm-offset-7 {
    margin-left: 29.16666667%;
  }
  .arco-col-sm-offset-7.arco-col-rtl {
    margin-left: 0;
    margin-right: 29.16666667%;
  }
  .arco-col-sm-offset-8 {
    margin-left: 33.33333333%;
  }
  .arco-col-sm-offset-8.arco-col-rtl {
    margin-left: 0;
    margin-right: 33.33333333%;
  }
  .arco-col-sm-offset-9 {
    margin-left: 37.5%;
  }
  .arco-col-sm-offset-9.arco-col-rtl {
    margin-left: 0;
    margin-right: 37.5%;
  }
  .arco-col-sm-offset-10 {
    margin-left: 41.66666667%;
  }
  .arco-col-sm-offset-10.arco-col-rtl {
    margin-left: 0;
    margin-right: 41.66666667%;
  }
  .arco-col-sm-offset-11 {
    margin-left: 45.83333333%;
  }
  .arco-col-sm-offset-11.arco-col-rtl {
    margin-left: 0;
    margin-right: 45.83333333%;
  }
  .arco-col-sm-offset-12 {
    margin-left: 50%;
  }
  .arco-col-sm-offset-12.arco-col-rtl {
    margin-left: 0;
    margin-right: 50%;
  }
  .arco-col-sm-offset-13 {
    margin-left: 54.16666667%;
  }
  .arco-col-sm-offset-13.arco-col-rtl {
    margin-left: 0;
    margin-right: 54.16666667%;
  }
  .arco-col-sm-offset-14 {
    margin-left: 58.33333333%;
  }
  .arco-col-sm-offset-14.arco-col-rtl {
    margin-left: 0;
    margin-right: 58.33333333%;
  }
  .arco-col-sm-offset-15 {
    margin-left: 62.5%;
  }
  .arco-col-sm-offset-15.arco-col-rtl {
    margin-left: 0;
    margin-right: 62.5%;
  }
  .arco-col-sm-offset-16 {
    margin-left: 66.66666667%;
  }
  .arco-col-sm-offset-16.arco-col-rtl {
    margin-left: 0;
    margin-right: 66.66666667%;
  }
  .arco-col-sm-offset-17 {
    margin-left: 70.83333333%;
  }
  .arco-col-sm-offset-17.arco-col-rtl {
    margin-left: 0;
    margin-right: 70.83333333%;
  }
  .arco-col-sm-offset-18 {
    margin-left: 75%;
  }
  .arco-col-sm-offset-18.arco-col-rtl {
    margin-left: 0;
    margin-right: 75%;
  }
  .arco-col-sm-offset-19 {
    margin-left: 79.16666667%;
  }
  .arco-col-sm-offset-19.arco-col-rtl {
    margin-left: 0;
    margin-right: 79.16666667%;
  }
  .arco-col-sm-offset-20 {
    margin-left: 83.33333333%;
  }
  .arco-col-sm-offset-20.arco-col-rtl {
    margin-left: 0;
    margin-right: 83.33333333%;
  }
  .arco-col-sm-offset-21 {
    margin-left: 87.5%;
  }
  .arco-col-sm-offset-21.arco-col-rtl {
    margin-left: 0;
    margin-right: 87.5%;
  }
  .arco-col-sm-offset-22 {
    margin-left: 91.66666667%;
  }
  .arco-col-sm-offset-22.arco-col-rtl {
    margin-left: 0;
    margin-right: 91.66666667%;
  }
  .arco-col-sm-offset-23 {
    margin-left: 95.83333333%;
  }
  .arco-col-sm-offset-23.arco-col-rtl {
    margin-left: 0;
    margin-right: 95.83333333%;
  }
  .arco-col-sm-order-0 {
    order: 0;
  }
  .arco-col-sm-order-1 {
    order: 1;
  }
  .arco-col-sm-order-2 {
    order: 2;
  }
  .arco-col-sm-order-3 {
    order: 3;
  }
  .arco-col-sm-order-4 {
    order: 4;
  }
  .arco-col-sm-order-5 {
    order: 5;
  }
  .arco-col-sm-order-6 {
    order: 6;
  }
  .arco-col-sm-order-7 {
    order: 7;
  }
  .arco-col-sm-order-8 {
    order: 8;
  }
  .arco-col-sm-order-9 {
    order: 9;
  }
  .arco-col-sm-order-10 {
    order: 10;
  }
  .arco-col-sm-order-11 {
    order: 11;
  }
  .arco-col-sm-order-12 {
    order: 12;
  }
  .arco-col-sm-order-13 {
    order: 13;
  }
  .arco-col-sm-order-14 {
    order: 14;
  }
  .arco-col-sm-order-15 {
    order: 15;
  }
  .arco-col-sm-order-16 {
    order: 16;
  }
  .arco-col-sm-order-17 {
    order: 17;
  }
  .arco-col-sm-order-18 {
    order: 18;
  }
  .arco-col-sm-order-19 {
    order: 19;
  }
  .arco-col-sm-order-20 {
    order: 20;
  }
  .arco-col-sm-order-21 {
    order: 21;
  }
  .arco-col-sm-order-22 {
    order: 22;
  }
  .arco-col-sm-order-23 {
    order: 23;
  }
  .arco-col-sm-order-24 {
    order: 24;
  }
  .arco-col-sm-pull-0 {
    right: 0%;
  }
  .arco-col-sm-pull-0.arco-col-rtl {
    right: unset;
    left: 0%;
  }
  .arco-col-sm-pull-1 {
    right: 4.16666667%;
  }
  .arco-col-sm-pull-1.arco-col-rtl {
    right: unset;
    left: 4.16666667%;
  }
  .arco-col-sm-pull-2 {
    right: 8.33333333%;
  }
  .arco-col-sm-pull-2.arco-col-rtl {
    right: unset;
    left: 8.33333333%;
  }
  .arco-col-sm-pull-3 {
    right: 12.5%;
  }
  .arco-col-sm-pull-3.arco-col-rtl {
    right: unset;
    left: 12.5%;
  }
  .arco-col-sm-pull-4 {
    right: 16.66666667%;
  }
  .arco-col-sm-pull-4.arco-col-rtl {
    right: unset;
    left: 16.66666667%;
  }
  .arco-col-sm-pull-5 {
    right: 20.83333333%;
  }
  .arco-col-sm-pull-5.arco-col-rtl {
    right: unset;
    left: 20.83333333%;
  }
  .arco-col-sm-pull-6 {
    right: 25%;
  }
  .arco-col-sm-pull-6.arco-col-rtl {
    right: unset;
    left: 25%;
  }
  .arco-col-sm-pull-7 {
    right: 29.16666667%;
  }
  .arco-col-sm-pull-7.arco-col-rtl {
    right: unset;
    left: 29.16666667%;
  }
  .arco-col-sm-pull-8 {
    right: 33.33333333%;
  }
  .arco-col-sm-pull-8.arco-col-rtl {
    right: unset;
    left: 33.33333333%;
  }
  .arco-col-sm-pull-9 {
    right: 37.5%;
  }
  .arco-col-sm-pull-9.arco-col-rtl {
    right: unset;
    left: 37.5%;
  }
  .arco-col-sm-pull-10 {
    right: 41.66666667%;
  }
  .arco-col-sm-pull-10.arco-col-rtl {
    right: unset;
    left: 41.66666667%;
  }
  .arco-col-sm-pull-11 {
    right: 45.83333333%;
  }
  .arco-col-sm-pull-11.arco-col-rtl {
    right: unset;
    left: 45.83333333%;
  }
  .arco-col-sm-pull-12 {
    right: 50%;
  }
  .arco-col-sm-pull-12.arco-col-rtl {
    right: unset;
    left: 50%;
  }
  .arco-col-sm-pull-13 {
    right: 54.16666667%;
  }
  .arco-col-sm-pull-13.arco-col-rtl {
    right: unset;
    left: 54.16666667%;
  }
  .arco-col-sm-pull-14 {
    right: 58.33333333%;
  }
  .arco-col-sm-pull-14.arco-col-rtl {
    right: unset;
    left: 58.33333333%;
  }
  .arco-col-sm-pull-15 {
    right: 62.5%;
  }
  .arco-col-sm-pull-15.arco-col-rtl {
    right: unset;
    left: 62.5%;
  }
  .arco-col-sm-pull-16 {
    right: 66.66666667%;
  }
  .arco-col-sm-pull-16.arco-col-rtl {
    right: unset;
    left: 66.66666667%;
  }
  .arco-col-sm-pull-17 {
    right: 70.83333333%;
  }
  .arco-col-sm-pull-17.arco-col-rtl {
    right: unset;
    left: 70.83333333%;
  }
  .arco-col-sm-pull-18 {
    right: 75%;
  }
  .arco-col-sm-pull-18.arco-col-rtl {
    right: unset;
    left: 75%;
  }
  .arco-col-sm-pull-19 {
    right: 79.16666667%;
  }
  .arco-col-sm-pull-19.arco-col-rtl {
    right: unset;
    left: 79.16666667%;
  }
  .arco-col-sm-pull-20 {
    right: 83.33333333%;
  }
  .arco-col-sm-pull-20.arco-col-rtl {
    right: unset;
    left: 83.33333333%;
  }
  .arco-col-sm-pull-21 {
    right: 87.5%;
  }
  .arco-col-sm-pull-21.arco-col-rtl {
    right: unset;
    left: 87.5%;
  }
  .arco-col-sm-pull-22 {
    right: 91.66666667%;
  }
  .arco-col-sm-pull-22.arco-col-rtl {
    right: unset;
    left: 91.66666667%;
  }
  .arco-col-sm-pull-23 {
    right: 95.83333333%;
  }
  .arco-col-sm-pull-23.arco-col-rtl {
    right: unset;
    left: 95.83333333%;
  }
  .arco-col-sm-pull-24 {
    right: 100%;
  }
  .arco-col-sm-pull-24.arco-col-rtl {
    right: unset;
    left: 100%;
  }
  .arco-col-sm-push-0 {
    left: 0%;
  }
  .arco-col-sm-push-0.arco-col-rtl {
    left: unset;
    right: 0%;
  }
  .arco-col-sm-push-1 {
    left: 4.16666667%;
  }
  .arco-col-sm-push-1.arco-col-rtl {
    left: unset;
    right: 4.16666667%;
  }
  .arco-col-sm-push-2 {
    left: 8.33333333%;
  }
  .arco-col-sm-push-2.arco-col-rtl {
    left: unset;
    right: 8.33333333%;
  }
  .arco-col-sm-push-3 {
    left: 12.5%;
  }
  .arco-col-sm-push-3.arco-col-rtl {
    left: unset;
    right: 12.5%;
  }
  .arco-col-sm-push-4 {
    left: 16.66666667%;
  }
  .arco-col-sm-push-4.arco-col-rtl {
    left: unset;
    right: 16.66666667%;
  }
  .arco-col-sm-push-5 {
    left: 20.83333333%;
  }
  .arco-col-sm-push-5.arco-col-rtl {
    left: unset;
    right: 20.83333333%;
  }
  .arco-col-sm-push-6 {
    left: 25%;
  }
  .arco-col-sm-push-6.arco-col-rtl {
    left: unset;
    right: 25%;
  }
  .arco-col-sm-push-7 {
    left: 29.16666667%;
  }
  .arco-col-sm-push-7.arco-col-rtl {
    left: unset;
    right: 29.16666667%;
  }
  .arco-col-sm-push-8 {
    left: 33.33333333%;
  }
  .arco-col-sm-push-8.arco-col-rtl {
    left: unset;
    right: 33.33333333%;
  }
  .arco-col-sm-push-9 {
    left: 37.5%;
  }
  .arco-col-sm-push-9.arco-col-rtl {
    left: unset;
    right: 37.5%;
  }
  .arco-col-sm-push-10 {
    left: 41.66666667%;
  }
  .arco-col-sm-push-10.arco-col-rtl {
    left: unset;
    right: 41.66666667%;
  }
  .arco-col-sm-push-11 {
    left: 45.83333333%;
  }
  .arco-col-sm-push-11.arco-col-rtl {
    left: unset;
    right: 45.83333333%;
  }
  .arco-col-sm-push-12 {
    left: 50%;
  }
  .arco-col-sm-push-12.arco-col-rtl {
    left: unset;
    right: 50%;
  }
  .arco-col-sm-push-13 {
    left: 54.16666667%;
  }
  .arco-col-sm-push-13.arco-col-rtl {
    left: unset;
    right: 54.16666667%;
  }
  .arco-col-sm-push-14 {
    left: 58.33333333%;
  }
  .arco-col-sm-push-14.arco-col-rtl {
    left: unset;
    right: 58.33333333%;
  }
  .arco-col-sm-push-15 {
    left: 62.5%;
  }
  .arco-col-sm-push-15.arco-col-rtl {
    left: unset;
    right: 62.5%;
  }
  .arco-col-sm-push-16 {
    left: 66.66666667%;
  }
  .arco-col-sm-push-16.arco-col-rtl {
    left: unset;
    right: 66.66666667%;
  }
  .arco-col-sm-push-17 {
    left: 70.83333333%;
  }
  .arco-col-sm-push-17.arco-col-rtl {
    left: unset;
    right: 70.83333333%;
  }
  .arco-col-sm-push-18 {
    left: 75%;
  }
  .arco-col-sm-push-18.arco-col-rtl {
    left: unset;
    right: 75%;
  }
  .arco-col-sm-push-19 {
    left: 79.16666667%;
  }
  .arco-col-sm-push-19.arco-col-rtl {
    left: unset;
    right: 79.16666667%;
  }
  .arco-col-sm-push-20 {
    left: 83.33333333%;
  }
  .arco-col-sm-push-20.arco-col-rtl {
    left: unset;
    right: 83.33333333%;
  }
  .arco-col-sm-push-21 {
    left: 87.5%;
  }
  .arco-col-sm-push-21.arco-col-rtl {
    left: unset;
    right: 87.5%;
  }
  .arco-col-sm-push-22 {
    left: 91.66666667%;
  }
  .arco-col-sm-push-22.arco-col-rtl {
    left: unset;
    right: 91.66666667%;
  }
  .arco-col-sm-push-23 {
    left: 95.83333333%;
  }
  .arco-col-sm-push-23.arco-col-rtl {
    left: unset;
    right: 95.83333333%;
  }
  .arco-col-sm-push-24 {
    left: 100%;
  }
  .arco-col-sm-push-24.arco-col-rtl {
    left: unset;
    right: 100%;
  }
}
@media (min-width: 768px) {
  .arco-col-md-0 {
    display: none;
  }
  .arco-col-md-1 {
    display: block;
    width: 4.16666667%;
    flex: 0 0 4.16666667%;
  }
  .arco-col-md-2 {
    display: block;
    width: 8.33333333%;
    flex: 0 0 8.33333333%;
  }
  .arco-col-md-3 {
    display: block;
    width: 12.5%;
    flex: 0 0 12.5%;
  }
  .arco-col-md-4 {
    display: block;
    width: 16.66666667%;
    flex: 0 0 16.66666667%;
  }
  .arco-col-md-5 {
    display: block;
    width: 20.83333333%;
    flex: 0 0 20.83333333%;
  }
  .arco-col-md-6 {
    display: block;
    width: 25%;
    flex: 0 0 25%;
  }
  .arco-col-md-7 {
    display: block;
    width: 29.16666667%;
    flex: 0 0 29.16666667%;
  }
  .arco-col-md-8 {
    display: block;
    width: 33.33333333%;
    flex: 0 0 33.33333333%;
  }
  .arco-col-md-9 {
    display: block;
    width: 37.5%;
    flex: 0 0 37.5%;
  }
  .arco-col-md-10 {
    display: block;
    width: 41.66666667%;
    flex: 0 0 41.66666667%;
  }
  .arco-col-md-11 {
    display: block;
    width: 45.83333333%;
    flex: 0 0 45.83333333%;
  }
  .arco-col-md-12 {
    display: block;
    width: 50%;
    flex: 0 0 50%;
  }
  .arco-col-md-13 {
    display: block;
    width: 54.16666667%;
    flex: 0 0 54.16666667%;
  }
  .arco-col-md-14 {
    display: block;
    width: 58.33333333%;
    flex: 0 0 58.33333333%;
  }
  .arco-col-md-15 {
    display: block;
    width: 62.5%;
    flex: 0 0 62.5%;
  }
  .arco-col-md-16 {
    display: block;
    width: 66.66666667%;
    flex: 0 0 66.66666667%;
  }
  .arco-col-md-17 {
    display: block;
    width: 70.83333333%;
    flex: 0 0 70.83333333%;
  }
  .arco-col-md-18 {
    display: block;
    width: 75%;
    flex: 0 0 75%;
  }
  .arco-col-md-19 {
    display: block;
    width: 79.16666667%;
    flex: 0 0 79.16666667%;
  }
  .arco-col-md-20 {
    display: block;
    width: 83.33333333%;
    flex: 0 0 83.33333333%;
  }
  .arco-col-md-21 {
    display: block;
    width: 87.5%;
    flex: 0 0 87.5%;
  }
  .arco-col-md-22 {
    display: block;
    width: 91.66666667%;
    flex: 0 0 91.66666667%;
  }
  .arco-col-md-23 {
    display: block;
    width: 95.83333333%;
    flex: 0 0 95.83333333%;
  }
  .arco-col-md-24 {
    display: block;
    width: 100%;
    flex: 0 0 100%;
  }
  .arco-col-md-offset-0 {
    margin-left: 0%;
  }
  .arco-col-md-offset-0.arco-col-rtl {
    margin-left: 0;
    margin-right: 0%;
  }
  .arco-col-md-offset-1 {
    margin-left: 4.16666667%;
  }
  .arco-col-md-offset-1.arco-col-rtl {
    margin-left: 0;
    margin-right: 4.16666667%;
  }
  .arco-col-md-offset-2 {
    margin-left: 8.33333333%;
  }
  .arco-col-md-offset-2.arco-col-rtl {
    margin-left: 0;
    margin-right: 8.33333333%;
  }
  .arco-col-md-offset-3 {
    margin-left: 12.5%;
  }
  .arco-col-md-offset-3.arco-col-rtl {
    margin-left: 0;
    margin-right: 12.5%;
  }
  .arco-col-md-offset-4 {
    margin-left: 16.66666667%;
  }
  .arco-col-md-offset-4.arco-col-rtl {
    margin-left: 0;
    margin-right: 16.66666667%;
  }
  .arco-col-md-offset-5 {
    margin-left: 20.83333333%;
  }
  .arco-col-md-offset-5.arco-col-rtl {
    margin-left: 0;
    margin-right: 20.83333333%;
  }
  .arco-col-md-offset-6 {
    margin-left: 25%;
  }
  .arco-col-md-offset-6.arco-col-rtl {
    margin-left: 0;
    margin-right: 25%;
  }
  .arco-col-md-offset-7 {
    margin-left: 29.16666667%;
  }
  .arco-col-md-offset-7.arco-col-rtl {
    margin-left: 0;
    margin-right: 29.16666667%;
  }
  .arco-col-md-offset-8 {
    margin-left: 33.33333333%;
  }
  .arco-col-md-offset-8.arco-col-rtl {
    margin-left: 0;
    margin-right: 33.33333333%;
  }
  .arco-col-md-offset-9 {
    margin-left: 37.5%;
  }
  .arco-col-md-offset-9.arco-col-rtl {
    margin-left: 0;
    margin-right: 37.5%;
  }
  .arco-col-md-offset-10 {
    margin-left: 41.66666667%;
  }
  .arco-col-md-offset-10.arco-col-rtl {
    margin-left: 0;
    margin-right: 41.66666667%;
  }
  .arco-col-md-offset-11 {
    margin-left: 45.83333333%;
  }
  .arco-col-md-offset-11.arco-col-rtl {
    margin-left: 0;
    margin-right: 45.83333333%;
  }
  .arco-col-md-offset-12 {
    margin-left: 50%;
  }
  .arco-col-md-offset-12.arco-col-rtl {
    margin-left: 0;
    margin-right: 50%;
  }
  .arco-col-md-offset-13 {
    margin-left: 54.16666667%;
  }
  .arco-col-md-offset-13.arco-col-rtl {
    margin-left: 0;
    margin-right: 54.16666667%;
  }
  .arco-col-md-offset-14 {
    margin-left: 58.33333333%;
  }
  .arco-col-md-offset-14.arco-col-rtl {
    margin-left: 0;
    margin-right: 58.33333333%;
  }
  .arco-col-md-offset-15 {
    margin-left: 62.5%;
  }
  .arco-col-md-offset-15.arco-col-rtl {
    margin-left: 0;
    margin-right: 62.5%;
  }
  .arco-col-md-offset-16 {
    margin-left: 66.66666667%;
  }
  .arco-col-md-offset-16.arco-col-rtl {
    margin-left: 0;
    margin-right: 66.66666667%;
  }
  .arco-col-md-offset-17 {
    margin-left: 70.83333333%;
  }
  .arco-col-md-offset-17.arco-col-rtl {
    margin-left: 0;
    margin-right: 70.83333333%;
  }
  .arco-col-md-offset-18 {
    margin-left: 75%;
  }
  .arco-col-md-offset-18.arco-col-rtl {
    margin-left: 0;
    margin-right: 75%;
  }
  .arco-col-md-offset-19 {
    margin-left: 79.16666667%;
  }
  .arco-col-md-offset-19.arco-col-rtl {
    margin-left: 0;
    margin-right: 79.16666667%;
  }
  .arco-col-md-offset-20 {
    margin-left: 83.33333333%;
  }
  .arco-col-md-offset-20.arco-col-rtl {
    margin-left: 0;
    margin-right: 83.33333333%;
  }
  .arco-col-md-offset-21 {
    margin-left: 87.5%;
  }
  .arco-col-md-offset-21.arco-col-rtl {
    margin-left: 0;
    margin-right: 87.5%;
  }
  .arco-col-md-offset-22 {
    margin-left: 91.66666667%;
  }
  .arco-col-md-offset-22.arco-col-rtl {
    margin-left: 0;
    margin-right: 91.66666667%;
  }
  .arco-col-md-offset-23 {
    margin-left: 95.83333333%;
  }
  .arco-col-md-offset-23.arco-col-rtl {
    margin-left: 0;
    margin-right: 95.83333333%;
  }
  .arco-col-md-order-0 {
    order: 0;
  }
  .arco-col-md-order-1 {
    order: 1;
  }
  .arco-col-md-order-2 {
    order: 2;
  }
  .arco-col-md-order-3 {
    order: 3;
  }
  .arco-col-md-order-4 {
    order: 4;
  }
  .arco-col-md-order-5 {
    order: 5;
  }
  .arco-col-md-order-6 {
    order: 6;
  }
  .arco-col-md-order-7 {
    order: 7;
  }
  .arco-col-md-order-8 {
    order: 8;
  }
  .arco-col-md-order-9 {
    order: 9;
  }
  .arco-col-md-order-10 {
    order: 10;
  }
  .arco-col-md-order-11 {
    order: 11;
  }
  .arco-col-md-order-12 {
    order: 12;
  }
  .arco-col-md-order-13 {
    order: 13;
  }
  .arco-col-md-order-14 {
    order: 14;
  }
  .arco-col-md-order-15 {
    order: 15;
  }
  .arco-col-md-order-16 {
    order: 16;
  }
  .arco-col-md-order-17 {
    order: 17;
  }
  .arco-col-md-order-18 {
    order: 18;
  }
  .arco-col-md-order-19 {
    order: 19;
  }
  .arco-col-md-order-20 {
    order: 20;
  }
  .arco-col-md-order-21 {
    order: 21;
  }
  .arco-col-md-order-22 {
    order: 22;
  }
  .arco-col-md-order-23 {
    order: 23;
  }
  .arco-col-md-order-24 {
    order: 24;
  }
  .arco-col-md-pull-0 {
    right: 0%;
  }
  .arco-col-md-pull-0.arco-col-rtl {
    right: unset;
    left: 0%;
  }
  .arco-col-md-pull-1 {
    right: 4.16666667%;
  }
  .arco-col-md-pull-1.arco-col-rtl {
    right: unset;
    left: 4.16666667%;
  }
  .arco-col-md-pull-2 {
    right: 8.33333333%;
  }
  .arco-col-md-pull-2.arco-col-rtl {
    right: unset;
    left: 8.33333333%;
  }
  .arco-col-md-pull-3 {
    right: 12.5%;
  }
  .arco-col-md-pull-3.arco-col-rtl {
    right: unset;
    left: 12.5%;
  }
  .arco-col-md-pull-4 {
    right: 16.66666667%;
  }
  .arco-col-md-pull-4.arco-col-rtl {
    right: unset;
    left: 16.66666667%;
  }
  .arco-col-md-pull-5 {
    right: 20.83333333%;
  }
  .arco-col-md-pull-5.arco-col-rtl {
    right: unset;
    left: 20.83333333%;
  }
  .arco-col-md-pull-6 {
    right: 25%;
  }
  .arco-col-md-pull-6.arco-col-rtl {
    right: unset;
    left: 25%;
  }
  .arco-col-md-pull-7 {
    right: 29.16666667%;
  }
  .arco-col-md-pull-7.arco-col-rtl {
    right: unset;
    left: 29.16666667%;
  }
  .arco-col-md-pull-8 {
    right: 33.33333333%;
  }
  .arco-col-md-pull-8.arco-col-rtl {
    right: unset;
    left: 33.33333333%;
  }
  .arco-col-md-pull-9 {
    right: 37.5%;
  }
  .arco-col-md-pull-9.arco-col-rtl {
    right: unset;
    left: 37.5%;
  }
  .arco-col-md-pull-10 {
    right: 41.66666667%;
  }
  .arco-col-md-pull-10.arco-col-rtl {
    right: unset;
    left: 41.66666667%;
  }
  .arco-col-md-pull-11 {
    right: 45.83333333%;
  }
  .arco-col-md-pull-11.arco-col-rtl {
    right: unset;
    left: 45.83333333%;
  }
  .arco-col-md-pull-12 {
    right: 50%;
  }
  .arco-col-md-pull-12.arco-col-rtl {
    right: unset;
    left: 50%;
  }
  .arco-col-md-pull-13 {
    right: 54.16666667%;
  }
  .arco-col-md-pull-13.arco-col-rtl {
    right: unset;
    left: 54.16666667%;
  }
  .arco-col-md-pull-14 {
    right: 58.33333333%;
  }
  .arco-col-md-pull-14.arco-col-rtl {
    right: unset;
    left: 58.33333333%;
  }
  .arco-col-md-pull-15 {
    right: 62.5%;
  }
  .arco-col-md-pull-15.arco-col-rtl {
    right: unset;
    left: 62.5%;
  }
  .arco-col-md-pull-16 {
    right: 66.66666667%;
  }
  .arco-col-md-pull-16.arco-col-rtl {
    right: unset;
    left: 66.66666667%;
  }
  .arco-col-md-pull-17 {
    right: 70.83333333%;
  }
  .arco-col-md-pull-17.arco-col-rtl {
    right: unset;
    left: 70.83333333%;
  }
  .arco-col-md-pull-18 {
    right: 75%;
  }
  .arco-col-md-pull-18.arco-col-rtl {
    right: unset;
    left: 75%;
  }
  .arco-col-md-pull-19 {
    right: 79.16666667%;
  }
  .arco-col-md-pull-19.arco-col-rtl {
    right: unset;
    left: 79.16666667%;
  }
  .arco-col-md-pull-20 {
    right: 83.33333333%;
  }
  .arco-col-md-pull-20.arco-col-rtl {
    right: unset;
    left: 83.33333333%;
  }
  .arco-col-md-pull-21 {
    right: 87.5%;
  }
  .arco-col-md-pull-21.arco-col-rtl {
    right: unset;
    left: 87.5%;
  }
  .arco-col-md-pull-22 {
    right: 91.66666667%;
  }
  .arco-col-md-pull-22.arco-col-rtl {
    right: unset;
    left: 91.66666667%;
  }
  .arco-col-md-pull-23 {
    right: 95.83333333%;
  }
  .arco-col-md-pull-23.arco-col-rtl {
    right: unset;
    left: 95.83333333%;
  }
  .arco-col-md-pull-24 {
    right: 100%;
  }
  .arco-col-md-pull-24.arco-col-rtl {
    right: unset;
    left: 100%;
  }
  .arco-col-md-push-0 {
    left: 0%;
  }
  .arco-col-md-push-0.arco-col-rtl {
    left: unset;
    right: 0%;
  }
  .arco-col-md-push-1 {
    left: 4.16666667%;
  }
  .arco-col-md-push-1.arco-col-rtl {
    left: unset;
    right: 4.16666667%;
  }
  .arco-col-md-push-2 {
    left: 8.33333333%;
  }
  .arco-col-md-push-2.arco-col-rtl {
    left: unset;
    right: 8.33333333%;
  }
  .arco-col-md-push-3 {
    left: 12.5%;
  }
  .arco-col-md-push-3.arco-col-rtl {
    left: unset;
    right: 12.5%;
  }
  .arco-col-md-push-4 {
    left: 16.66666667%;
  }
  .arco-col-md-push-4.arco-col-rtl {
    left: unset;
    right: 16.66666667%;
  }
  .arco-col-md-push-5 {
    left: 20.83333333%;
  }
  .arco-col-md-push-5.arco-col-rtl {
    left: unset;
    right: 20.83333333%;
  }
  .arco-col-md-push-6 {
    left: 25%;
  }
  .arco-col-md-push-6.arco-col-rtl {
    left: unset;
    right: 25%;
  }
  .arco-col-md-push-7 {
    left: 29.16666667%;
  }
  .arco-col-md-push-7.arco-col-rtl {
    left: unset;
    right: 29.16666667%;
  }
  .arco-col-md-push-8 {
    left: 33.33333333%;
  }
  .arco-col-md-push-8.arco-col-rtl {
    left: unset;
    right: 33.33333333%;
  }
  .arco-col-md-push-9 {
    left: 37.5%;
  }
  .arco-col-md-push-9.arco-col-rtl {
    left: unset;
    right: 37.5%;
  }
  .arco-col-md-push-10 {
    left: 41.66666667%;
  }
  .arco-col-md-push-10.arco-col-rtl {
    left: unset;
    right: 41.66666667%;
  }
  .arco-col-md-push-11 {
    left: 45.83333333%;
  }
  .arco-col-md-push-11.arco-col-rtl {
    left: unset;
    right: 45.83333333%;
  }
  .arco-col-md-push-12 {
    left: 50%;
  }
  .arco-col-md-push-12.arco-col-rtl {
    left: unset;
    right: 50%;
  }
  .arco-col-md-push-13 {
    left: 54.16666667%;
  }
  .arco-col-md-push-13.arco-col-rtl {
    left: unset;
    right: 54.16666667%;
  }
  .arco-col-md-push-14 {
    left: 58.33333333%;
  }
  .arco-col-md-push-14.arco-col-rtl {
    left: unset;
    right: 58.33333333%;
  }
  .arco-col-md-push-15 {
    left: 62.5%;
  }
  .arco-col-md-push-15.arco-col-rtl {
    left: unset;
    right: 62.5%;
  }
  .arco-col-md-push-16 {
    left: 66.66666667%;
  }
  .arco-col-md-push-16.arco-col-rtl {
    left: unset;
    right: 66.66666667%;
  }
  .arco-col-md-push-17 {
    left: 70.83333333%;
  }
  .arco-col-md-push-17.arco-col-rtl {
    left: unset;
    right: 70.83333333%;
  }
  .arco-col-md-push-18 {
    left: 75%;
  }
  .arco-col-md-push-18.arco-col-rtl {
    left: unset;
    right: 75%;
  }
  .arco-col-md-push-19 {
    left: 79.16666667%;
  }
  .arco-col-md-push-19.arco-col-rtl {
    left: unset;
    right: 79.16666667%;
  }
  .arco-col-md-push-20 {
    left: 83.33333333%;
  }
  .arco-col-md-push-20.arco-col-rtl {
    left: unset;
    right: 83.33333333%;
  }
  .arco-col-md-push-21 {
    left: 87.5%;
  }
  .arco-col-md-push-21.arco-col-rtl {
    left: unset;
    right: 87.5%;
  }
  .arco-col-md-push-22 {
    left: 91.66666667%;
  }
  .arco-col-md-push-22.arco-col-rtl {
    left: unset;
    right: 91.66666667%;
  }
  .arco-col-md-push-23 {
    left: 95.83333333%;
  }
  .arco-col-md-push-23.arco-col-rtl {
    left: unset;
    right: 95.83333333%;
  }
  .arco-col-md-push-24 {
    left: 100%;
  }
  .arco-col-md-push-24.arco-col-rtl {
    left: unset;
    right: 100%;
  }
}
@media (min-width: 992px) {
  .arco-col-lg-0 {
    display: none;
  }
  .arco-col-lg-1 {
    display: block;
    width: 4.16666667%;
    flex: 0 0 4.16666667%;
  }
  .arco-col-lg-2 {
    display: block;
    width: 8.33333333%;
    flex: 0 0 8.33333333%;
  }
  .arco-col-lg-3 {
    display: block;
    width: 12.5%;
    flex: 0 0 12.5%;
  }
  .arco-col-lg-4 {
    display: block;
    width: 16.66666667%;
    flex: 0 0 16.66666667%;
  }
  .arco-col-lg-5 {
    display: block;
    width: 20.83333333%;
    flex: 0 0 20.83333333%;
  }
  .arco-col-lg-6 {
    display: block;
    width: 25%;
    flex: 0 0 25%;
  }
  .arco-col-lg-7 {
    display: block;
    width: 29.16666667%;
    flex: 0 0 29.16666667%;
  }
  .arco-col-lg-8 {
    display: block;
    width: 33.33333333%;
    flex: 0 0 33.33333333%;
  }
  .arco-col-lg-9 {
    display: block;
    width: 37.5%;
    flex: 0 0 37.5%;
  }
  .arco-col-lg-10 {
    display: block;
    width: 41.66666667%;
    flex: 0 0 41.66666667%;
  }
  .arco-col-lg-11 {
    display: block;
    width: 45.83333333%;
    flex: 0 0 45.83333333%;
  }
  .arco-col-lg-12 {
    display: block;
    width: 50%;
    flex: 0 0 50%;
  }
  .arco-col-lg-13 {
    display: block;
    width: 54.16666667%;
    flex: 0 0 54.16666667%;
  }
  .arco-col-lg-14 {
    display: block;
    width: 58.33333333%;
    flex: 0 0 58.33333333%;
  }
  .arco-col-lg-15 {
    display: block;
    width: 62.5%;
    flex: 0 0 62.5%;
  }
  .arco-col-lg-16 {
    display: block;
    width: 66.66666667%;
    flex: 0 0 66.66666667%;
  }
  .arco-col-lg-17 {
    display: block;
    width: 70.83333333%;
    flex: 0 0 70.83333333%;
  }
  .arco-col-lg-18 {
    display: block;
    width: 75%;
    flex: 0 0 75%;
  }
  .arco-col-lg-19 {
    display: block;
    width: 79.16666667%;
    flex: 0 0 79.16666667%;
  }
  .arco-col-lg-20 {
    display: block;
    width: 83.33333333%;
    flex: 0 0 83.33333333%;
  }
  .arco-col-lg-21 {
    display: block;
    width: 87.5%;
    flex: 0 0 87.5%;
  }
  .arco-col-lg-22 {
    display: block;
    width: 91.66666667%;
    flex: 0 0 91.66666667%;
  }
  .arco-col-lg-23 {
    display: block;
    width: 95.83333333%;
    flex: 0 0 95.83333333%;
  }
  .arco-col-lg-24 {
    display: block;
    width: 100%;
    flex: 0 0 100%;
  }
  .arco-col-lg-offset-0 {
    margin-left: 0%;
  }
  .arco-col-lg-offset-0.arco-col-rtl {
    margin-left: 0;
    margin-right: 0%;
  }
  .arco-col-lg-offset-1 {
    margin-left: 4.16666667%;
  }
  .arco-col-lg-offset-1.arco-col-rtl {
    margin-left: 0;
    margin-right: 4.16666667%;
  }
  .arco-col-lg-offset-2 {
    margin-left: 8.33333333%;
  }
  .arco-col-lg-offset-2.arco-col-rtl {
    margin-left: 0;
    margin-right: 8.33333333%;
  }
  .arco-col-lg-offset-3 {
    margin-left: 12.5%;
  }
  .arco-col-lg-offset-3.arco-col-rtl {
    margin-left: 0;
    margin-right: 12.5%;
  }
  .arco-col-lg-offset-4 {
    margin-left: 16.66666667%;
  }
  .arco-col-lg-offset-4.arco-col-rtl {
    margin-left: 0;
    margin-right: 16.66666667%;
  }
  .arco-col-lg-offset-5 {
    margin-left: 20.83333333%;
  }
  .arco-col-lg-offset-5.arco-col-rtl {
    margin-left: 0;
    margin-right: 20.83333333%;
  }
  .arco-col-lg-offset-6 {
    margin-left: 25%;
  }
  .arco-col-lg-offset-6.arco-col-rtl {
    margin-left: 0;
    margin-right: 25%;
  }
  .arco-col-lg-offset-7 {
    margin-left: 29.16666667%;
  }
  .arco-col-lg-offset-7.arco-col-rtl {
    margin-left: 0;
    margin-right: 29.16666667%;
  }
  .arco-col-lg-offset-8 {
    margin-left: 33.33333333%;
  }
  .arco-col-lg-offset-8.arco-col-rtl {
    margin-left: 0;
    margin-right: 33.33333333%;
  }
  .arco-col-lg-offset-9 {
    margin-left: 37.5%;
  }
  .arco-col-lg-offset-9.arco-col-rtl {
    margin-left: 0;
    margin-right: 37.5%;
  }
  .arco-col-lg-offset-10 {
    margin-left: 41.66666667%;
  }
  .arco-col-lg-offset-10.arco-col-rtl {
    margin-left: 0;
    margin-right: 41.66666667%;
  }
  .arco-col-lg-offset-11 {
    margin-left: 45.83333333%;
  }
  .arco-col-lg-offset-11.arco-col-rtl {
    margin-left: 0;
    margin-right: 45.83333333%;
  }
  .arco-col-lg-offset-12 {
    margin-left: 50%;
  }
  .arco-col-lg-offset-12.arco-col-rtl {
    margin-left: 0;
    margin-right: 50%;
  }
  .arco-col-lg-offset-13 {
    margin-left: 54.16666667%;
  }
  .arco-col-lg-offset-13.arco-col-rtl {
    margin-left: 0;
    margin-right: 54.16666667%;
  }
  .arco-col-lg-offset-14 {
    margin-left: 58.33333333%;
  }
  .arco-col-lg-offset-14.arco-col-rtl {
    margin-left: 0;
    margin-right: 58.33333333%;
  }
  .arco-col-lg-offset-15 {
    margin-left: 62.5%;
  }
  .arco-col-lg-offset-15.arco-col-rtl {
    margin-left: 0;
    margin-right: 62.5%;
  }
  .arco-col-lg-offset-16 {
    margin-left: 66.66666667%;
  }
  .arco-col-lg-offset-16.arco-col-rtl {
    margin-left: 0;
    margin-right: 66.66666667%;
  }
  .arco-col-lg-offset-17 {
    margin-left: 70.83333333%;
  }
  .arco-col-lg-offset-17.arco-col-rtl {
    margin-left: 0;
    margin-right: 70.83333333%;
  }
  .arco-col-lg-offset-18 {
    margin-left: 75%;
  }
  .arco-col-lg-offset-18.arco-col-rtl {
    margin-left: 0;
    margin-right: 75%;
  }
  .arco-col-lg-offset-19 {
    margin-left: 79.16666667%;
  }
  .arco-col-lg-offset-19.arco-col-rtl {
    margin-left: 0;
    margin-right: 79.16666667%;
  }
  .arco-col-lg-offset-20 {
    margin-left: 83.33333333%;
  }
  .arco-col-lg-offset-20.arco-col-rtl {
    margin-left: 0;
    margin-right: 83.33333333%;
  }
  .arco-col-lg-offset-21 {
    margin-left: 87.5%;
  }
  .arco-col-lg-offset-21.arco-col-rtl {
    margin-left: 0;
    margin-right: 87.5%;
  }
  .arco-col-lg-offset-22 {
    margin-left: 91.66666667%;
  }
  .arco-col-lg-offset-22.arco-col-rtl {
    margin-left: 0;
    margin-right: 91.66666667%;
  }
  .arco-col-lg-offset-23 {
    margin-left: 95.83333333%;
  }
  .arco-col-lg-offset-23.arco-col-rtl {
    margin-left: 0;
    margin-right: 95.83333333%;
  }
  .arco-col-lg-order-0 {
    order: 0;
  }
  .arco-col-lg-order-1 {
    order: 1;
  }
  .arco-col-lg-order-2 {
    order: 2;
  }
  .arco-col-lg-order-3 {
    order: 3;
  }
  .arco-col-lg-order-4 {
    order: 4;
  }
  .arco-col-lg-order-5 {
    order: 5;
  }
  .arco-col-lg-order-6 {
    order: 6;
  }
  .arco-col-lg-order-7 {
    order: 7;
  }
  .arco-col-lg-order-8 {
    order: 8;
  }
  .arco-col-lg-order-9 {
    order: 9;
  }
  .arco-col-lg-order-10 {
    order: 10;
  }
  .arco-col-lg-order-11 {
    order: 11;
  }
  .arco-col-lg-order-12 {
    order: 12;
  }
  .arco-col-lg-order-13 {
    order: 13;
  }
  .arco-col-lg-order-14 {
    order: 14;
  }
  .arco-col-lg-order-15 {
    order: 15;
  }
  .arco-col-lg-order-16 {
    order: 16;
  }
  .arco-col-lg-order-17 {
    order: 17;
  }
  .arco-col-lg-order-18 {
    order: 18;
  }
  .arco-col-lg-order-19 {
    order: 19;
  }
  .arco-col-lg-order-20 {
    order: 20;
  }
  .arco-col-lg-order-21 {
    order: 21;
  }
  .arco-col-lg-order-22 {
    order: 22;
  }
  .arco-col-lg-order-23 {
    order: 23;
  }
  .arco-col-lg-order-24 {
    order: 24;
  }
  .arco-col-lg-pull-0 {
    right: 0%;
  }
  .arco-col-lg-pull-0.arco-col-rtl {
    right: unset;
    left: 0%;
  }
  .arco-col-lg-pull-1 {
    right: 4.16666667%;
  }
  .arco-col-lg-pull-1.arco-col-rtl {
    right: unset;
    left: 4.16666667%;
  }
  .arco-col-lg-pull-2 {
    right: 8.33333333%;
  }
  .arco-col-lg-pull-2.arco-col-rtl {
    right: unset;
    left: 8.33333333%;
  }
  .arco-col-lg-pull-3 {
    right: 12.5%;
  }
  .arco-col-lg-pull-3.arco-col-rtl {
    right: unset;
    left: 12.5%;
  }
  .arco-col-lg-pull-4 {
    right: 16.66666667%;
  }
  .arco-col-lg-pull-4.arco-col-rtl {
    right: unset;
    left: 16.66666667%;
  }
  .arco-col-lg-pull-5 {
    right: 20.83333333%;
  }
  .arco-col-lg-pull-5.arco-col-rtl {
    right: unset;
    left: 20.83333333%;
  }
  .arco-col-lg-pull-6 {
    right: 25%;
  }
  .arco-col-lg-pull-6.arco-col-rtl {
    right: unset;
    left: 25%;
  }
  .arco-col-lg-pull-7 {
    right: 29.16666667%;
  }
  .arco-col-lg-pull-7.arco-col-rtl {
    right: unset;
    left: 29.16666667%;
  }
  .arco-col-lg-pull-8 {
    right: 33.33333333%;
  }
  .arco-col-lg-pull-8.arco-col-rtl {
    right: unset;
    left: 33.33333333%;
  }
  .arco-col-lg-pull-9 {
    right: 37.5%;
  }
  .arco-col-lg-pull-9.arco-col-rtl {
    right: unset;
    left: 37.5%;
  }
  .arco-col-lg-pull-10 {
    right: 41.66666667%;
  }
  .arco-col-lg-pull-10.arco-col-rtl {
    right: unset;
    left: 41.66666667%;
  }
  .arco-col-lg-pull-11 {
    right: 45.83333333%;
  }
  .arco-col-lg-pull-11.arco-col-rtl {
    right: unset;
    left: 45.83333333%;
  }
  .arco-col-lg-pull-12 {
    right: 50%;
  }
  .arco-col-lg-pull-12.arco-col-rtl {
    right: unset;
    left: 50%;
  }
  .arco-col-lg-pull-13 {
    right: 54.16666667%;
  }
  .arco-col-lg-pull-13.arco-col-rtl {
    right: unset;
    left: 54.16666667%;
  }
  .arco-col-lg-pull-14 {
    right: 58.33333333%;
  }
  .arco-col-lg-pull-14.arco-col-rtl {
    right: unset;
    left: 58.33333333%;
  }
  .arco-col-lg-pull-15 {
    right: 62.5%;
  }
  .arco-col-lg-pull-15.arco-col-rtl {
    right: unset;
    left: 62.5%;
  }
  .arco-col-lg-pull-16 {
    right: 66.66666667%;
  }
  .arco-col-lg-pull-16.arco-col-rtl {
    right: unset;
    left: 66.66666667%;
  }
  .arco-col-lg-pull-17 {
    right: 70.83333333%;
  }
  .arco-col-lg-pull-17.arco-col-rtl {
    right: unset;
    left: 70.83333333%;
  }
  .arco-col-lg-pull-18 {
    right: 75%;
  }
  .arco-col-lg-pull-18.arco-col-rtl {
    right: unset;
    left: 75%;
  }
  .arco-col-lg-pull-19 {
    right: 79.16666667%;
  }
  .arco-col-lg-pull-19.arco-col-rtl {
    right: unset;
    left: 79.16666667%;
  }
  .arco-col-lg-pull-20 {
    right: 83.33333333%;
  }
  .arco-col-lg-pull-20.arco-col-rtl {
    right: unset;
    left: 83.33333333%;
  }
  .arco-col-lg-pull-21 {
    right: 87.5%;
  }
  .arco-col-lg-pull-21.arco-col-rtl {
    right: unset;
    left: 87.5%;
  }
  .arco-col-lg-pull-22 {
    right: 91.66666667%;
  }
  .arco-col-lg-pull-22.arco-col-rtl {
    right: unset;
    left: 91.66666667%;
  }
  .arco-col-lg-pull-23 {
    right: 95.83333333%;
  }
  .arco-col-lg-pull-23.arco-col-rtl {
    right: unset;
    left: 95.83333333%;
  }
  .arco-col-lg-pull-24 {
    right: 100%;
  }
  .arco-col-lg-pull-24.arco-col-rtl {
    right: unset;
    left: 100%;
  }
  .arco-col-lg-push-0 {
    left: 0%;
  }
  .arco-col-lg-push-0.arco-col-rtl {
    left: unset;
    right: 0%;
  }
  .arco-col-lg-push-1 {
    left: 4.16666667%;
  }
  .arco-col-lg-push-1.arco-col-rtl {
    left: unset;
    right: 4.16666667%;
  }
  .arco-col-lg-push-2 {
    left: 8.33333333%;
  }
  .arco-col-lg-push-2.arco-col-rtl {
    left: unset;
    right: 8.33333333%;
  }
  .arco-col-lg-push-3 {
    left: 12.5%;
  }
  .arco-col-lg-push-3.arco-col-rtl {
    left: unset;
    right: 12.5%;
  }
  .arco-col-lg-push-4 {
    left: 16.66666667%;
  }
  .arco-col-lg-push-4.arco-col-rtl {
    left: unset;
    right: 16.66666667%;
  }
  .arco-col-lg-push-5 {
    left: 20.83333333%;
  }
  .arco-col-lg-push-5.arco-col-rtl {
    left: unset;
    right: 20.83333333%;
  }
  .arco-col-lg-push-6 {
    left: 25%;
  }
  .arco-col-lg-push-6.arco-col-rtl {
    left: unset;
    right: 25%;
  }
  .arco-col-lg-push-7 {
    left: 29.16666667%;
  }
  .arco-col-lg-push-7.arco-col-rtl {
    left: unset;
    right: 29.16666667%;
  }
  .arco-col-lg-push-8 {
    left: 33.33333333%;
  }
  .arco-col-lg-push-8.arco-col-rtl {
    left: unset;
    right: 33.33333333%;
  }
  .arco-col-lg-push-9 {
    left: 37.5%;
  }
  .arco-col-lg-push-9.arco-col-rtl {
    left: unset;
    right: 37.5%;
  }
  .arco-col-lg-push-10 {
    left: 41.66666667%;
  }
  .arco-col-lg-push-10.arco-col-rtl {
    left: unset;
    right: 41.66666667%;
  }
  .arco-col-lg-push-11 {
    left: 45.83333333%;
  }
  .arco-col-lg-push-11.arco-col-rtl {
    left: unset;
    right: 45.83333333%;
  }
  .arco-col-lg-push-12 {
    left: 50%;
  }
  .arco-col-lg-push-12.arco-col-rtl {
    left: unset;
    right: 50%;
  }
  .arco-col-lg-push-13 {
    left: 54.16666667%;
  }
  .arco-col-lg-push-13.arco-col-rtl {
    left: unset;
    right: 54.16666667%;
  }
  .arco-col-lg-push-14 {
    left: 58.33333333%;
  }
  .arco-col-lg-push-14.arco-col-rtl {
    left: unset;
    right: 58.33333333%;
  }
  .arco-col-lg-push-15 {
    left: 62.5%;
  }
  .arco-col-lg-push-15.arco-col-rtl {
    left: unset;
    right: 62.5%;
  }
  .arco-col-lg-push-16 {
    left: 66.66666667%;
  }
  .arco-col-lg-push-16.arco-col-rtl {
    left: unset;
    right: 66.66666667%;
  }
  .arco-col-lg-push-17 {
    left: 70.83333333%;
  }
  .arco-col-lg-push-17.arco-col-rtl {
    left: unset;
    right: 70.83333333%;
  }
  .arco-col-lg-push-18 {
    left: 75%;
  }
  .arco-col-lg-push-18.arco-col-rtl {
    left: unset;
    right: 75%;
  }
  .arco-col-lg-push-19 {
    left: 79.16666667%;
  }
  .arco-col-lg-push-19.arco-col-rtl {
    left: unset;
    right: 79.16666667%;
  }
  .arco-col-lg-push-20 {
    left: 83.33333333%;
  }
  .arco-col-lg-push-20.arco-col-rtl {
    left: unset;
    right: 83.33333333%;
  }
  .arco-col-lg-push-21 {
    left: 87.5%;
  }
  .arco-col-lg-push-21.arco-col-rtl {
    left: unset;
    right: 87.5%;
  }
  .arco-col-lg-push-22 {
    left: 91.66666667%;
  }
  .arco-col-lg-push-22.arco-col-rtl {
    left: unset;
    right: 91.66666667%;
  }
  .arco-col-lg-push-23 {
    left: 95.83333333%;
  }
  .arco-col-lg-push-23.arco-col-rtl {
    left: unset;
    right: 95.83333333%;
  }
  .arco-col-lg-push-24 {
    left: 100%;
  }
  .arco-col-lg-push-24.arco-col-rtl {
    left: unset;
    right: 100%;
  }
}
@media (min-width: 1200px) {
  .arco-col-xl-0 {
    display: none;
  }
  .arco-col-xl-1 {
    display: block;
    width: 4.16666667%;
    flex: 0 0 4.16666667%;
  }
  .arco-col-xl-2 {
    display: block;
    width: 8.33333333%;
    flex: 0 0 8.33333333%;
  }
  .arco-col-xl-3 {
    display: block;
    width: 12.5%;
    flex: 0 0 12.5%;
  }
  .arco-col-xl-4 {
    display: block;
    width: 16.66666667%;
    flex: 0 0 16.66666667%;
  }
  .arco-col-xl-5 {
    display: block;
    width: 20.83333333%;
    flex: 0 0 20.83333333%;
  }
  .arco-col-xl-6 {
    display: block;
    width: 25%;
    flex: 0 0 25%;
  }
  .arco-col-xl-7 {
    display: block;
    width: 29.16666667%;
    flex: 0 0 29.16666667%;
  }
  .arco-col-xl-8 {
    display: block;
    width: 33.33333333%;
    flex: 0 0 33.33333333%;
  }
  .arco-col-xl-9 {
    display: block;
    width: 37.5%;
    flex: 0 0 37.5%;
  }
  .arco-col-xl-10 {
    display: block;
    width: 41.66666667%;
    flex: 0 0 41.66666667%;
  }
  .arco-col-xl-11 {
    display: block;
    width: 45.83333333%;
    flex: 0 0 45.83333333%;
  }
  .arco-col-xl-12 {
    display: block;
    width: 50%;
    flex: 0 0 50%;
  }
  .arco-col-xl-13 {
    display: block;
    width: 54.16666667%;
    flex: 0 0 54.16666667%;
  }
  .arco-col-xl-14 {
    display: block;
    width: 58.33333333%;
    flex: 0 0 58.33333333%;
  }
  .arco-col-xl-15 {
    display: block;
    width: 62.5%;
    flex: 0 0 62.5%;
  }
  .arco-col-xl-16 {
    display: block;
    width: 66.66666667%;
    flex: 0 0 66.66666667%;
  }
  .arco-col-xl-17 {
    display: block;
    width: 70.83333333%;
    flex: 0 0 70.83333333%;
  }
  .arco-col-xl-18 {
    display: block;
    width: 75%;
    flex: 0 0 75%;
  }
  .arco-col-xl-19 {
    display: block;
    width: 79.16666667%;
    flex: 0 0 79.16666667%;
  }
  .arco-col-xl-20 {
    display: block;
    width: 83.33333333%;
    flex: 0 0 83.33333333%;
  }
  .arco-col-xl-21 {
    display: block;
    width: 87.5%;
    flex: 0 0 87.5%;
  }
  .arco-col-xl-22 {
    display: block;
    width: 91.66666667%;
    flex: 0 0 91.66666667%;
  }
  .arco-col-xl-23 {
    display: block;
    width: 95.83333333%;
    flex: 0 0 95.83333333%;
  }
  .arco-col-xl-24 {
    display: block;
    width: 100%;
    flex: 0 0 100%;
  }
  .arco-col-xl-offset-0 {
    margin-left: 0%;
  }
  .arco-col-xl-offset-0.arco-col-rtl {
    margin-left: 0;
    margin-right: 0%;
  }
  .arco-col-xl-offset-1 {
    margin-left: 4.16666667%;
  }
  .arco-col-xl-offset-1.arco-col-rtl {
    margin-left: 0;
    margin-right: 4.16666667%;
  }
  .arco-col-xl-offset-2 {
    margin-left: 8.33333333%;
  }
  .arco-col-xl-offset-2.arco-col-rtl {
    margin-left: 0;
    margin-right: 8.33333333%;
  }
  .arco-col-xl-offset-3 {
    margin-left: 12.5%;
  }
  .arco-col-xl-offset-3.arco-col-rtl {
    margin-left: 0;
    margin-right: 12.5%;
  }
  .arco-col-xl-offset-4 {
    margin-left: 16.66666667%;
  }
  .arco-col-xl-offset-4.arco-col-rtl {
    margin-left: 0;
    margin-right: 16.66666667%;
  }
  .arco-col-xl-offset-5 {
    margin-left: 20.83333333%;
  }
  .arco-col-xl-offset-5.arco-col-rtl {
    margin-left: 0;
    margin-right: 20.83333333%;
  }
  .arco-col-xl-offset-6 {
    margin-left: 25%;
  }
  .arco-col-xl-offset-6.arco-col-rtl {
    margin-left: 0;
    margin-right: 25%;
  }
  .arco-col-xl-offset-7 {
    margin-left: 29.16666667%;
  }
  .arco-col-xl-offset-7.arco-col-rtl {
    margin-left: 0;
    margin-right: 29.16666667%;
  }
  .arco-col-xl-offset-8 {
    margin-left: 33.33333333%;
  }
  .arco-col-xl-offset-8.arco-col-rtl {
    margin-left: 0;
    margin-right: 33.33333333%;
  }
  .arco-col-xl-offset-9 {
    margin-left: 37.5%;
  }
  .arco-col-xl-offset-9.arco-col-rtl {
    margin-left: 0;
    margin-right: 37.5%;
  }
  .arco-col-xl-offset-10 {
    margin-left: 41.66666667%;
  }
  .arco-col-xl-offset-10.arco-col-rtl {
    margin-left: 0;
    margin-right: 41.66666667%;
  }
  .arco-col-xl-offset-11 {
    margin-left: 45.83333333%;
  }
  .arco-col-xl-offset-11.arco-col-rtl {
    margin-left: 0;
    margin-right: 45.83333333%;
  }
  .arco-col-xl-offset-12 {
    margin-left: 50%;
  }
  .arco-col-xl-offset-12.arco-col-rtl {
    margin-left: 0;
    margin-right: 50%;
  }
  .arco-col-xl-offset-13 {
    margin-left: 54.16666667%;
  }
  .arco-col-xl-offset-13.arco-col-rtl {
    margin-left: 0;
    margin-right: 54.16666667%;
  }
  .arco-col-xl-offset-14 {
    margin-left: 58.33333333%;
  }
  .arco-col-xl-offset-14.arco-col-rtl {
    margin-left: 0;
    margin-right: 58.33333333%;
  }
  .arco-col-xl-offset-15 {
    margin-left: 62.5%;
  }
  .arco-col-xl-offset-15.arco-col-rtl {
    margin-left: 0;
    margin-right: 62.5%;
  }
  .arco-col-xl-offset-16 {
    margin-left: 66.66666667%;
  }
  .arco-col-xl-offset-16.arco-col-rtl {
    margin-left: 0;
    margin-right: 66.66666667%;
  }
  .arco-col-xl-offset-17 {
    margin-left: 70.83333333%;
  }
  .arco-col-xl-offset-17.arco-col-rtl {
    margin-left: 0;
    margin-right: 70.83333333%;
  }
  .arco-col-xl-offset-18 {
    margin-left: 75%;
  }
  .arco-col-xl-offset-18.arco-col-rtl {
    margin-left: 0;
    margin-right: 75%;
  }
  .arco-col-xl-offset-19 {
    margin-left: 79.16666667%;
  }
  .arco-col-xl-offset-19.arco-col-rtl {
    margin-left: 0;
    margin-right: 79.16666667%;
  }
  .arco-col-xl-offset-20 {
    margin-left: 83.33333333%;
  }
  .arco-col-xl-offset-20.arco-col-rtl {
    margin-left: 0;
    margin-right: 83.33333333%;
  }
  .arco-col-xl-offset-21 {
    margin-left: 87.5%;
  }
  .arco-col-xl-offset-21.arco-col-rtl {
    margin-left: 0;
    margin-right: 87.5%;
  }
  .arco-col-xl-offset-22 {
    margin-left: 91.66666667%;
  }
  .arco-col-xl-offset-22.arco-col-rtl {
    margin-left: 0;
    margin-right: 91.66666667%;
  }
  .arco-col-xl-offset-23 {
    margin-left: 95.83333333%;
  }
  .arco-col-xl-offset-23.arco-col-rtl {
    margin-left: 0;
    margin-right: 95.83333333%;
  }
  .arco-col-xl-order-0 {
    order: 0;
  }
  .arco-col-xl-order-1 {
    order: 1;
  }
  .arco-col-xl-order-2 {
    order: 2;
  }
  .arco-col-xl-order-3 {
    order: 3;
  }
  .arco-col-xl-order-4 {
    order: 4;
  }
  .arco-col-xl-order-5 {
    order: 5;
  }
  .arco-col-xl-order-6 {
    order: 6;
  }
  .arco-col-xl-order-7 {
    order: 7;
  }
  .arco-col-xl-order-8 {
    order: 8;
  }
  .arco-col-xl-order-9 {
    order: 9;
  }
  .arco-col-xl-order-10 {
    order: 10;
  }
  .arco-col-xl-order-11 {
    order: 11;
  }
  .arco-col-xl-order-12 {
    order: 12;
  }
  .arco-col-xl-order-13 {
    order: 13;
  }
  .arco-col-xl-order-14 {
    order: 14;
  }
  .arco-col-xl-order-15 {
    order: 15;
  }
  .arco-col-xl-order-16 {
    order: 16;
  }
  .arco-col-xl-order-17 {
    order: 17;
  }
  .arco-col-xl-order-18 {
    order: 18;
  }
  .arco-col-xl-order-19 {
    order: 19;
  }
  .arco-col-xl-order-20 {
    order: 20;
  }
  .arco-col-xl-order-21 {
    order: 21;
  }
  .arco-col-xl-order-22 {
    order: 22;
  }
  .arco-col-xl-order-23 {
    order: 23;
  }
  .arco-col-xl-order-24 {
    order: 24;
  }
  .arco-col-xl-pull-0 {
    right: 0%;
  }
  .arco-col-xl-pull-0.arco-col-rtl {
    right: unset;
    left: 0%;
  }
  .arco-col-xl-pull-1 {
    right: 4.16666667%;
  }
  .arco-col-xl-pull-1.arco-col-rtl {
    right: unset;
    left: 4.16666667%;
  }
  .arco-col-xl-pull-2 {
    right: 8.33333333%;
  }
  .arco-col-xl-pull-2.arco-col-rtl {
    right: unset;
    left: 8.33333333%;
  }
  .arco-col-xl-pull-3 {
    right: 12.5%;
  }
  .arco-col-xl-pull-3.arco-col-rtl {
    right: unset;
    left: 12.5%;
  }
  .arco-col-xl-pull-4 {
    right: 16.66666667%;
  }
  .arco-col-xl-pull-4.arco-col-rtl {
    right: unset;
    left: 16.66666667%;
  }
  .arco-col-xl-pull-5 {
    right: 20.83333333%;
  }
  .arco-col-xl-pull-5.arco-col-rtl {
    right: unset;
    left: 20.83333333%;
  }
  .arco-col-xl-pull-6 {
    right: 25%;
  }
  .arco-col-xl-pull-6.arco-col-rtl {
    right: unset;
    left: 25%;
  }
  .arco-col-xl-pull-7 {
    right: 29.16666667%;
  }
  .arco-col-xl-pull-7.arco-col-rtl {
    right: unset;
    left: 29.16666667%;
  }
  .arco-col-xl-pull-8 {
    right: 33.33333333%;
  }
  .arco-col-xl-pull-8.arco-col-rtl {
    right: unset;
    left: 33.33333333%;
  }
  .arco-col-xl-pull-9 {
    right: 37.5%;
  }
  .arco-col-xl-pull-9.arco-col-rtl {
    right: unset;
    left: 37.5%;
  }
  .arco-col-xl-pull-10 {
    right: 41.66666667%;
  }
  .arco-col-xl-pull-10.arco-col-rtl {
    right: unset;
    left: 41.66666667%;
  }
  .arco-col-xl-pull-11 {
    right: 45.83333333%;
  }
  .arco-col-xl-pull-11.arco-col-rtl {
    right: unset;
    left: 45.83333333%;
  }
  .arco-col-xl-pull-12 {
    right: 50%;
  }
  .arco-col-xl-pull-12.arco-col-rtl {
    right: unset;
    left: 50%;
  }
  .arco-col-xl-pull-13 {
    right: 54.16666667%;
  }
  .arco-col-xl-pull-13.arco-col-rtl {
    right: unset;
    left: 54.16666667%;
  }
  .arco-col-xl-pull-14 {
    right: 58.33333333%;
  }
  .arco-col-xl-pull-14.arco-col-rtl {
    right: unset;
    left: 58.33333333%;
  }
  .arco-col-xl-pull-15 {
    right: 62.5%;
  }
  .arco-col-xl-pull-15.arco-col-rtl {
    right: unset;
    left: 62.5%;
  }
  .arco-col-xl-pull-16 {
    right: 66.66666667%;
  }
  .arco-col-xl-pull-16.arco-col-rtl {
    right: unset;
    left: 66.66666667%;
  }
  .arco-col-xl-pull-17 {
    right: 70.83333333%;
  }
  .arco-col-xl-pull-17.arco-col-rtl {
    right: unset;
    left: 70.83333333%;
  }
  .arco-col-xl-pull-18 {
    right: 75%;
  }
  .arco-col-xl-pull-18.arco-col-rtl {
    right: unset;
    left: 75%;
  }
  .arco-col-xl-pull-19 {
    right: 79.16666667%;
  }
  .arco-col-xl-pull-19.arco-col-rtl {
    right: unset;
    left: 79.16666667%;
  }
  .arco-col-xl-pull-20 {
    right: 83.33333333%;
  }
  .arco-col-xl-pull-20.arco-col-rtl {
    right: unset;
    left: 83.33333333%;
  }
  .arco-col-xl-pull-21 {
    right: 87.5%;
  }
  .arco-col-xl-pull-21.arco-col-rtl {
    right: unset;
    left: 87.5%;
  }
  .arco-col-xl-pull-22 {
    right: 91.66666667%;
  }
  .arco-col-xl-pull-22.arco-col-rtl {
    right: unset;
    left: 91.66666667%;
  }
  .arco-col-xl-pull-23 {
    right: 95.83333333%;
  }
  .arco-col-xl-pull-23.arco-col-rtl {
    right: unset;
    left: 95.83333333%;
  }
  .arco-col-xl-pull-24 {
    right: 100%;
  }
  .arco-col-xl-pull-24.arco-col-rtl {
    right: unset;
    left: 100%;
  }
  .arco-col-xl-push-0 {
    left: 0%;
  }
  .arco-col-xl-push-0.arco-col-rtl {
    left: unset;
    right: 0%;
  }
  .arco-col-xl-push-1 {
    left: 4.16666667%;
  }
  .arco-col-xl-push-1.arco-col-rtl {
    left: unset;
    right: 4.16666667%;
  }
  .arco-col-xl-push-2 {
    left: 8.33333333%;
  }
  .arco-col-xl-push-2.arco-col-rtl {
    left: unset;
    right: 8.33333333%;
  }
  .arco-col-xl-push-3 {
    left: 12.5%;
  }
  .arco-col-xl-push-3.arco-col-rtl {
    left: unset;
    right: 12.5%;
  }
  .arco-col-xl-push-4 {
    left: 16.66666667%;
  }
  .arco-col-xl-push-4.arco-col-rtl {
    left: unset;
    right: 16.66666667%;
  }
  .arco-col-xl-push-5 {
    left: 20.83333333%;
  }
  .arco-col-xl-push-5.arco-col-rtl {
    left: unset;
    right: 20.83333333%;
  }
  .arco-col-xl-push-6 {
    left: 25%;
  }
  .arco-col-xl-push-6.arco-col-rtl {
    left: unset;
    right: 25%;
  }
  .arco-col-xl-push-7 {
    left: 29.16666667%;
  }
  .arco-col-xl-push-7.arco-col-rtl {
    left: unset;
    right: 29.16666667%;
  }
  .arco-col-xl-push-8 {
    left: 33.33333333%;
  }
  .arco-col-xl-push-8.arco-col-rtl {
    left: unset;
    right: 33.33333333%;
  }
  .arco-col-xl-push-9 {
    left: 37.5%;
  }
  .arco-col-xl-push-9.arco-col-rtl {
    left: unset;
    right: 37.5%;
  }
  .arco-col-xl-push-10 {
    left: 41.66666667%;
  }
  .arco-col-xl-push-10.arco-col-rtl {
    left: unset;
    right: 41.66666667%;
  }
  .arco-col-xl-push-11 {
    left: 45.83333333%;
  }
  .arco-col-xl-push-11.arco-col-rtl {
    left: unset;
    right: 45.83333333%;
  }
  .arco-col-xl-push-12 {
    left: 50%;
  }
  .arco-col-xl-push-12.arco-col-rtl {
    left: unset;
    right: 50%;
  }
  .arco-col-xl-push-13 {
    left: 54.16666667%;
  }
  .arco-col-xl-push-13.arco-col-rtl {
    left: unset;
    right: 54.16666667%;
  }
  .arco-col-xl-push-14 {
    left: 58.33333333%;
  }
  .arco-col-xl-push-14.arco-col-rtl {
    left: unset;
    right: 58.33333333%;
  }
  .arco-col-xl-push-15 {
    left: 62.5%;
  }
  .arco-col-xl-push-15.arco-col-rtl {
    left: unset;
    right: 62.5%;
  }
  .arco-col-xl-push-16 {
    left: 66.66666667%;
  }
  .arco-col-xl-push-16.arco-col-rtl {
    left: unset;
    right: 66.66666667%;
  }
  .arco-col-xl-push-17 {
    left: 70.83333333%;
  }
  .arco-col-xl-push-17.arco-col-rtl {
    left: unset;
    right: 70.83333333%;
  }
  .arco-col-xl-push-18 {
    left: 75%;
  }
  .arco-col-xl-push-18.arco-col-rtl {
    left: unset;
    right: 75%;
  }
  .arco-col-xl-push-19 {
    left: 79.16666667%;
  }
  .arco-col-xl-push-19.arco-col-rtl {
    left: unset;
    right: 79.16666667%;
  }
  .arco-col-xl-push-20 {
    left: 83.33333333%;
  }
  .arco-col-xl-push-20.arco-col-rtl {
    left: unset;
    right: 83.33333333%;
  }
  .arco-col-xl-push-21 {
    left: 87.5%;
  }
  .arco-col-xl-push-21.arco-col-rtl {
    left: unset;
    right: 87.5%;
  }
  .arco-col-xl-push-22 {
    left: 91.66666667%;
  }
  .arco-col-xl-push-22.arco-col-rtl {
    left: unset;
    right: 91.66666667%;
  }
  .arco-col-xl-push-23 {
    left: 95.83333333%;
  }
  .arco-col-xl-push-23.arco-col-rtl {
    left: unset;
    right: 95.83333333%;
  }
  .arco-col-xl-push-24 {
    left: 100%;
  }
  .arco-col-xl-push-24.arco-col-rtl {
    left: unset;
    right: 100%;
  }
}
@media (min-width: 1600px) {
  .arco-col-xxl-0 {
    display: none;
  }
  .arco-col-xxl-1 {
    display: block;
    width: 4.16666667%;
    flex: 0 0 4.16666667%;
  }
  .arco-col-xxl-2 {
    display: block;
    width: 8.33333333%;
    flex: 0 0 8.33333333%;
  }
  .arco-col-xxl-3 {
    display: block;
    width: 12.5%;
    flex: 0 0 12.5%;
  }
  .arco-col-xxl-4 {
    display: block;
    width: 16.66666667%;
    flex: 0 0 16.66666667%;
  }
  .arco-col-xxl-5 {
    display: block;
    width: 20.83333333%;
    flex: 0 0 20.83333333%;
  }
  .arco-col-xxl-6 {
    display: block;
    width: 25%;
    flex: 0 0 25%;
  }
  .arco-col-xxl-7 {
    display: block;
    width: 29.16666667%;
    flex: 0 0 29.16666667%;
  }
  .arco-col-xxl-8 {
    display: block;
    width: 33.33333333%;
    flex: 0 0 33.33333333%;
  }
  .arco-col-xxl-9 {
    display: block;
    width: 37.5%;
    flex: 0 0 37.5%;
  }
  .arco-col-xxl-10 {
    display: block;
    width: 41.66666667%;
    flex: 0 0 41.66666667%;
  }
  .arco-col-xxl-11 {
    display: block;
    width: 45.83333333%;
    flex: 0 0 45.83333333%;
  }
  .arco-col-xxl-12 {
    display: block;
    width: 50%;
    flex: 0 0 50%;
  }
  .arco-col-xxl-13 {
    display: block;
    width: 54.16666667%;
    flex: 0 0 54.16666667%;
  }
  .arco-col-xxl-14 {
    display: block;
    width: 58.33333333%;
    flex: 0 0 58.33333333%;
  }
  .arco-col-xxl-15 {
    display: block;
    width: 62.5%;
    flex: 0 0 62.5%;
  }
  .arco-col-xxl-16 {
    display: block;
    width: 66.66666667%;
    flex: 0 0 66.66666667%;
  }
  .arco-col-xxl-17 {
    display: block;
    width: 70.83333333%;
    flex: 0 0 70.83333333%;
  }
  .arco-col-xxl-18 {
    display: block;
    width: 75%;
    flex: 0 0 75%;
  }
  .arco-col-xxl-19 {
    display: block;
    width: 79.16666667%;
    flex: 0 0 79.16666667%;
  }
  .arco-col-xxl-20 {
    display: block;
    width: 83.33333333%;
    flex: 0 0 83.33333333%;
  }
  .arco-col-xxl-21 {
    display: block;
    width: 87.5%;
    flex: 0 0 87.5%;
  }
  .arco-col-xxl-22 {
    display: block;
    width: 91.66666667%;
    flex: 0 0 91.66666667%;
  }
  .arco-col-xxl-23 {
    display: block;
    width: 95.83333333%;
    flex: 0 0 95.83333333%;
  }
  .arco-col-xxl-24 {
    display: block;
    width: 100%;
    flex: 0 0 100%;
  }
  .arco-col-xxl-offset-0 {
    margin-left: 0%;
  }
  .arco-col-xxl-offset-0.arco-col-rtl {
    margin-left: 0;
    margin-right: 0%;
  }
  .arco-col-xxl-offset-1 {
    margin-left: 4.16666667%;
  }
  .arco-col-xxl-offset-1.arco-col-rtl {
    margin-left: 0;
    margin-right: 4.16666667%;
  }
  .arco-col-xxl-offset-2 {
    margin-left: 8.33333333%;
  }
  .arco-col-xxl-offset-2.arco-col-rtl {
    margin-left: 0;
    margin-right: 8.33333333%;
  }
  .arco-col-xxl-offset-3 {
    margin-left: 12.5%;
  }
  .arco-col-xxl-offset-3.arco-col-rtl {
    margin-left: 0;
    margin-right: 12.5%;
  }
  .arco-col-xxl-offset-4 {
    margin-left: 16.66666667%;
  }
  .arco-col-xxl-offset-4.arco-col-rtl {
    margin-left: 0;
    margin-right: 16.66666667%;
  }
  .arco-col-xxl-offset-5 {
    margin-left: 20.83333333%;
  }
  .arco-col-xxl-offset-5.arco-col-rtl {
    margin-left: 0;
    margin-right: 20.83333333%;
  }
  .arco-col-xxl-offset-6 {
    margin-left: 25%;
  }
  .arco-col-xxl-offset-6.arco-col-rtl {
    margin-left: 0;
    margin-right: 25%;
  }
  .arco-col-xxl-offset-7 {
    margin-left: 29.16666667%;
  }
  .arco-col-xxl-offset-7.arco-col-rtl {
    margin-left: 0;
    margin-right: 29.16666667%;
  }
  .arco-col-xxl-offset-8 {
    margin-left: 33.33333333%;
  }
  .arco-col-xxl-offset-8.arco-col-rtl {
    margin-left: 0;
    margin-right: 33.33333333%;
  }
  .arco-col-xxl-offset-9 {
    margin-left: 37.5%;
  }
  .arco-col-xxl-offset-9.arco-col-rtl {
    margin-left: 0;
    margin-right: 37.5%;
  }
  .arco-col-xxl-offset-10 {
    margin-left: 41.66666667%;
  }
  .arco-col-xxl-offset-10.arco-col-rtl {
    margin-left: 0;
    margin-right: 41.66666667%;
  }
  .arco-col-xxl-offset-11 {
    margin-left: 45.83333333%;
  }
  .arco-col-xxl-offset-11.arco-col-rtl {
    margin-left: 0;
    margin-right: 45.83333333%;
  }
  .arco-col-xxl-offset-12 {
    margin-left: 50%;
  }
  .arco-col-xxl-offset-12.arco-col-rtl {
    margin-left: 0;
    margin-right: 50%;
  }
  .arco-col-xxl-offset-13 {
    margin-left: 54.16666667%;
  }
  .arco-col-xxl-offset-13.arco-col-rtl {
    margin-left: 0;
    margin-right: 54.16666667%;
  }
  .arco-col-xxl-offset-14 {
    margin-left: 58.33333333%;
  }
  .arco-col-xxl-offset-14.arco-col-rtl {
    margin-left: 0;
    margin-right: 58.33333333%;
  }
  .arco-col-xxl-offset-15 {
    margin-left: 62.5%;
  }
  .arco-col-xxl-offset-15.arco-col-rtl {
    margin-left: 0;
    margin-right: 62.5%;
  }
  .arco-col-xxl-offset-16 {
    margin-left: 66.66666667%;
  }
  .arco-col-xxl-offset-16.arco-col-rtl {
    margin-left: 0;
    margin-right: 66.66666667%;
  }
  .arco-col-xxl-offset-17 {
    margin-left: 70.83333333%;
  }
  .arco-col-xxl-offset-17.arco-col-rtl {
    margin-left: 0;
    margin-right: 70.83333333%;
  }
  .arco-col-xxl-offset-18 {
    margin-left: 75%;
  }
  .arco-col-xxl-offset-18.arco-col-rtl {
    margin-left: 0;
    margin-right: 75%;
  }
  .arco-col-xxl-offset-19 {
    margin-left: 79.16666667%;
  }
  .arco-col-xxl-offset-19.arco-col-rtl {
    margin-left: 0;
    margin-right: 79.16666667%;
  }
  .arco-col-xxl-offset-20 {
    margin-left: 83.33333333%;
  }
  .arco-col-xxl-offset-20.arco-col-rtl {
    margin-left: 0;
    margin-right: 83.33333333%;
  }
  .arco-col-xxl-offset-21 {
    margin-left: 87.5%;
  }
  .arco-col-xxl-offset-21.arco-col-rtl {
    margin-left: 0;
    margin-right: 87.5%;
  }
  .arco-col-xxl-offset-22 {
    margin-left: 91.66666667%;
  }
  .arco-col-xxl-offset-22.arco-col-rtl {
    margin-left: 0;
    margin-right: 91.66666667%;
  }
  .arco-col-xxl-offset-23 {
    margin-left: 95.83333333%;
  }
  .arco-col-xxl-offset-23.arco-col-rtl {
    margin-left: 0;
    margin-right: 95.83333333%;
  }
  .arco-col-xxl-order-0 {
    order: 0;
  }
  .arco-col-xxl-order-1 {
    order: 1;
  }
  .arco-col-xxl-order-2 {
    order: 2;
  }
  .arco-col-xxl-order-3 {
    order: 3;
  }
  .arco-col-xxl-order-4 {
    order: 4;
  }
  .arco-col-xxl-order-5 {
    order: 5;
  }
  .arco-col-xxl-order-6 {
    order: 6;
  }
  .arco-col-xxl-order-7 {
    order: 7;
  }
  .arco-col-xxl-order-8 {
    order: 8;
  }
  .arco-col-xxl-order-9 {
    order: 9;
  }
  .arco-col-xxl-order-10 {
    order: 10;
  }
  .arco-col-xxl-order-11 {
    order: 11;
  }
  .arco-col-xxl-order-12 {
    order: 12;
  }
  .arco-col-xxl-order-13 {
    order: 13;
  }
  .arco-col-xxl-order-14 {
    order: 14;
  }
  .arco-col-xxl-order-15 {
    order: 15;
  }
  .arco-col-xxl-order-16 {
    order: 16;
  }
  .arco-col-xxl-order-17 {
    order: 17;
  }
  .arco-col-xxl-order-18 {
    order: 18;
  }
  .arco-col-xxl-order-19 {
    order: 19;
  }
  .arco-col-xxl-order-20 {
    order: 20;
  }
  .arco-col-xxl-order-21 {
    order: 21;
  }
  .arco-col-xxl-order-22 {
    order: 22;
  }
  .arco-col-xxl-order-23 {
    order: 23;
  }
  .arco-col-xxl-order-24 {
    order: 24;
  }
  .arco-col-xxl-pull-0 {
    right: 0%;
  }
  .arco-col-xxl-pull-0.arco-col-rtl {
    right: unset;
    left: 0%;
  }
  .arco-col-xxl-pull-1 {
    right: 4.16666667%;
  }
  .arco-col-xxl-pull-1.arco-col-rtl {
    right: unset;
    left: 4.16666667%;
  }
  .arco-col-xxl-pull-2 {
    right: 8.33333333%;
  }
  .arco-col-xxl-pull-2.arco-col-rtl {
    right: unset;
    left: 8.33333333%;
  }
  .arco-col-xxl-pull-3 {
    right: 12.5%;
  }
  .arco-col-xxl-pull-3.arco-col-rtl {
    right: unset;
    left: 12.5%;
  }
  .arco-col-xxl-pull-4 {
    right: 16.66666667%;
  }
  .arco-col-xxl-pull-4.arco-col-rtl {
    right: unset;
    left: 16.66666667%;
  }
  .arco-col-xxl-pull-5 {
    right: 20.83333333%;
  }
  .arco-col-xxl-pull-5.arco-col-rtl {
    right: unset;
    left: 20.83333333%;
  }
  .arco-col-xxl-pull-6 {
    right: 25%;
  }
  .arco-col-xxl-pull-6.arco-col-rtl {
    right: unset;
    left: 25%;
  }
  .arco-col-xxl-pull-7 {
    right: 29.16666667%;
  }
  .arco-col-xxl-pull-7.arco-col-rtl {
    right: unset;
    left: 29.16666667%;
  }
  .arco-col-xxl-pull-8 {
    right: 33.33333333%;
  }
  .arco-col-xxl-pull-8.arco-col-rtl {
    right: unset;
    left: 33.33333333%;
  }
  .arco-col-xxl-pull-9 {
    right: 37.5%;
  }
  .arco-col-xxl-pull-9.arco-col-rtl {
    right: unset;
    left: 37.5%;
  }
  .arco-col-xxl-pull-10 {
    right: 41.66666667%;
  }
  .arco-col-xxl-pull-10.arco-col-rtl {
    right: unset;
    left: 41.66666667%;
  }
  .arco-col-xxl-pull-11 {
    right: 45.83333333%;
  }
  .arco-col-xxl-pull-11.arco-col-rtl {
    right: unset;
    left: 45.83333333%;
  }
  .arco-col-xxl-pull-12 {
    right: 50%;
  }
  .arco-col-xxl-pull-12.arco-col-rtl {
    right: unset;
    left: 50%;
  }
  .arco-col-xxl-pull-13 {
    right: 54.16666667%;
  }
  .arco-col-xxl-pull-13.arco-col-rtl {
    right: unset;
    left: 54.16666667%;
  }
  .arco-col-xxl-pull-14 {
    right: 58.33333333%;
  }
  .arco-col-xxl-pull-14.arco-col-rtl {
    right: unset;
    left: 58.33333333%;
  }
  .arco-col-xxl-pull-15 {
    right: 62.5%;
  }
  .arco-col-xxl-pull-15.arco-col-rtl {
    right: unset;
    left: 62.5%;
  }
  .arco-col-xxl-pull-16 {
    right: 66.66666667%;
  }
  .arco-col-xxl-pull-16.arco-col-rtl {
    right: unset;
    left: 66.66666667%;
  }
  .arco-col-xxl-pull-17 {
    right: 70.83333333%;
  }
  .arco-col-xxl-pull-17.arco-col-rtl {
    right: unset;
    left: 70.83333333%;
  }
  .arco-col-xxl-pull-18 {
    right: 75%;
  }
  .arco-col-xxl-pull-18.arco-col-rtl {
    right: unset;
    left: 75%;
  }
  .arco-col-xxl-pull-19 {
    right: 79.16666667%;
  }
  .arco-col-xxl-pull-19.arco-col-rtl {
    right: unset;
    left: 79.16666667%;
  }
  .arco-col-xxl-pull-20 {
    right: 83.33333333%;
  }
  .arco-col-xxl-pull-20.arco-col-rtl {
    right: unset;
    left: 83.33333333%;
  }
  .arco-col-xxl-pull-21 {
    right: 87.5%;
  }
  .arco-col-xxl-pull-21.arco-col-rtl {
    right: unset;
    left: 87.5%;
  }
  .arco-col-xxl-pull-22 {
    right: 91.66666667%;
  }
  .arco-col-xxl-pull-22.arco-col-rtl {
    right: unset;
    left: 91.66666667%;
  }
  .arco-col-xxl-pull-23 {
    right: 95.83333333%;
  }
  .arco-col-xxl-pull-23.arco-col-rtl {
    right: unset;
    left: 95.83333333%;
  }
  .arco-col-xxl-pull-24 {
    right: 100%;
  }
  .arco-col-xxl-pull-24.arco-col-rtl {
    right: unset;
    left: 100%;
  }
  .arco-col-xxl-push-0 {
    left: 0%;
  }
  .arco-col-xxl-push-0.arco-col-rtl {
    left: unset;
    right: 0%;
  }
  .arco-col-xxl-push-1 {
    left: 4.16666667%;
  }
  .arco-col-xxl-push-1.arco-col-rtl {
    left: unset;
    right: 4.16666667%;
  }
  .arco-col-xxl-push-2 {
    left: 8.33333333%;
  }
  .arco-col-xxl-push-2.arco-col-rtl {
    left: unset;
    right: 8.33333333%;
  }
  .arco-col-xxl-push-3 {
    left: 12.5%;
  }
  .arco-col-xxl-push-3.arco-col-rtl {
    left: unset;
    right: 12.5%;
  }
  .arco-col-xxl-push-4 {
    left: 16.66666667%;
  }
  .arco-col-xxl-push-4.arco-col-rtl {
    left: unset;
    right: 16.66666667%;
  }
  .arco-col-xxl-push-5 {
    left: 20.83333333%;
  }
  .arco-col-xxl-push-5.arco-col-rtl {
    left: unset;
    right: 20.83333333%;
  }
  .arco-col-xxl-push-6 {
    left: 25%;
  }
  .arco-col-xxl-push-6.arco-col-rtl {
    left: unset;
    right: 25%;
  }
  .arco-col-xxl-push-7 {
    left: 29.16666667%;
  }
  .arco-col-xxl-push-7.arco-col-rtl {
    left: unset;
    right: 29.16666667%;
  }
  .arco-col-xxl-push-8 {
    left: 33.33333333%;
  }
  .arco-col-xxl-push-8.arco-col-rtl {
    left: unset;
    right: 33.33333333%;
  }
  .arco-col-xxl-push-9 {
    left: 37.5%;
  }
  .arco-col-xxl-push-9.arco-col-rtl {
    left: unset;
    right: 37.5%;
  }
  .arco-col-xxl-push-10 {
    left: 41.66666667%;
  }
  .arco-col-xxl-push-10.arco-col-rtl {
    left: unset;
    right: 41.66666667%;
  }
  .arco-col-xxl-push-11 {
    left: 45.83333333%;
  }
  .arco-col-xxl-push-11.arco-col-rtl {
    left: unset;
    right: 45.83333333%;
  }
  .arco-col-xxl-push-12 {
    left: 50%;
  }
  .arco-col-xxl-push-12.arco-col-rtl {
    left: unset;
    right: 50%;
  }
  .arco-col-xxl-push-13 {
    left: 54.16666667%;
  }
  .arco-col-xxl-push-13.arco-col-rtl {
    left: unset;
    right: 54.16666667%;
  }
  .arco-col-xxl-push-14 {
    left: 58.33333333%;
  }
  .arco-col-xxl-push-14.arco-col-rtl {
    left: unset;
    right: 58.33333333%;
  }
  .arco-col-xxl-push-15 {
    left: 62.5%;
  }
  .arco-col-xxl-push-15.arco-col-rtl {
    left: unset;
    right: 62.5%;
  }
  .arco-col-xxl-push-16 {
    left: 66.66666667%;
  }
  .arco-col-xxl-push-16.arco-col-rtl {
    left: unset;
    right: 66.66666667%;
  }
  .arco-col-xxl-push-17 {
    left: 70.83333333%;
  }
  .arco-col-xxl-push-17.arco-col-rtl {
    left: unset;
    right: 70.83333333%;
  }
  .arco-col-xxl-push-18 {
    left: 75%;
  }
  .arco-col-xxl-push-18.arco-col-rtl {
    left: unset;
    right: 75%;
  }
  .arco-col-xxl-push-19 {
    left: 79.16666667%;
  }
  .arco-col-xxl-push-19.arco-col-rtl {
    left: unset;
    right: 79.16666667%;
  }
  .arco-col-xxl-push-20 {
    left: 83.33333333%;
  }
  .arco-col-xxl-push-20.arco-col-rtl {
    left: unset;
    right: 83.33333333%;
  }
  .arco-col-xxl-push-21 {
    left: 87.5%;
  }
  .arco-col-xxl-push-21.arco-col-rtl {
    left: unset;
    right: 87.5%;
  }
  .arco-col-xxl-push-22 {
    left: 91.66666667%;
  }
  .arco-col-xxl-push-22.arco-col-rtl {
    left: unset;
    right: 91.66666667%;
  }
  .arco-col-xxl-push-23 {
    left: 95.83333333%;
  }
  .arco-col-xxl-push-23.arco-col-rtl {
    left: unset;
    right: 95.83333333%;
  }
  .arco-col-xxl-push-24 {
    left: 100%;
  }
  .arco-col-xxl-push-24.arco-col-rtl {
    left: unset;
    right: 100%;
  }
}
@media (min-width: 2000px) {
  .arco-col-xxxl-0 {
    display: none;
  }
  .arco-col-xxxl-1 {
    display: block;
    width: 4.16666667%;
    flex: 0 0 4.16666667%;
  }
  .arco-col-xxxl-2 {
    display: block;
    width: 8.33333333%;
    flex: 0 0 8.33333333%;
  }
  .arco-col-xxxl-3 {
    display: block;
    width: 12.5%;
    flex: 0 0 12.5%;
  }
  .arco-col-xxxl-4 {
    display: block;
    width: 16.66666667%;
    flex: 0 0 16.66666667%;
  }
  .arco-col-xxxl-5 {
    display: block;
    width: 20.83333333%;
    flex: 0 0 20.83333333%;
  }
  .arco-col-xxxl-6 {
    display: block;
    width: 25%;
    flex: 0 0 25%;
  }
  .arco-col-xxxl-7 {
    display: block;
    width: 29.16666667%;
    flex: 0 0 29.16666667%;
  }
  .arco-col-xxxl-8 {
    display: block;
    width: 33.33333333%;
    flex: 0 0 33.33333333%;
  }
  .arco-col-xxxl-9 {
    display: block;
    width: 37.5%;
    flex: 0 0 37.5%;
  }
  .arco-col-xxxl-10 {
    display: block;
    width: 41.66666667%;
    flex: 0 0 41.66666667%;
  }
  .arco-col-xxxl-11 {
    display: block;
    width: 45.83333333%;
    flex: 0 0 45.83333333%;
  }
  .arco-col-xxxl-12 {
    display: block;
    width: 50%;
    flex: 0 0 50%;
  }
  .arco-col-xxxl-13 {
    display: block;
    width: 54.16666667%;
    flex: 0 0 54.16666667%;
  }
  .arco-col-xxxl-14 {
    display: block;
    width: 58.33333333%;
    flex: 0 0 58.33333333%;
  }
  .arco-col-xxxl-15 {
    display: block;
    width: 62.5%;
    flex: 0 0 62.5%;
  }
  .arco-col-xxxl-16 {
    display: block;
    width: 66.66666667%;
    flex: 0 0 66.66666667%;
  }
  .arco-col-xxxl-17 {
    display: block;
    width: 70.83333333%;
    flex: 0 0 70.83333333%;
  }
  .arco-col-xxxl-18 {
    display: block;
    width: 75%;
    flex: 0 0 75%;
  }
  .arco-col-xxxl-19 {
    display: block;
    width: 79.16666667%;
    flex: 0 0 79.16666667%;
  }
  .arco-col-xxxl-20 {
    display: block;
    width: 83.33333333%;
    flex: 0 0 83.33333333%;
  }
  .arco-col-xxxl-21 {
    display: block;
    width: 87.5%;
    flex: 0 0 87.5%;
  }
  .arco-col-xxxl-22 {
    display: block;
    width: 91.66666667%;
    flex: 0 0 91.66666667%;
  }
  .arco-col-xxxl-23 {
    display: block;
    width: 95.83333333%;
    flex: 0 0 95.83333333%;
  }
  .arco-col-xxxl-24 {
    display: block;
    width: 100%;
    flex: 0 0 100%;
  }
  .arco-col-xxxl-offset-0 {
    margin-left: 0%;
  }
  .arco-col-xxxl-offset-0.arco-col-rtl {
    margin-left: 0;
    margin-right: 0%;
  }
  .arco-col-xxxl-offset-1 {
    margin-left: 4.16666667%;
  }
  .arco-col-xxxl-offset-1.arco-col-rtl {
    margin-left: 0;
    margin-right: 4.16666667%;
  }
  .arco-col-xxxl-offset-2 {
    margin-left: 8.33333333%;
  }
  .arco-col-xxxl-offset-2.arco-col-rtl {
    margin-left: 0;
    margin-right: 8.33333333%;
  }
  .arco-col-xxxl-offset-3 {
    margin-left: 12.5%;
  }
  .arco-col-xxxl-offset-3.arco-col-rtl {
    margin-left: 0;
    margin-right: 12.5%;
  }
  .arco-col-xxxl-offset-4 {
    margin-left: 16.66666667%;
  }
  .arco-col-xxxl-offset-4.arco-col-rtl {
    margin-left: 0;
    margin-right: 16.66666667%;
  }
  .arco-col-xxxl-offset-5 {
    margin-left: 20.83333333%;
  }
  .arco-col-xxxl-offset-5.arco-col-rtl {
    margin-left: 0;
    margin-right: 20.83333333%;
  }
  .arco-col-xxxl-offset-6 {
    margin-left: 25%;
  }
  .arco-col-xxxl-offset-6.arco-col-rtl {
    margin-left: 0;
    margin-right: 25%;
  }
  .arco-col-xxxl-offset-7 {
    margin-left: 29.16666667%;
  }
  .arco-col-xxxl-offset-7.arco-col-rtl {
    margin-left: 0;
    margin-right: 29.16666667%;
  }
  .arco-col-xxxl-offset-8 {
    margin-left: 33.33333333%;
  }
  .arco-col-xxxl-offset-8.arco-col-rtl {
    margin-left: 0;
    margin-right: 33.33333333%;
  }
  .arco-col-xxxl-offset-9 {
    margin-left: 37.5%;
  }
  .arco-col-xxxl-offset-9.arco-col-rtl {
    margin-left: 0;
    margin-right: 37.5%;
  }
  .arco-col-xxxl-offset-10 {
    margin-left: 41.66666667%;
  }
  .arco-col-xxxl-offset-10.arco-col-rtl {
    margin-left: 0;
    margin-right: 41.66666667%;
  }
  .arco-col-xxxl-offset-11 {
    margin-left: 45.83333333%;
  }
  .arco-col-xxxl-offset-11.arco-col-rtl {
    margin-left: 0;
    margin-right: 45.83333333%;
  }
  .arco-col-xxxl-offset-12 {
    margin-left: 50%;
  }
  .arco-col-xxxl-offset-12.arco-col-rtl {
    margin-left: 0;
    margin-right: 50%;
  }
  .arco-col-xxxl-offset-13 {
    margin-left: 54.16666667%;
  }
  .arco-col-xxxl-offset-13.arco-col-rtl {
    margin-left: 0;
    margin-right: 54.16666667%;
  }
  .arco-col-xxxl-offset-14 {
    margin-left: 58.33333333%;
  }
  .arco-col-xxxl-offset-14.arco-col-rtl {
    margin-left: 0;
    margin-right: 58.33333333%;
  }
  .arco-col-xxxl-offset-15 {
    margin-left: 62.5%;
  }
  .arco-col-xxxl-offset-15.arco-col-rtl {
    margin-left: 0;
    margin-right: 62.5%;
  }
  .arco-col-xxxl-offset-16 {
    margin-left: 66.66666667%;
  }
  .arco-col-xxxl-offset-16.arco-col-rtl {
    margin-left: 0;
    margin-right: 66.66666667%;
  }
  .arco-col-xxxl-offset-17 {
    margin-left: 70.83333333%;
  }
  .arco-col-xxxl-offset-17.arco-col-rtl {
    margin-left: 0;
    margin-right: 70.83333333%;
  }
  .arco-col-xxxl-offset-18 {
    margin-left: 75%;
  }
  .arco-col-xxxl-offset-18.arco-col-rtl {
    margin-left: 0;
    margin-right: 75%;
  }
  .arco-col-xxxl-offset-19 {
    margin-left: 79.16666667%;
  }
  .arco-col-xxxl-offset-19.arco-col-rtl {
    margin-left: 0;
    margin-right: 79.16666667%;
  }
  .arco-col-xxxl-offset-20 {
    margin-left: 83.33333333%;
  }
  .arco-col-xxxl-offset-20.arco-col-rtl {
    margin-left: 0;
    margin-right: 83.33333333%;
  }
  .arco-col-xxxl-offset-21 {
    margin-left: 87.5%;
  }
  .arco-col-xxxl-offset-21.arco-col-rtl {
    margin-left: 0;
    margin-right: 87.5%;
  }
  .arco-col-xxxl-offset-22 {
    margin-left: 91.66666667%;
  }
  .arco-col-xxxl-offset-22.arco-col-rtl {
    margin-left: 0;
    margin-right: 91.66666667%;
  }
  .arco-col-xxxl-offset-23 {
    margin-left: 95.83333333%;
  }
  .arco-col-xxxl-offset-23.arco-col-rtl {
    margin-left: 0;
    margin-right: 95.83333333%;
  }
  .arco-col-xxxl-order-0 {
    order: 0;
  }
  .arco-col-xxxl-order-1 {
    order: 1;
  }
  .arco-col-xxxl-order-2 {
    order: 2;
  }
  .arco-col-xxxl-order-3 {
    order: 3;
  }
  .arco-col-xxxl-order-4 {
    order: 4;
  }
  .arco-col-xxxl-order-5 {
    order: 5;
  }
  .arco-col-xxxl-order-6 {
    order: 6;
  }
  .arco-col-xxxl-order-7 {
    order: 7;
  }
  .arco-col-xxxl-order-8 {
    order: 8;
  }
  .arco-col-xxxl-order-9 {
    order: 9;
  }
  .arco-col-xxxl-order-10 {
    order: 10;
  }
  .arco-col-xxxl-order-11 {
    order: 11;
  }
  .arco-col-xxxl-order-12 {
    order: 12;
  }
  .arco-col-xxxl-order-13 {
    order: 13;
  }
  .arco-col-xxxl-order-14 {
    order: 14;
  }
  .arco-col-xxxl-order-15 {
    order: 15;
  }
  .arco-col-xxxl-order-16 {
    order: 16;
  }
  .arco-col-xxxl-order-17 {
    order: 17;
  }
  .arco-col-xxxl-order-18 {
    order: 18;
  }
  .arco-col-xxxl-order-19 {
    order: 19;
  }
  .arco-col-xxxl-order-20 {
    order: 20;
  }
  .arco-col-xxxl-order-21 {
    order: 21;
  }
  .arco-col-xxxl-order-22 {
    order: 22;
  }
  .arco-col-xxxl-order-23 {
    order: 23;
  }
  .arco-col-xxxl-order-24 {
    order: 24;
  }
  .arco-col-xxxl-pull-0 {
    right: 0%;
  }
  .arco-col-xxxl-pull-0.arco-col-rtl {
    right: unset;
    left: 0%;
  }
  .arco-col-xxxl-pull-1 {
    right: 4.16666667%;
  }
  .arco-col-xxxl-pull-1.arco-col-rtl {
    right: unset;
    left: 4.16666667%;
  }
  .arco-col-xxxl-pull-2 {
    right: 8.33333333%;
  }
  .arco-col-xxxl-pull-2.arco-col-rtl {
    right: unset;
    left: 8.33333333%;
  }
  .arco-col-xxxl-pull-3 {
    right: 12.5%;
  }
  .arco-col-xxxl-pull-3.arco-col-rtl {
    right: unset;
    left: 12.5%;
  }
  .arco-col-xxxl-pull-4 {
    right: 16.66666667%;
  }
  .arco-col-xxxl-pull-4.arco-col-rtl {
    right: unset;
    left: 16.66666667%;
  }
  .arco-col-xxxl-pull-5 {
    right: 20.83333333%;
  }
  .arco-col-xxxl-pull-5.arco-col-rtl {
    right: unset;
    left: 20.83333333%;
  }
  .arco-col-xxxl-pull-6 {
    right: 25%;
  }
  .arco-col-xxxl-pull-6.arco-col-rtl {
    right: unset;
    left: 25%;
  }
  .arco-col-xxxl-pull-7 {
    right: 29.16666667%;
  }
  .arco-col-xxxl-pull-7.arco-col-rtl {
    right: unset;
    left: 29.16666667%;
  }
  .arco-col-xxxl-pull-8 {
    right: 33.33333333%;
  }
  .arco-col-xxxl-pull-8.arco-col-rtl {
    right: unset;
    left: 33.33333333%;
  }
  .arco-col-xxxl-pull-9 {
    right: 37.5%;
  }
  .arco-col-xxxl-pull-9.arco-col-rtl {
    right: unset;
    left: 37.5%;
  }
  .arco-col-xxxl-pull-10 {
    right: 41.66666667%;
  }
  .arco-col-xxxl-pull-10.arco-col-rtl {
    right: unset;
    left: 41.66666667%;
  }
  .arco-col-xxxl-pull-11 {
    right: 45.83333333%;
  }
  .arco-col-xxxl-pull-11.arco-col-rtl {
    right: unset;
    left: 45.83333333%;
  }
  .arco-col-xxxl-pull-12 {
    right: 50%;
  }
  .arco-col-xxxl-pull-12.arco-col-rtl {
    right: unset;
    left: 50%;
  }
  .arco-col-xxxl-pull-13 {
    right: 54.16666667%;
  }
  .arco-col-xxxl-pull-13.arco-col-rtl {
    right: unset;
    left: 54.16666667%;
  }
  .arco-col-xxxl-pull-14 {
    right: 58.33333333%;
  }
  .arco-col-xxxl-pull-14.arco-col-rtl {
    right: unset;
    left: 58.33333333%;
  }
  .arco-col-xxxl-pull-15 {
    right: 62.5%;
  }
  .arco-col-xxxl-pull-15.arco-col-rtl {
    right: unset;
    left: 62.5%;
  }
  .arco-col-xxxl-pull-16 {
    right: 66.66666667%;
  }
  .arco-col-xxxl-pull-16.arco-col-rtl {
    right: unset;
    left: 66.66666667%;
  }
  .arco-col-xxxl-pull-17 {
    right: 70.83333333%;
  }
  .arco-col-xxxl-pull-17.arco-col-rtl {
    right: unset;
    left: 70.83333333%;
  }
  .arco-col-xxxl-pull-18 {
    right: 75%;
  }
  .arco-col-xxxl-pull-18.arco-col-rtl {
    right: unset;
    left: 75%;
  }
  .arco-col-xxxl-pull-19 {
    right: 79.16666667%;
  }
  .arco-col-xxxl-pull-19.arco-col-rtl {
    right: unset;
    left: 79.16666667%;
  }
  .arco-col-xxxl-pull-20 {
    right: 83.33333333%;
  }
  .arco-col-xxxl-pull-20.arco-col-rtl {
    right: unset;
    left: 83.33333333%;
  }
  .arco-col-xxxl-pull-21 {
    right: 87.5%;
  }
  .arco-col-xxxl-pull-21.arco-col-rtl {
    right: unset;
    left: 87.5%;
  }
  .arco-col-xxxl-pull-22 {
    right: 91.66666667%;
  }
  .arco-col-xxxl-pull-22.arco-col-rtl {
    right: unset;
    left: 91.66666667%;
  }
  .arco-col-xxxl-pull-23 {
    right: 95.83333333%;
  }
  .arco-col-xxxl-pull-23.arco-col-rtl {
    right: unset;
    left: 95.83333333%;
  }
  .arco-col-xxxl-pull-24 {
    right: 100%;
  }
  .arco-col-xxxl-pull-24.arco-col-rtl {
    right: unset;
    left: 100%;
  }
  .arco-col-xxxl-push-0 {
    left: 0%;
  }
  .arco-col-xxxl-push-0.arco-col-rtl {
    left: unset;
    right: 0%;
  }
  .arco-col-xxxl-push-1 {
    left: 4.16666667%;
  }
  .arco-col-xxxl-push-1.arco-col-rtl {
    left: unset;
    right: 4.16666667%;
  }
  .arco-col-xxxl-push-2 {
    left: 8.33333333%;
  }
  .arco-col-xxxl-push-2.arco-col-rtl {
    left: unset;
    right: 8.33333333%;
  }
  .arco-col-xxxl-push-3 {
    left: 12.5%;
  }
  .arco-col-xxxl-push-3.arco-col-rtl {
    left: unset;
    right: 12.5%;
  }
  .arco-col-xxxl-push-4 {
    left: 16.66666667%;
  }
  .arco-col-xxxl-push-4.arco-col-rtl {
    left: unset;
    right: 16.66666667%;
  }
  .arco-col-xxxl-push-5 {
    left: 20.83333333%;
  }
  .arco-col-xxxl-push-5.arco-col-rtl {
    left: unset;
    right: 20.83333333%;
  }
  .arco-col-xxxl-push-6 {
    left: 25%;
  }
  .arco-col-xxxl-push-6.arco-col-rtl {
    left: unset;
    right: 25%;
  }
  .arco-col-xxxl-push-7 {
    left: 29.16666667%;
  }
  .arco-col-xxxl-push-7.arco-col-rtl {
    left: unset;
    right: 29.16666667%;
  }
  .arco-col-xxxl-push-8 {
    left: 33.33333333%;
  }
  .arco-col-xxxl-push-8.arco-col-rtl {
    left: unset;
    right: 33.33333333%;
  }
  .arco-col-xxxl-push-9 {
    left: 37.5%;
  }
  .arco-col-xxxl-push-9.arco-col-rtl {
    left: unset;
    right: 37.5%;
  }
  .arco-col-xxxl-push-10 {
    left: 41.66666667%;
  }
  .arco-col-xxxl-push-10.arco-col-rtl {
    left: unset;
    right: 41.66666667%;
  }
  .arco-col-xxxl-push-11 {
    left: 45.83333333%;
  }
  .arco-col-xxxl-push-11.arco-col-rtl {
    left: unset;
    right: 45.83333333%;
  }
  .arco-col-xxxl-push-12 {
    left: 50%;
  }
  .arco-col-xxxl-push-12.arco-col-rtl {
    left: unset;
    right: 50%;
  }
  .arco-col-xxxl-push-13 {
    left: 54.16666667%;
  }
  .arco-col-xxxl-push-13.arco-col-rtl {
    left: unset;
    right: 54.16666667%;
  }
  .arco-col-xxxl-push-14 {
    left: 58.33333333%;
  }
  .arco-col-xxxl-push-14.arco-col-rtl {
    left: unset;
    right: 58.33333333%;
  }
  .arco-col-xxxl-push-15 {
    left: 62.5%;
  }
  .arco-col-xxxl-push-15.arco-col-rtl {
    left: unset;
    right: 62.5%;
  }
  .arco-col-xxxl-push-16 {
    left: 66.66666667%;
  }
  .arco-col-xxxl-push-16.arco-col-rtl {
    left: unset;
    right: 66.66666667%;
  }
  .arco-col-xxxl-push-17 {
    left: 70.83333333%;
  }
  .arco-col-xxxl-push-17.arco-col-rtl {
    left: unset;
    right: 70.83333333%;
  }
  .arco-col-xxxl-push-18 {
    left: 75%;
  }
  .arco-col-xxxl-push-18.arco-col-rtl {
    left: unset;
    right: 75%;
  }
  .arco-col-xxxl-push-19 {
    left: 79.16666667%;
  }
  .arco-col-xxxl-push-19.arco-col-rtl {
    left: unset;
    right: 79.16666667%;
  }
  .arco-col-xxxl-push-20 {
    left: 83.33333333%;
  }
  .arco-col-xxxl-push-20.arco-col-rtl {
    left: unset;
    right: 83.33333333%;
  }
  .arco-col-xxxl-push-21 {
    left: 87.5%;
  }
  .arco-col-xxxl-push-21.arco-col-rtl {
    left: unset;
    right: 87.5%;
  }
  .arco-col-xxxl-push-22 {
    left: 91.66666667%;
  }
  .arco-col-xxxl-push-22.arco-col-rtl {
    left: unset;
    right: 91.66666667%;
  }
  .arco-col-xxxl-push-23 {
    left: 95.83333333%;
  }
  .arco-col-xxxl-push-23.arco-col-rtl {
    left: unset;
    right: 95.83333333%;
  }
  .arco-col-xxxl-push-24 {
    left: 100%;
  }
  .arco-col-xxxl-push-24.arco-col-rtl {
    left: unset;
    right: 100%;
  }
}
.arco-grid {
  display: grid;
}
.arco-grid-rtl {
  direction: rtl;
}
.arco-list {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100%;
  border-radius: 2px;
  font-size: 14px;
  line-height: 1.5715;
  color: var(--color-text-1);
  overflow-y: auto;
  border: 1px solid var(--color-neutral-3);
}
.arco-list-wrapper:after {
  content: '';
  display: block;
  visibility: hidden;
  clear: both;
}
.arco-list-small > .arco-list-header {
  padding: 8px 20px;
}
.arco-list-small > .arco-list-footer,
.arco-list-small > .arco-list-content > .arco-list-item,
.arco-list-small > .arco-list-content .arco-list-row-col > .arco-list-item,
.arco-list-small > .arco-list-content.arco-list-virtual .arco-list-item {
  padding: 9px 20px;
}
.arco-list-default > .arco-list-header {
  padding: 12px 20px;
}
.arco-list-default > .arco-list-footer,
.arco-list-default > .arco-list-content > .arco-list-item,
.arco-list-default > .arco-list-content .arco-list-row-col > .arco-list-item,
.arco-list-default > .arco-list-content.arco-list-virtual .arco-list-item {
  padding: 13px 20px;
}
.arco-list-large > .arco-list-header {
  padding: 16px 20px;
}
.arco-list-large > .arco-list-footer,
.arco-list-large > .arco-list-content > .arco-list-item,
.arco-list-large > .arco-list-content .arco-list-row-col > .arco-list-item,
.arco-list-large > .arco-list-content.arco-list-virtual .arco-list-item {
  padding: 17px 20px;
}
.arco-list-header,
.arco-list-item:not(:last-child) {
  border-bottom: 1px solid var(--color-neutral-3);
}
.arco-list-footer {
  border-top: 1px solid var(--color-neutral-3);
}
.arco-list-no-border,
.arco-list-no-split .arco-list-header,
.arco-list-no-split .arco-list-footer,
.arco-list-no-split .arco-list-item {
  border: none;
}
.arco-list-header {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.5;
  color: var(--color-text-1);
}
.arco-list-item {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
}
.arco-list-item-main {
  flex: 1;
  overflow: hidden;
}
.arco-list-item-main .arco-list-item-action:not(:first-child) {
  margin-top: 4px;
}
.arco-list-item-meta {
  display: flex;
  align-items: center;
  padding: 4px 0;
}
.arco-list-item-meta-avatar {
  display: flex;
}
.arco-list-item-meta-avatar:not(:last-child) {
  margin-right: 16px;
}
.arco-list-item-meta-title {
  font-weight: 500;
  color: var(--color-text-1);
}
.arco-list-item-meta-title:not(:last-child) {
  margin-bottom: 2px;
}
.arco-list-item-meta-description {
  color: var(--color-text-2);
}
.arco-list-item-action {
  display: flex;
  flex-wrap: nowrap;
  align-self: center;
  list-style: none;
}
.arco-list-item-action > li {
  display: inline-block;
  cursor: pointer;
}
.arco-list-item-action > li:not(:last-child) {
  margin-right: 20px;
}
.arco-list-hoverable .arco-list-item:hover {
  background-color: var(--color-fill-1);
}
.arco-list-pagination {
  float: right;
  margin-top: 24px;
}
.arco-list-pagination:after {
  display: block;
  clear: both;
  height: 0;
  content: '';
  visibility: hidden;
  overflow: hidden;
}
.arco-list-scroll-loading {
  display: flex;
  align-items: center;
  justify-content: center;
}
.arco-list-content {
  flex: 1;
}
.arco-list-content .arco-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.arco-list-rtl {
  direction: rtl;
}
.arco-list-rtl .arco-list-item-meta-avatar:not(:last-child) {
  margin-right: 0;
  margin-left: 16px;
}
.arco-list-rtl .arco-list-item-action > li:not(:last-child) {
  margin-left: 20px;
  margin-right: 0;
}
.arco-list-wrapper-rtl .arco-list-pagination {
  float: left;
}
._message-box_18ki9_1 {
  width: 400px;
  max-height: 800px;
  background-color: var(--color-bg-popup);
  border: 1px solid var(--color-border-2);
  box-shadow: 0 4px 10px #0000001a;
  border-radius: 4px;
}
._message-box_18ki9_1 .arco-tabs-header-nav {
  padding: 8px 16px;
  border-bottom: 1px solid var(--color-border-2);
}
._message-box_18ki9_1 .arco-list-item-meta {
  align-items: flex-start;
}
._message-box_18ki9_1 .arco-list-item-meta-content {
  width: 100%;
}
._message-box_18ki9_1 .arco-tabs-content {
  padding-top: 0;
}
._message-title_18ki9_22 {
  display: flex;
  justify-content: space-between;
}
._footer_18ki9_26 {
  display: flex;
}
._footer-item_18ki9_29 {
  display: flex;
  justify-content: center;
  width: 50%;
}
._footer-item_18ki9_29:first-child {
  border-right: 1px solid var(--color-border-2);
}
._icon-button_13yaw_1 {
  font-size: 16px;
  border: 1px solid var(--color-border-2);
}
._icon-button_13yaw_1 > svg {
  vertical-align: -3px;
}
.arco-alert {
  box-sizing: border-box;
  border-radius: var(--border-radius-small);
  padding: 8px 15px;
  font-size: 14px;
  overflow: hidden;
  display: flex;
  width: 100%;
  text-align: left;
  align-items: center;
  line-height: 1.5715;
}
.arco-alert-with-title {
  padding: 15px;
}
.arco-alert-with-title {
  align-items: flex-start;
}
.arco-alert-info {
  border: 1px solid transparent;
  background-color: var(--color-primary-light-1);
}
.arco-alert-success {
  border: 1px solid transparent;
  background-color: var(--color-success-light-1);
}
.arco-alert-warning {
  border: 1px solid transparent;
  background-color: var(--color-warning-light-1);
}
.arco-alert-error {
  border: 1px solid transparent;
  background-color: var(--color-danger-light-1);
}
.arco-alert-banner {
  border: none;
  border-radius: 0;
}
.arco-alert-content-wrapper {
  position: relative;
  flex: 1;
}
.arco-alert-title {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.5;
  margin-bottom: 4px;
}
.arco-alert-info .arco-alert-title,
.arco-alert-info .arco-alert-content {
  color: var(--color-text-1);
}
.arco-alert-info.arco-alert-with-title .arco-alert-content {
  color: var(--color-text-2);
}
.arco-alert-success .arco-alert-title,
.arco-alert-success .arco-alert-content {
  color: var(--color-text-1);
}
.arco-alert-success.arco-alert-with-title .arco-alert-content {
  color: var(--color-text-2);
}
.arco-alert-warning .arco-alert-title,
.arco-alert-warning .arco-alert-content {
  color: var(--color-text-1);
}
.arco-alert-warning.arco-alert-with-title .arco-alert-content {
  color: var(--color-text-2);
}
.arco-alert-error .arco-alert-title,
.arco-alert-error .arco-alert-content {
  color: var(--color-text-1);
}
.arco-alert-error.arco-alert-with-title .arco-alert-content {
  color: var(--color-text-2);
}
.arco-alert-icon-wrapper {
  margin-right: 8px;
  height: 22.001px;
  display: flex;
  align-items: center;
}
.arco-alert-icon-wrapper svg {
  font-size: 16px;
}
.arco-alert-with-title .arco-alert-icon-wrapper {
  height: 24px;
}
.arco-alert-with-title .arco-alert-icon-wrapper svg {
  font-size: 18px;
}
.arco-alert-info .arco-alert-icon-wrapper svg {
  color: rgb(var(--primary-6));
}
.arco-alert-success .arco-alert-icon-wrapper svg {
  color: rgb(var(--success-6));
}
.arco-alert-warning .arco-alert-icon-wrapper svg {
  color: rgb(var(--warning-6));
}
.arco-alert-error .arco-alert-icon-wrapper svg {
  color: rgb(var(--danger-6));
}
.arco-alert-close-btn {
  box-sizing: border-box;
  padding: 0;
  border: none;
  outline: none;
  font-size: 12px;
  color: var(--color-text-2);
  background-color: transparent;
  cursor: pointer;
  transition: color 0.1s cubic-bezier(0, 0, 1, 1);
  margin-left: 8px;
  top: 4px;
  right: 0;
}
.arco-alert-close-btn:hover {
  color: var(--color-text-1);
}
.arco-alert-action + .arco-alert-close-btn {
  margin-left: 8px;
}
.arco-alert-action {
  margin-left: 8px;
}
.arco-alert-with-title .arco-alert-close-btn {
  margin-top: 0;
  margin-right: 0;
}
.arco-alert-rtl {
  direction: rtl;
  text-align: right;
}
.arco-alert-rtl .arco-alert-with-title {
  align-items: flex-end;
}
.arco-alert-rtl .arco-alert-icon-wrapper {
  margin-right: 0;
  margin-left: 8px;
}
.arco-alert-rtl .arco-alert-close-btn {
  right: initial;
  left: 0;
}
.arco-alert-rtl .arco-alert-action {
  margin-left: 0;
  margin-right: 8px;
}
.arco-alert-rtl .arco-alert-action + .arco-alert-close-btn {
  margin-left: 0;
  margin-right: 8px;
}
.arco-drawer-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-mask-bg);
}
.arco-drawer-no-mask {
  pointer-events: none;
}
.arco-drawer-no-mask .arco-drawer {
  pointer-events: auto;
}
.arco-drawer-wrapper {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1001;
}
.arco-drawer-wrapper-hide {
  display: none;
}
.arco-drawer {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: var(--color-bg-3);
  line-height: 1.5715;
}
.arco-drawer-wrapper {
  position: relative;
  height: 100%;
}
.arco-drawer-fixed {
  position: fixed;
  top: 0;
  z-index: 1001;
}
.arco-drawer-inner {
  height: 100%;
  overflow: hidden;
}
.arco-drawer-scroll {
  overflow: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.arco-drawer-header {
  height: 48px;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  padding: 0 16px;
  border-bottom: 1px solid var(--color-neutral-3);
  flex-shrink: 0;
}
.arco-drawer-header-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-1);
  text-align: left;
}
.arco-drawer-footer {
  box-sizing: border-box;
  padding: 16px;
  border-top: 1px solid var(--color-neutral-3);
  text-align: right;
  flex-shrink: 0;
}
.arco-drawer-footer > .arco-btn {
  margin-left: 12px;
}
.arco-drawer .arco-drawer-close-icon {
  position: absolute;
  right: 20px;
  top: 18px;
  cursor: pointer;
  z-index: 1;
  font-size: 12px;
  color: var(--color-text-1);
}
.arco-drawer-content {
  flex: 1;
  height: 100%;
  padding: 12px 16px;
  color: var(--color-text-1);
  box-sizing: border-box;
  position: relative;
  overflow: auto;
}
.arco-drawer-rtl {
  direction: rtl;
}
.arco-drawer-rtl .arco-drawer-close-icon {
  right: initial;
  left: 20px;
}
.arco-drawer-rtl .arco-drawer-footer {
  text-align: left;
}
.arco-input-number {
  position: relative;
  display: inline-block;
  width: 100%;
  box-sizing: border-box;
  border-radius: var(--border-radius-small);
}
.arco-input-number-step-button {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border-color: var(--color-neutral-3);
  background-color: var(--color-fill-2);
  color: var(--color-text-2);
  cursor: pointer;
  user-select: none;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-input-number-step-button:hover {
  border-color: var(--color-fill-3);
  background-color: var(--color-fill-3);
}
.arco-input-number-step-button:active {
  border-color: var(--color-fill-4);
  background-color: var(--color-fill-4);
}
.arco-input-number-step-button-disabled {
  cursor: not-allowed;
  background-color: var(--color-fill-2);
  color: var(--color-text-4);
}
.arco-input-number-step-button-disabled:hover,
.arco-input-number-step-button-disabled:active {
  border-color: var(--color-neutral-3);
  background-color: var(--color-fill-2);
}
.arco-input-number-prefix,
.arco-input-number-suffix {
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-input-number-mode-embed .arco-input-number-step-layer {
  opacity: 0;
  position: absolute;
  right: 4px;
  top: 4px;
  bottom: 4px;
  width: 18px;
  border-radius: 1px;
  overflow: hidden;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-input-number-mode-embed
  .arco-input-number-step-layer
  .arco-input-number-step-button {
  width: 100%;
  height: 50%;
  font-size: 10px;
}
.arco-input-number-mode-embed:not(.arco-input-group-wrapper-disabled):hover
  .arco-input-number-step-layer {
  opacity: 1;
}
.arco-input-number-mode-embed:not(.arco-input-group-wrapper-disabled):hover
  .arco-input-number-step-layer
  ~ .arco-input-number-suffix {
  opacity: 0;
  pointer-events: none;
}
.arco-input-number-mode-embed
  .arco-input-inner-wrapper:not(.arco-input-inner-wrapper-focus)
  .arco-input-number-step-button:not(
    .arco-input-number-step-button-disabled
  ):hover {
  background-color: var(--color-fill-4);
}
.arco-input-number-rtl.arco-input-number-mode-embed
  .arco-input-number-step-layer {
  right: unset;
  left: 4px;
}
.arco-input-number-mode-button .arco-input {
  text-align: center;
}
.arco-input-number-mode-button .arco-input-group {
  position: relative;
}
.arco-input-number-mode-button .arco-input-group-addbefore,
.arco-input-number-mode-button .arco-input-group-addafter {
  padding: 0;
}
.arco-input-number-mode-button
  .arco-input-group
  .arco-input-number-step-button {
  position: absolute;
  top: 0;
  height: 100%;
  border: inherit;
}
.arco-input-number-mode-button
  .arco-input-group
  .arco-input-number-step-button:active {
  border-color: var(--color-fill-4);
}
.arco-input-number-mode-button
  .arco-input-group-addbefore
  .arco-input-number-step-button {
  left: 0;
  border-right-color: var(--color-neutral-3);
}
.arco-input-number-mode-button
  .arco-input-group-addafter
  .arco-input-number-step-button {
  right: 0;
  border-left-color: var(--color-neutral-3);
}
.arco-input-number-mode-button.arco-input-number-size-mini
  .arco-input-group-addbefore,
.arco-input-number-mode-button.arco-input-number-size-mini
  .arco-input-group-addafter,
.arco-input-number-mode-button.arco-input-number-size-mini
  .arco-input-group-addbefore
  .arco-input-number-step-button,
.arco-input-number-mode-button.arco-input-number-size-mini
  .arco-input-group-addafter
  .arco-input-number-step-button {
  width: 24px;
}
.arco-input-number-mode-button.arco-input-number-size-small
  .arco-input-group-addbefore,
.arco-input-number-mode-button.arco-input-number-size-small
  .arco-input-group-addafter,
.arco-input-number-mode-button.arco-input-number-size-small
  .arco-input-group-addbefore
  .arco-input-number-step-button,
.arco-input-number-mode-button.arco-input-number-size-small
  .arco-input-group-addafter
  .arco-input-number-step-button {
  width: 28px;
}
.arco-input-number-mode-button.arco-input-number-size-default
  .arco-input-group-addbefore,
.arco-input-number-mode-button.arco-input-number-size-default
  .arco-input-group-addafter,
.arco-input-number-mode-button.arco-input-number-size-default
  .arco-input-group-addbefore
  .arco-input-number-step-button,
.arco-input-number-mode-button.arco-input-number-size-default
  .arco-input-group-addafter
  .arco-input-number-step-button {
  width: 32px;
}
.arco-input-number-mode-button.arco-input-number-size-large
  .arco-input-group-addbefore,
.arco-input-number-mode-button.arco-input-number-size-large
  .arco-input-group-addafter,
.arco-input-number-mode-button.arco-input-number-size-large
  .arco-input-group-addbefore
  .arco-input-number-step-button,
.arco-input-number-mode-button.arco-input-number-size-large
  .arco-input-group-addafter
  .arco-input-number-step-button {
  width: 36px;
}
.arco-input-number-readonly .arco-input-number-step-button {
  pointer-events: none;
  color: var(--color-text-4);
}
.arco-input-number-illegal-value input {
  color: rgb(var(--danger-6));
}
.switchSlideText-enter,
.switchSlideText-appear {
  left: -100% !important;
}
.switchSlideText-enter-active,
.switchSlideText-appear-active {
  left: 8px !important;
  transition: left 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.switchSlideText-exit {
  left: 100% !important;
}
.switchSlideText-exit-active {
  left: 26px !important;
  transition: left 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-switch {
  position: relative;
  outline: none;
  height: 24px;
  line-height: 24px;
  min-width: 40px;
  background-color: var(--color-fill-4);
  border-radius: 12px;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
  padding: 0;
  box-sizing: border-box;
  vertical-align: middle;
  overflow: hidden;
}
.arco-switch-type-round:focus-visible,
.arco-switch-type-circle:focus-visible {
  box-shadow: 0 0 0 2px rgb(var(--gray-6));
}
.arco-switch-type-round.arco-switch-checked:focus-visible,
.arco-switch-type-circle.arco-switch-checked:focus-visible {
  box-shadow: 0 0 0 2px var(--color-primary-light-3);
}
.arco-switch-type-line:focus-visible .arco-switch-dot {
  box-shadow: 0 0 0 2px rgb(var(--gray-6));
  transition: none;
}
.arco-switch-type-line.arco-switch-checked:focus-visible .arco-switch-dot {
  box-shadow: 0 0 0 2px var(--color-primary-light-3);
}
.arco-switch-dot {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 4px;
  left: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--color-bg-white);
  color: var(--color-neutral-3);
  font-size: 12px;
  transition: all 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-switch-checked {
  background-color: rgb(var(--primary-6));
}
.arco-switch-checked .arco-switch-dot {
  color: rgb(var(--primary-6));
  left: calc(100% - 20px);
}
.arco-switch[disabled] .arco-switch-dot {
  color: var(--color-fill-2);
}
.arco-switch[disabled].arco-switch-checked .arco-switch-dot {
  color: var(--color-primary-light-3);
}
.arco-switch-text-holder {
  opacity: 0;
  font-size: 12px;
  margin: 0 8px 0 26px;
}
.arco-switch-text {
  position: absolute;
  color: var(--color-white);
  font-size: 12px;
  top: 0;
  left: 26px;
}
.arco-switch-checked .arco-switch-text-holder {
  margin: 0 26px 0 8px;
}
.arco-switch-checked .arco-switch-text {
  left: 8px;
  color: var(--color-white);
}
.arco-switch[disabled] {
  cursor: not-allowed;
  background-color: var(--color-fill-2);
}
.arco-switch[disabled] .arco-switch-text {
  color: var(--color-white);
}
.arco-switch[disabled].arco-switch-checked {
  background-color: var(--color-primary-light-3);
}
.arco-switch[disabled].arco-switch-checked .arco-switch-text {
  color: var(--color-white);
}
.arco-switch-loading {
  background-color: var(--color-fill-2);
}
.arco-switch-loading .arco-switch-dot {
  color: var(--color-neutral-3);
}
.arco-switch-loading .arco-switch-text {
  color: var(--color-white);
}
.arco-switch-loading.arco-switch-checked {
  background-color: var(--color-primary-light-3);
}
.arco-switch-loading.arco-switch-checked .arco-switch-dot {
  color: var(--color-primary-light-3);
}
.arco-switch-loading.arco-switch-checked .arco-switch-text {
  color: var(--color-primary-light-1);
}
.arco-switch-small {
  height: 16px;
  line-height: 16px;
  min-width: 28px;
}
.arco-switch-small.arco-switch-checked {
  padding-left: -2px;
}
.arco-switch-small .arco-switch-dot {
  top: 2px;
  left: 2px;
  width: 12px;
  height: 12px;
  border-radius: 8px;
}
.arco-switch-small .arco-switch-dot-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.66667);
}
.arco-switch-small.arco-switch-checked .arco-switch-dot {
  left: calc(100% - 14px);
}
.arco-switch-type-round {
  border-radius: var(--border-radius-small);
  min-width: 40px;
}
.arco-switch-type-round .arco-switch-dot {
  border-radius: 2px;
}
.arco-switch-type-round.arco-switch-small {
  border-radius: 2px;
  height: 16px;
  line-height: 16px;
  min-width: 28px;
}
.arco-switch-type-round.arco-switch-small .arco-switch-dot {
  border-radius: 1px;
}
.arco-switch-type-line {
  min-width: 36px;
  background-color: transparent;
  overflow: unset;
}
.arco-switch-type-line:after {
  content: '';
  display: block;
  width: 100%;
  border-radius: 3px;
  height: 6px;
  background-color: var(--color-fill-4);
  transition: background-color 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-switch-type-line .arco-switch-dot {
  top: 2px;
  left: 0;
  border-radius: 10px;
  width: 20px;
  height: 20px;
  background-color: var(--color-bg-white);
  box-shadow: 0 1px 3px var(--color-neutral-6);
}
.arco-switch-type-line.arco-switch-checked {
  background-color: transparent;
}
.arco-switch-type-line.arco-switch-checked:after {
  background-color: rgb(var(--primary-6));
}
.arco-switch-type-line.arco-switch-checked .arco-switch-dot {
  left: calc(100% - 20px);
}
.arco-switch-type-line[disabled] {
  cursor: not-allowed;
  background-color: transparent;
}
.arco-switch-type-line[disabled]:after {
  background-color: var(--color-fill-2);
}
.arco-switch-type-line[disabled].arco-switch-checked {
  background-color: transparent;
}
.arco-switch-type-line[disabled].arco-switch-checked:after {
  background-color: var(--color-primary-light-3);
}
.arco-switch-type-line.arco-switch-loading {
  background-color: transparent;
}
.arco-switch-type-line.arco-switch-loading:after {
  background-color: var(--color-fill-2);
}
.arco-switch-type-line.arco-switch-loading.arco-switch-checked {
  background-color: transparent;
}
.arco-switch-type-line.arco-switch-loading.arco-switch-checked:after {
  background-color: var(--color-primary-light-3);
}
.arco-switch-type-line.arco-switch-small {
  height: 16px;
  line-height: 16px;
  min-width: 28px;
}
.arco-switch-type-line.arco-switch-small.arco-switch-checked {
  padding-left: 0;
}
.arco-switch-type-line.arco-switch-small .arco-switch-dot {
  top: 0px;
  width: 16px;
  height: 16px;
  border-radius: 8px;
}
.arco-switch-type-line.arco-switch-small .arco-switch-dot-icon {
  transform: translate(-50%, -50%) scale(1);
}
.arco-switch-type-line.arco-switch-small.arco-switch-checked .arco-switch-dot {
  left: calc(100% - 16px);
}
.arco-switch-rtl {
  direction: rtl;
}
.arco-switch-rtl .switchSlideText-enter,
.arco-switch-rtl .switchSlideText-appear {
  right: -100% !important;
  left: initial;
}
.arco-switch-rtl .switchSlideText-enter-active,
.arco-switch-rtl .switchSlideText-appear-active {
  left: initial;
  right: 8px !important;
  transition: right 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-switch-rtl .switchSlideText-exit {
  right: 100% !important;
  left: initial;
}
.arco-switch-rtl .switchSlideText-exit-active {
  left: initial;
  right: 26px !important;
  transition: right 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}
.arco-switch-rtl .arco-switch-dot {
  left: initial;
  right: 4px;
}
.arco-switch-rtl .arco-switch-text-holder {
  margin: 0 26px 0 8px;
}
.arco-switch-rtl .arco-switch-text {
  left: initial;
  right: 26px;
}
.arco-switch-rtl.arco-switch-small .arco-switch-dot {
  right: 2px;
}
.arco-switch-rtl.arco-switch-type-line .arco-switch-dot {
  right: 0;
}
.arco-switch-rtl.arco-switch-checked .arco-switch-dot {
  right: calc(100% - 20px);
}
.arco-switch-rtl.arco-switch-checked .arco-switch-text-holder {
  margin: 0 8px 0 26px;
}
.arco-switch-rtl.arco-switch-checked .arco-switch-text {
  right: 8px;
  left: initial;
}
.arco-switch-rtl.arco-switch-checked.arco-switch-small {
  padding-right: -2px;
  padding-left: 0;
}
.arco-switch-rtl.arco-switch-checked.arco-switch-small.arco-switch-type-line {
  padding-right: 0;
  padding-left: 0;
}
.arco-switch-rtl.arco-switch-checked.arco-switch-small .arco-switch-dot {
  left: initial;
  right: calc(100% - 14px);
}
._block_4se2h_1 {
  margin-bottom: 24px;
}
._title_4se2h_4 {
  font-size: 14px;
  padding: 0;
  margin: 10px 0;
}
._switch-wrapper_4se2h_9 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
}
._input_va63a_1 {
  display: flex;
  width: 100%;
  height: 32px;
  border: 1px solid var(--color-border);
  padding: 3px;
  box-sizing: border-box;
}
._color_va63a_9 {
  width: 100px;
  height: 24px;
  margin-right: 10px;
}
._ul_va63a_14 {
  list-style: none;
  display: flex;
  padding: 0;
}
._li_va63a_19 {
  width: 10%;
  height: 26px;
}
._navbar_1d5yr_1 {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid var(--color-border);
  box-sizing: border-box;
  background-color: var(--color-bg-2);
  height: 100%;
}
._left_1d5yr_9 {
  display: flex;
  align-items: center;
}
._logo_1d5yr_13 {
  display: flex;
  align-items: center;
  width: 200px;
  padding-left: 20px;
  box-sizing: border-box;
}
._logo-name_1d5yr_20 {
  color: var(--color-text-1);
  font-weight: 500;
  font-size: 20px;
  margin-left: 10px;
  font-family: PingFang SC;
}
._right_1d5yr_27 {
  display: flex;
  list-style: none;
  padding-right: 20px;
}
._right_1d5yr_27 li {
  padding: 0 8px;
  display: flex;
  align-items: center;
}
._right_1d5yr_27 a {
  text-decoration: none;
  color: var(--color-text-1);
}
._username_1d5yr_41 {
  cursor: pointer;
}
._round_1d5yr_44 .arco-input-inner-wrapper {
  border-radius: 16px;
}
._round_1d5yr_44 svg {
  font-size: 16px;
}
._dropdown-icon_1d5yr_50 {
  margin-right: 8px;
  font-size: 16px;
  vertical-align: text-bottom;
}
._fixed-settings_1d5yr_55 {
  position: fixed;
  top: 280px;
  right: 0px;
}
._fixed-settings_1d5yr_55 svg {
  font-size: 18px;
  vertical-align: -4px;
}
._footer_ovnxf_1 {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  text-align: center;
  color: var(--color-text-2);
}
._layout_mbd37_1 {
  width: 100%;
  height: 100%;
}
._layout-navbar_mbd37_5 {
  position: fixed;
  width: 100%;
  min-width: 1100px;
  top: 0;
  left: 0;
  height: 60px;
  z-index: 100;
}
._layout-navbar-hidden_mbd37_14 {
  height: 0;
}
._layout-sider_mbd37_17 {
  position: fixed;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 99;
  box-sizing: border-box;
}
._layout-sider_mbd37_17 ::-webkit-scrollbar {
  width: 12px;
  height: 4px;
}
._layout-sider_mbd37_17 ::-webkit-scrollbar-thumb {
  border: 4px solid transparent;
  background-clip: padding-box;
  border-radius: 7px;
  background-color: var(--color-text-4);
}
._layout-sider_mbd37_17 ::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-text-3);
}
._layout-sider_mbd37_17:after {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  right: -1px;
  width: 1px;
  height: 100%;
  background-color: var(--color-border);
}
._layout-sider_mbd37_17 > .arco-layout-sider-children {
  overflow-y: hidden;
}
._layout-sider_mbd37_17 ._collapse-btn_mbd37_51 {
  height: 24px;
  width: 24px;
  background-color: var(--color-fill-1);
  color: var(--color-text-3);
  border-radius: 2px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  bottom: 12px;
  right: 12px;
}
._layout-sider_mbd37_17 ._collapse-btn_mbd37_51:hover {
  background-color: var(--color-fill-3);
}
._menu-wrapper_mbd37_68 {
  overflow: auto;
  height: 100%;
}
._menu-wrapper_mbd37_68 .arco-menu-item-inner > a:after,
._menu-wrapper_mbd37_68 .arco-menu-item > a:after {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
._menu-wrapper_mbd37_68 .arco-menu-inline-header {
  font-weight: 500;
}
._icon_mbd37_87 {
  font-size: 18px;
  vertical-align: text-bottom;
}
._icon-empty_mbd37_91 {
  width: 12px;
  height: 18px;
  display: inline-block;
}
._layout-content_mbd37_96 {
  background-color: var(--color-fill-2);
  min-width: 1100px;
  min-height: 100vh;
  transition: padding-left 0.2s;
  box-sizing: border-box;
}
._layout-content-wrapper_mbd37_103 {
  padding: 16px 20px 0;
}
._layout-breadcrumb_mbd37_106 {
  margin-bottom: 16px;
}
._spin_mbd37_109 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: calc(100vh - 60px);
}
.arco-link {
  color: rgb(var(--link-6));
  font-size: 14px;
  line-height: 1.5715;
  display: inline-block;
  padding: 1px 4px;
  text-decoration: none;
  cursor: pointer;
  border-radius: var(--border-radius-small);
  background-color: transparent;
  transition: all 0.1s cubic-bezier(0, 0, 1, 1);
}
.arco-link:hover {
  color: rgb(var(--link-6));
  background-color: var(--color-fill-2);
}
.arco-link:active {
  transition: none;
  color: rgb(var(--link-6));
  background-color: var(--color-fill-3);
}
.arco-link:focus-visible {
  box-shadow: 0 0 0 2px var(--color-link-light-3);
}
.arco-link.arco-link-hoverless {
  background-color: unset;
  padding: 0;
  display: inline;
}
.arco-link.arco-link-hoverless:active,
.arco-link.arco-link-hoverless:hover {
  background-color: unset;
}
.arco-link.arco-link-disabled {
  color: var(--color-link-light-3);
  background: none;
  cursor: not-allowed;
}
.arco-link-is-success,
.arco-link-is-success:hover,
.arco-link-is-success:active {
  color: rgb(var(--success-6));
}
.arco-link-is-success.arco-link-disabled {
  color: var(--color-success-light-3);
}
.arco-link-is-error,
.arco-link-is-error:hover,
.arco-link-is-error:active {
  color: rgb(var(--danger-6));
}
.arco-link-is-error.arco-link-disabled {
  color: var(--color-danger-light-3);
}
.arco-link-is-warning,
.arco-link-is-warning:hover,
.arco-link-is-warning:active {
  color: rgb(var(--warning-6));
}
.arco-link-is-warning.arco-link-disabled {
  color: var(--color-warning-light-2);
}
.arco-link-icon {
  margin-right: 6px;
  font-size: 12px;
}
.arco-link-rtl .arco-link-icon {
  margin-left: 6px;
  margin-right: 0;
}
.arco-form {
  width: 100%;
  display: flex;
  flex-direction: column;
}
.arco-form-inline {
  flex-flow: row wrap;
}
.arco-form-inline .arco-form-item {
  width: auto;
  margin-bottom: 8px;
}
.arco-form-item {
  width: 100%;
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}
.arco-form-item.arco-form-item-hidden {
  display: none;
}
.arco-form-item > .arco-form-label-item {
  padding-right: 16px;
}
.arco-form-item.arco-form-item-error {
  margin-bottom: 0;
}
.arco-form-item-wrapper-flex.arco-col {
  flex: 1;
}
.arco-form-size-mini .arco-form-label-item {
  line-height: 24px;
  font-size: 12px;
}
.arco-form-size-mini .arco-form-label-item > label {
  font-size: 12px;
}
.arco-form-size-mini .arco-form-item-control {
  min-height: 24px;
}
.arco-form-size-small .arco-form-label-item {
  line-height: 28px;
}
.arco-form-size-small .arco-form-label-item > label {
  font-size: 14px;
}
.arco-form-size-small .arco-form-item-control {
  min-height: 28px;
}
.arco-form-size-large .arco-form-label-item {
  line-height: 36px;
}
.arco-form-size-large .arco-form-label-item > label {
  font-size: 14px;
}
.arco-form-size-large .arco-form-item-control {
  min-height: 36px;
}
.arco-form-extra {
  font-size: 12px;
  color: var(--color-text-3);
  margin-top: 4px;
}
.arco-form-message {
  font-size: 12px;
  color: rgb(var(--danger-6));
  min-height: 20px;
  line-height: 20px;
}
.arco-form-message-help {
  color: var(--color-text-3);
}
.arco-form-message + .arco-form-extra {
  margin-bottom: 4px;
  margin-top: 0;
}
.arco-form-layout-vertical {
  display: block;
}
.arco-form-layout-vertical > .arco-form-label-item {
  line-height: 1.5715;
  white-space: normal;
  text-align: left;
  padding: 0;
  margin-bottom: 8px;
}
.arco-form-layout-inline {
  margin-right: 24px;
}
.arco-form-label-item {
  text-align: right;
  white-space: nowrap;
  line-height: 32px;
}
.arco-form-label-item-flex.arco-col {
  flex: 0;
}
.arco-form-label-item-flex.arco-col > label {
  white-space: nowrap;
}
.arco-form-label-item > label {
  font-size: 14px;
  white-space: normal;
  color: var(--color-text-2);
}
.arco-form-label-item .arco-form-item-tooltip {
  margin-left: 4px;
  color: var(--color-text-4);
}
.arco-form-label-item .arco-form-item-symbol {
  color: rgb(var(--danger-6));
  font-size: 12px;
  line-height: 1;
}
.arco-form-label-item .arco-form-item-symbol svg {
  transform: scale(0.5);
}
.arco-form-label-item-left {
  text-align: left;
}
.arco-form-item-control {
  display: flex;
  align-items: center;
  min-height: 32px;
  width: 100%;
}
.arco-form-item-control-children {
  width: 100%;
  flex: 1;
}
.arco-form-item-control-wrapper {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: flex-start;
}
.arco-form .arco-slider {
  display: block;
}
.arco-form-item-status-validating .arco-input:not(.arco-input-disabled),
.arco-form-item-status-validating
  .arco-input-inner-wrapper:not(.arco-input-inner-wrapper-disabled),
.arco-form-item-status-validating .arco-textarea:not(.arco-textarea-disabled) {
  border-color: transparent;
  background-color: var(--color-fill-2);
}
.arco-form-item-status-validating .arco-input:not(.arco-input-disabled):hover,
.arco-form-item-status-validating
  .arco-input-inner-wrapper:not(.arco-input-inner-wrapper-disabled):hover,
.arco-form-item-status-validating
  .arco-textarea:not(.arco-textarea-disabled):hover {
  border-color: transparent;
  background-color: var(--color-fill-3);
}
.arco-form-item-status-validating
  .arco-input-inner-wrapper.arco-input-inner-wrapper-focus,
.arco-form-item-status-validating .arco-input-inner-wrapper.arco-textarea-focus,
.arco-form-item-status-validating .arco-textarea.arco-input-inner-wrapper-focus,
.arco-form-item-status-validating .arco-textarea.arco-textarea-focus,
.arco-form-item-status-validating
  .arco-input-inner-wrapper.arco-input-inner-wrapper-focus:hover,
.arco-form-item-status-validating
  .arco-input-inner-wrapper.arco-textarea-focus:hover,
.arco-form-item-status-validating
  .arco-textarea.arco-input-inner-wrapper-focus:hover,
.arco-form-item-status-validating .arco-textarea.arco-textarea-focus:hover {
  border-color: rgb(var(--primary-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-form-item-status-validating .arco-input:focus,
.arco-form-item-status-validating .arco-input:focus:hover {
  border-color: rgb(var(--primary-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-form-item-status-validating .arco-input-inner-wrapper .arco-input,
.arco-form-item-status-validating .arco-input-inner-wrapper .arco-input:hover {
  background: none;
  box-shadow: none;
}
.arco-form-item-status-validating
  .arco-select:not(.arco-select-disabled)
  .arco-select-view {
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-form-item-status-validating
  .arco-select:not(.arco-select-disabled):hover
  .arco-select-view {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-form-item-status-validating
  .arco-select:not(.arco-select-disabled).arco-select-focused
  .arco-select-view {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--primary-6));
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-form-item-status-validating
  .arco-cascader:not(.arco-cascader-disabled)
  .arco-cascader-view {
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-form-item-status-validating
  .arco-cascader:not(.arco-cascader-disabled):hover
  .arco-cascader-view {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-form-item-status-validating
  .arco-cascader:not(.arco-cascader-disabled).arco-cascader-focused
  .arco-cascader-view {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--primary-6));
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-form-item-status-validating
  .arco-tree-select:not(.arco-tree-select-disabled)
  .arco-tree-select-view {
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-form-item-status-validating
  .arco-tree-select:not(.arco-tree-select-disabled):hover
  .arco-tree-select-view {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-form-item-status-validating
  .arco-tree-select:not(.arco-tree-select-disabled).arco-tree-select-focused
  .arco-tree-select-view {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--primary-6));
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-form-item-status-validating .arco-picker:not(.arco-picker-disabled) {
  border-color: transparent;
  background-color: var(--color-fill-2);
}
.arco-form-item-status-validating
  .arco-picker:not(.arco-picker-disabled):hover {
  border-color: transparent;
  background-color: var(--color-fill-3);
}
.arco-form-item-status-validating
  .arco-picker-focused:not(.arco-picker-disabled),
.arco-form-item-status-validating
  .arco-picker-focused:not(.arco-picker-disabled):hover {
  border-color: rgb(var(--primary-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-form-item-status-validating .arco-input-tag {
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-form-item-status-validating .arco-input-tag:hover {
  border-color: transparent;
  background-color: var(--color-fill-3);
}
.arco-form-item-status-validating .arco-input-tag.arco-input-tag-focus {
  border-color: rgb(var(--primary-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-primary-light-2);
}
.arco-form-item-status-validating .arco-form-message-help {
  color: rgb(var(--primary-6));
}
.arco-form-item-status-validating
  .arco-form-message-help
  .arco-form-message-help-warning {
  color: rgb(var(--warning-6));
}
.arco-form-item-feedback-validating {
  color: rgb(var(--primary-6));
}
.arco-form-item-status-success .arco-input:not(.arco-input-disabled),
.arco-form-item-status-success
  .arco-input-inner-wrapper:not(.arco-input-inner-wrapper-disabled),
.arco-form-item-status-success .arco-textarea:not(.arco-textarea-disabled) {
  border-color: transparent;
  background-color: var(--color-fill-2);
}
.arco-form-item-status-success .arco-input:not(.arco-input-disabled):hover,
.arco-form-item-status-success
  .arco-input-inner-wrapper:not(.arco-input-inner-wrapper-disabled):hover,
.arco-form-item-status-success
  .arco-textarea:not(.arco-textarea-disabled):hover {
  border-color: transparent;
  background-color: var(--color-fill-3);
}
.arco-form-item-status-success
  .arco-input-inner-wrapper.arco-input-inner-wrapper-focus,
.arco-form-item-status-success .arco-input-inner-wrapper.arco-textarea-focus,
.arco-form-item-status-success .arco-textarea.arco-input-inner-wrapper-focus,
.arco-form-item-status-success .arco-textarea.arco-textarea-focus,
.arco-form-item-status-success
  .arco-input-inner-wrapper.arco-input-inner-wrapper-focus:hover,
.arco-form-item-status-success
  .arco-input-inner-wrapper.arco-textarea-focus:hover,
.arco-form-item-status-success
  .arco-textarea.arco-input-inner-wrapper-focus:hover,
.arco-form-item-status-success .arco-textarea.arco-textarea-focus:hover {
  border-color: rgb(var(--success-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-success-light-2);
}
.arco-form-item-status-success .arco-input:focus,
.arco-form-item-status-success .arco-input:focus:hover {
  border-color: rgb(var(--success-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-success-light-2);
}
.arco-form-item-status-success .arco-input-inner-wrapper .arco-input,
.arco-form-item-status-success .arco-input-inner-wrapper .arco-input:hover {
  background: none;
  box-shadow: none;
}
.arco-form-item-status-success
  .arco-select:not(.arco-select-disabled)
  .arco-select-view {
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-form-item-status-success
  .arco-select:not(.arco-select-disabled):hover
  .arco-select-view {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-form-item-status-success
  .arco-select:not(.arco-select-disabled).arco-select-focused
  .arco-select-view {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--success-6));
  box-shadow: 0 0 0 0 var(--color-success-light-2);
}
.arco-form-item-status-success
  .arco-cascader:not(.arco-cascader-disabled)
  .arco-cascader-view {
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-form-item-status-success
  .arco-cascader:not(.arco-cascader-disabled):hover
  .arco-cascader-view {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-form-item-status-success
  .arco-cascader:not(.arco-cascader-disabled).arco-cascader-focused
  .arco-cascader-view {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--success-6));
  box-shadow: 0 0 0 0 var(--color-success-light-2);
}
.arco-form-item-status-success
  .arco-tree-select:not(.arco-tree-select-disabled)
  .arco-tree-select-view {
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-form-item-status-success
  .arco-tree-select:not(.arco-tree-select-disabled):hover
  .arco-tree-select-view {
  background-color: var(--color-fill-3);
  border-color: transparent;
}
.arco-form-item-status-success
  .arco-tree-select:not(.arco-tree-select-disabled).arco-tree-select-focused
  .arco-tree-select-view {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--success-6));
  box-shadow: 0 0 0 0 var(--color-success-light-2);
}
.arco-form-item-status-success .arco-picker:not(.arco-picker-disabled) {
  border-color: transparent;
  background-color: var(--color-fill-2);
}
.arco-form-item-status-success .arco-picker:not(.arco-picker-disabled):hover {
  border-color: transparent;
  background-color: var(--color-fill-3);
}
.arco-form-item-status-success .arco-picker-focused:not(.arco-picker-disabled),
.arco-form-item-status-success
  .arco-picker-focused:not(.arco-picker-disabled):hover {
  border-color: rgb(var(--success-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-success-light-2);
}
.arco-form-item-status-success .arco-input-tag {
  background-color: var(--color-fill-2);
  border-color: transparent;
}
.arco-form-item-status-success .arco-input-tag:hover {
  border-color: transparent;
  background-color: var(--color-fill-3);
}
.arco-form-item-status-success .arco-input-tag.arco-input-tag-focus {
  border-color: rgb(var(--success-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-success-light-2);
}
.arco-form-item-status-success .arco-form-message-help {
  color: rgb(var(--success-6));
}
.arco-form-item-status-success
  .arco-form-message-help
  .arco-form-message-help-warning {
  color: rgb(var(--warning-6));
}
.arco-form-item-feedback-success {
  color: rgb(var(--success-6));
}
.arco-form-item-status-warning .arco-input:not(.arco-input-disabled),
.arco-form-item-status-warning
  .arco-input-inner-wrapper:not(.arco-input-inner-wrapper-disabled),
.arco-form-item-status-warning .arco-textarea:not(.arco-textarea-disabled) {
  border-color: transparent;
  background-color: var(--color-warning-light-1);
}
.arco-form-item-status-warning .arco-input:not(.arco-input-disabled):hover,
.arco-form-item-status-warning
  .arco-input-inner-wrapper:not(.arco-input-inner-wrapper-disabled):hover,
.arco-form-item-status-warning
  .arco-textarea:not(.arco-textarea-disabled):hover {
  border-color: transparent;
  background-color: var(--color-warning-light-2);
}
.arco-form-item-status-warning
  .arco-input-inner-wrapper.arco-input-inner-wrapper-focus,
.arco-form-item-status-warning .arco-input-inner-wrapper.arco-textarea-focus,
.arco-form-item-status-warning .arco-textarea.arco-input-inner-wrapper-focus,
.arco-form-item-status-warning .arco-textarea.arco-textarea-focus,
.arco-form-item-status-warning
  .arco-input-inner-wrapper.arco-input-inner-wrapper-focus:hover,
.arco-form-item-status-warning
  .arco-input-inner-wrapper.arco-textarea-focus:hover,
.arco-form-item-status-warning
  .arco-textarea.arco-input-inner-wrapper-focus:hover,
.arco-form-item-status-warning .arco-textarea.arco-textarea-focus:hover {
  border-color: rgb(var(--warning-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-form-item-status-warning .arco-input:focus,
.arco-form-item-status-warning .arco-input:focus:hover {
  border-color: rgb(var(--warning-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-form-item-status-warning .arco-input-inner-wrapper .arco-input,
.arco-form-item-status-warning .arco-input-inner-wrapper .arco-input:hover {
  background: none;
  box-shadow: none;
}
.arco-form-item-status-warning
  .arco-select:not(.arco-select-disabled)
  .arco-select-view {
  background-color: var(--color-warning-light-1);
  border-color: transparent;
}
.arco-form-item-status-warning
  .arco-select:not(.arco-select-disabled):hover
  .arco-select-view {
  background-color: var(--color-warning-light-2);
  border-color: transparent;
}
.arco-form-item-status-warning
  .arco-select:not(.arco-select-disabled).arco-select-focused
  .arco-select-view {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--warning-6));
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-form-item-status-warning
  .arco-cascader:not(.arco-cascader-disabled)
  .arco-cascader-view {
  background-color: var(--color-warning-light-1);
  border-color: transparent;
}
.arco-form-item-status-warning
  .arco-cascader:not(.arco-cascader-disabled):hover
  .arco-cascader-view {
  background-color: var(--color-warning-light-2);
  border-color: transparent;
}
.arco-form-item-status-warning
  .arco-cascader:not(.arco-cascader-disabled).arco-cascader-focused
  .arco-cascader-view {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--warning-6));
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-form-item-status-warning
  .arco-tree-select:not(.arco-tree-select-disabled)
  .arco-tree-select-view {
  background-color: var(--color-warning-light-1);
  border-color: transparent;
}
.arco-form-item-status-warning
  .arco-tree-select:not(.arco-tree-select-disabled):hover
  .arco-tree-select-view {
  background-color: var(--color-warning-light-2);
  border-color: transparent;
}
.arco-form-item-status-warning
  .arco-tree-select:not(.arco-tree-select-disabled).arco-tree-select-focused
  .arco-tree-select-view {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--warning-6));
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-form-item-status-warning .arco-picker:not(.arco-picker-disabled) {
  border-color: transparent;
  background-color: var(--color-warning-light-1);
}
.arco-form-item-status-warning .arco-picker:not(.arco-picker-disabled):hover {
  border-color: transparent;
  background-color: var(--color-warning-light-2);
}
.arco-form-item-status-warning .arco-picker-focused:not(.arco-picker-disabled),
.arco-form-item-status-warning
  .arco-picker-focused:not(.arco-picker-disabled):hover {
  border-color: rgb(var(--warning-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-form-item-status-warning .arco-input-tag {
  background-color: var(--color-warning-light-1);
  border-color: transparent;
}
.arco-form-item-status-warning .arco-input-tag:hover {
  border-color: transparent;
  background-color: var(--color-warning-light-2);
}
.arco-form-item-status-warning .arco-input-tag.arco-input-tag-focus {
  border-color: rgb(var(--warning-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-warning-light-2);
}
.arco-form-item-status-warning .arco-form-message-help,
.arco-form-item-status-warning
  .arco-form-message-help
  .arco-form-message-help-warning,
.arco-form-item-feedback-warning {
  color: rgb(var(--warning-6));
}
.arco-form-item-status-error .arco-input:not(.arco-input-disabled),
.arco-form-item-status-error
  .arco-input-inner-wrapper:not(.arco-input-inner-wrapper-disabled),
.arco-form-item-status-error .arco-textarea:not(.arco-textarea-disabled) {
  border-color: transparent;
  background-color: var(--color-danger-light-1);
}
.arco-form-item-status-error .arco-input:not(.arco-input-disabled):hover,
.arco-form-item-status-error
  .arco-input-inner-wrapper:not(.arco-input-inner-wrapper-disabled):hover,
.arco-form-item-status-error .arco-textarea:not(.arco-textarea-disabled):hover {
  border-color: transparent;
  background-color: var(--color-danger-light-2);
}
.arco-form-item-status-error
  .arco-input-inner-wrapper.arco-input-inner-wrapper-focus,
.arco-form-item-status-error .arco-input-inner-wrapper.arco-textarea-focus,
.arco-form-item-status-error .arco-textarea.arco-input-inner-wrapper-focus,
.arco-form-item-status-error .arco-textarea.arco-textarea-focus,
.arco-form-item-status-error
  .arco-input-inner-wrapper.arco-input-inner-wrapper-focus:hover,
.arco-form-item-status-error
  .arco-input-inner-wrapper.arco-textarea-focus:hover,
.arco-form-item-status-error
  .arco-textarea.arco-input-inner-wrapper-focus:hover,
.arco-form-item-status-error .arco-textarea.arco-textarea-focus:hover {
  border-color: rgb(var(--danger-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-form-item-status-error .arco-input:focus,
.arco-form-item-status-error .arco-input:focus:hover {
  border-color: rgb(var(--danger-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-form-item-status-error .arco-input-inner-wrapper .arco-input,
.arco-form-item-status-error .arco-input-inner-wrapper .arco-input:hover {
  background: none;
  box-shadow: none;
}
.arco-form-item-status-error
  .arco-select:not(.arco-select-disabled)
  .arco-select-view {
  background-color: var(--color-danger-light-1);
  border-color: transparent;
}
.arco-form-item-status-error
  .arco-select:not(.arco-select-disabled):hover
  .arco-select-view {
  background-color: var(--color-danger-light-2);
  border-color: transparent;
}
.arco-form-item-status-error
  .arco-select:not(.arco-select-disabled).arco-select-focused
  .arco-select-view {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--danger-6));
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-form-item-status-error
  .arco-cascader:not(.arco-cascader-disabled)
  .arco-cascader-view {
  background-color: var(--color-danger-light-1);
  border-color: transparent;
}
.arco-form-item-status-error
  .arco-cascader:not(.arco-cascader-disabled):hover
  .arco-cascader-view {
  background-color: var(--color-danger-light-2);
  border-color: transparent;
}
.arco-form-item-status-error
  .arco-cascader:not(.arco-cascader-disabled).arco-cascader-focused
  .arco-cascader-view {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--danger-6));
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-form-item-status-error
  .arco-tree-select:not(.arco-tree-select-disabled)
  .arco-tree-select-view {
  background-color: var(--color-danger-light-1);
  border-color: transparent;
}
.arco-form-item-status-error
  .arco-tree-select:not(.arco-tree-select-disabled):hover
  .arco-tree-select-view {
  background-color: var(--color-danger-light-2);
  border-color: transparent;
}
.arco-form-item-status-error
  .arco-tree-select:not(.arco-tree-select-disabled).arco-tree-select-focused
  .arco-tree-select-view {
  background-color: var(--color-bg-2);
  border-color: rgb(var(--danger-6));
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-form-item-status-error .arco-picker:not(.arco-picker-disabled) {
  border-color: transparent;
  background-color: var(--color-danger-light-1);
}
.arco-form-item-status-error .arco-picker:not(.arco-picker-disabled):hover {
  border-color: transparent;
  background-color: var(--color-danger-light-2);
}
.arco-form-item-status-error .arco-picker-focused:not(.arco-picker-disabled),
.arco-form-item-status-error
  .arco-picker-focused:not(.arco-picker-disabled):hover {
  border-color: rgb(var(--danger-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-form-item-status-error .arco-input-tag {
  background-color: var(--color-danger-light-1);
  border-color: transparent;
}
.arco-form-item-status-error .arco-input-tag:hover {
  border-color: transparent;
  background-color: var(--color-danger-light-2);
}
.arco-form-item-status-error .arco-input-tag.arco-input-tag-focus {
  border-color: rgb(var(--danger-6));
  background-color: var(--color-bg-2);
  box-shadow: 0 0 0 0 var(--color-danger-light-2);
}
.arco-form-item-status-error .arco-form-message-help {
  color: rgb(var(--danger-6));
}
.arco-form-item-status-error
  .arco-form-message-help
  .arco-form-message-help-warning {
  color: rgb(var(--warning-6));
}
.arco-form-item-feedback-error {
  color: rgb(var(--danger-6));
}
.arco-form-item-control-children {
  position: relative;
}
.arco-form-item-feedback {
  position: absolute;
  right: 9px;
  top: 50%;
  font-size: 14px;
  transform: translateY(-50%);
}
.arco-form-item-feedback .arco-icon-loading {
  font-size: 12px;
}
.arco-form-item-has-feedback .arco-input,
.arco-form-item-has-feedback .arco-input-inner-wrapper,
.arco-form-item-has-feedback .arco-textarea,
.arco-form-item-has-feedback
  .arco-select.arco-select-multiple
  .arco-select-view,
.arco-form-item-has-feedback .arco-select.arco-select-single .arco-select-view {
  padding-right: 28px;
}
.arco-form-item-has-feedback
  .arco-select.arco-select-multiple
  .arco-select-suffix {
  padding-right: 0;
}
.arco-form-item-has-feedback
  .arco-cascader.arco-cascader-multiple
  .arco-cascader-view,
.arco-form-item-has-feedback
  .arco-cascader.arco-cascader-single
  .arco-cascader-view {
  padding-right: 28px;
}
.arco-form-item-has-feedback
  .arco-cascader.arco-cascader-multiple
  .arco-cascader-suffix {
  padding-right: 0;
}
.arco-form-item-has-feedback
  .arco-tree-select.arco-tree-select-multiple
  .arco-tree-select-view,
.arco-form-item-has-feedback
  .arco-tree-select.arco-tree-select-single
  .arco-tree-select-view {
  padding-right: 28px;
}
.arco-form-item-has-feedback
  .arco-tree-select.arco-tree-select-multiple
  .arco-tree-select-suffix {
  padding-right: 0;
}
.arco-form-item-has-feedback .arco-picker {
  padding-right: 28px;
}
.arco-form-item-has-feedback .arco-picker-suffix .arco-picker-suffix-icon,
.arco-form-item-has-feedback .arco-picker-suffix .arco-picker-clear-icon {
  margin-right: 0;
  margin-left: 0;
}
.arco-form-item-has-feedback .arco-input-tag {
  padding-right: 23px;
}
.arco-form-item-has-feedback .arco-input-tag-suffix {
  padding-right: 0;
}
.arco-form-rtl .arco-form-item-feedback {
  right: unset;
  left: 9px;
}
.arco-form-rtl .arco-form-item-has-feedback .arco-input,
.arco-form-rtl .arco-form-item-has-feedback .arco-input-inner-wrapper,
.arco-form-rtl .arco-form-item-has-feedback .arco-textarea,
.arco-form-rtl
  .arco-form-item-has-feedback
  .arco-select.arco-select-multiple
  .arco-select-view,
.arco-form-rtl
  .arco-form-item-has-feedback
  .arco-select.arco-select-single
  .arco-select-view {
  padding-left: 28px;
}
.arco-form-rtl
  .arco-form-item-has-feedback
  .arco-select.arco-select-multiple
  .arco-select-suffix {
  padding-left: 0;
}
.arco-form-rtl
  .arco-form-item-has-feedback
  .arco-cascader.arco-cascader-multiple
  .arco-cascader-view,
.arco-form-rtl
  .arco-form-item-has-feedback
  .arco-cascader.arco-cascader-single
  .arco-cascader-view {
  padding-left: 28px;
}
.arco-form-rtl
  .arco-form-item-has-feedback
  .arco-cascader.arco-cascader-multiple
  .arco-cascader-suffix {
  padding-left: 0;
}
.arco-form-rtl
  .arco-form-item-has-feedback
  .arco-tree-select.arco-tree-select-multiple
  .arco-tree-select-view,
.arco-form-rtl
  .arco-form-item-has-feedback
  .arco-tree-select.arco-tree-select-single
  .arco-tree-select-view {
  padding-left: 28px;
}
.arco-form-rtl
  .arco-form-item-has-feedback
  .arco-tree-select.arco-tree-select-multiple
  .arco-tree-select-suffix {
  padding-left: 0;
}
.arco-form-rtl .arco-form-item-has-feedback .arco-picker {
  padding-left: 28px;
}
.arco-form-rtl
  .arco-form-item-has-feedback
  .arco-picker-suffix
  .arco-picker-suffix-icon,
.arco-form-rtl
  .arco-form-item-has-feedback
  .arco-picker-suffix
  .arco-picker-clear-icon {
  margin-right: 0;
  margin-left: 0;
}
.arco-form-rtl .arco-form-item-has-feedback .arco-input-tag {
  padding-left: 23px;
}
.arco-form-rtl .arco-form-item-has-feedback .arco-input-tag-suffix {
  padding-left: 0;
}
.formblink-enter,
.formblink-appear {
  opacity: 0;
}
.formblink-enter-active,
.formblink-appear-active {
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0, 0, 1, 1);
}
.formblink-enter-done {
  animation: arco-form-blink 0.5s cubic-bezier(0, 0, 1, 1);
}
@keyframes arco-form-blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.2;
  }
  to {
    opacity: 1;
  }
}
.arco-form-rtl {
  direction: rtl;
}
.arco-form-rtl .arco-form-item > .arco-form-label-item {
  padding-left: 16px;
  padding-right: 0;
}
.arco-form-rtl .arco-form-label-item {
  text-align: left;
}
.arco-form-rtl .arco-form-layout-vertical > .arco-form-label-item {
  text-align: right;
}
.arco-form-rtl .arco-form-layout-inline {
  margin-right: 0;
  margin-left: 24px;
}
._container_1bc2k_1 {
  display: flex;
  height: 100vh;
}
._container_1bc2k_1 ._banner_1bc2k_5 {
  width: 550px;
  background: linear-gradient(163.85deg, #1d2129 0%, #00308f 100%);
}
._container_1bc2k_1 ._content_1bc2k_9 {
  flex: 1;
  position: relative;
  padding-bottom: 40px;
}
._container_1bc2k_1 ._footer_1bc2k_14 {
  width: 100%;
  position: absolute;
  bottom: 0;
  right: 0;
}
._logo_1bc2k_20 {
  position: fixed;
  top: 24px;
  left: 22px;
  display: inline-flex;
  align-items: center;
  z-index: 1;
}
._logo-text_1bc2k_28 {
  margin-left: 4px;
  margin-right: 4px;
  font-size: 20px;
  color: var(--color-fill-1);
}
._banner_1bc2k_5 {
  display: flex;
  justify-content: center;
  align-items: center;
}
._banner-inner_1bc2k_39 {
  height: 100%;
  flex: 1;
}
._content_1bc2k_9 {
  display: flex;
  justify-content: center;
  align-items: center;
}
._carousel_1bc2k_48 {
  height: 100%;
}
._carousel-item_1bc2k_51 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}
._carousel-title_1bc2k_58 {
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  color: var(--color-fill-1);
}
._carousel-sub-title_1bc2k_64 {
  margin-top: 8px;
  font-size: 14px;
  line-height: 22px;
  color: var(--color-text-3);
}
._carousel-image_1bc2k_70 {
  margin-top: 30px;
  width: 320px;
}
._login-form-wrapper_1bc2k_74 {
  width: 320px;
}
._login-form-title_1bc2k_77 {
  font-size: 24px;
  font-weight: 500;
  color: var(--color-text-1);
  line-height: 32px;
}
._login-form-sub-title_1bc2k_83 {
  font-size: 16px;
  line-height: 24px;
  color: var(--color-text-3);
}
._login-form-error-msg_1bc2k_88 {
  height: 32px;
  line-height: 32px;
  color: rgb(var(--red-6));
}
._login-form-password-actions_1bc2k_93 {
  display: flex;
  justify-content: space-between;
}
._login-form-register-btn_1bc2k_97 {
  color: var(--color-text-3) !important;
}
@keyframes arco-carousel-slide-x-in {
  0% {
    transform: translate(100%);
  }
  to {
    transform: translate(0);
  }
}
@keyframes arco-carousel-slide-x-out {
  0% {
    transform: translate(0);
  }
  to {
    transform: translate(-100%);
  }
}
@keyframes arco-carousel-slide-x-in-reverse {
  0% {
    transform: translate(-100%);
  }
  to {
    transform: translate(0);
  }
}
@keyframes arco-carousel-slide-x-out-reverse {
  0% {
    transform: translate(0);
  }
  to {
    transform: translate(100%);
  }
}
@keyframes arco-carousel-slide-y-in {
  0% {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
@keyframes arco-carousel-slide-y-out {
  0% {
    transform: translateY(0);
  }
  to {
    transform: translateY(-100%);
  }
}
@keyframes arco-carousel-slide-y-in-reverse {
  0% {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}
@keyframes arco-carousel-slide-y-out-reverse {
  0% {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}
@keyframes arco-carousel-card-bottom-to-middle {
  0% {
    opacity: 0;
    transform: translate(0) translateZ(-400px);
  }
  to {
    opacity: 0.4;
    transform: translate(0) translateZ(-200px);
  }
}
@keyframes arco-carousel-card-middle-to-bottom {
  0% {
    opacity: 0.4;
    transform: translate(-100%) translateZ(-200px);
  }
  to {
    opacity: 0;
    transform: translate(-100%) translateZ(-400px);
  }
}
@keyframes arco-carousel-card-middle-to-bottom-rtl {
  0% {
    opacity: 0.4;
    transform: translate(100%) translateZ(-200px);
  }
  to {
    opacity: 0;
    transform: translate(100%) translateZ(-400px);
  }
}
@keyframes arco-carousel-card-top-to-middle {
  0% {
    opacity: 1;
    transform: translate(-50%) translateZ(0);
  }
  to {
    opacity: 0.4;
    transform: translate(-100%) translateZ(-200px);
  }
}
@keyframes arco-carousel-card-top-to-middle-rtl {
  0% {
    opacity: 1;
    transform: translate(50%) translateZ(0);
  }
  to {
    opacity: 0.4;
    transform: translate(100%) translateZ(-200px);
  }
}
@keyframes arco-carousel-card-middle-to-top {
  0% {
    opacity: 0.4;
    transform: translate(0) translateZ(-200px);
  }
  to {
    opacity: 1;
    transform: translate(-50%) translateZ(0);
  }
}
@keyframes arco-carousel-card-middle-to-top-rtl {
  0% {
    opacity: 0.4;
    transform: translate(0) translateZ(-200px);
  }
  to {
    opacity: 1;
    transform: translate(50%) translateZ(0);
  }
}
@keyframes arco-carousel-card-bottom-to-middle-reverse {
  0% {
    opacity: 0;
    transform: translate(-100%) translateZ(-400px);
  }
  to {
    opacity: 0.4;
    transform: translate(-100%) translateZ(-200px);
  }
}
@keyframes arco-carousel-card-bottom-to-middle-reverse-rtl {
  0% {
    opacity: 0;
    transform: translate(100%) translateZ(-400px);
  }
  to {
    opacity: 0.4;
    transform: translate(100%) translateZ(-200px);
  }
}
@keyframes arco-carousel-card-middle-to-bottom-reverse {
  0% {
    opacity: 0.4;
    transform: translate(0) translateZ(-200px);
  }
  to {
    opacity: 0;
    transform: translate(0) translateZ(-400px);
  }
}
@keyframes arco-carousel-card-top-to-middle-reverse {
  0% {
    opacity: 1;
    transform: translate(-50%) translateZ(0);
  }
  to {
    opacity: 0.4;
    transform: translate(0) translateZ(-200px);
  }
}
@keyframes arco-carousel-card-top-to-middle-reverse-rtl {
  0% {
    opacity: 1;
    transform: translate(50%) translateZ(0);
  }
  to {
    opacity: 0.4;
    transform: translate(0) translateZ(-200px);
  }
}
@keyframes arco-carousel-card-middle-to-top-reverse {
  0% {
    opacity: 0.4;
    transform: translate(-100%) translateZ(-200px);
  }
  to {
    opacity: 1;
    transform: translate(-50%) translateZ(0);
  }
}
@keyframes arco-carousel-card-middle-to-top-reverse-rtl {
  0% {
    opacity: 0.4;
    transform: translate(100%) translateZ(-200px);
  }
  to {
    opacity: 1;
    transform: translate(50%) translateZ(0);
  }
}
@keyframes arco-carousel-card-right-to-middle {
  0% {
    opacity: 0;
    transform: translate(-50%) translateY(0) translateZ(-400px);
  }
  to {
    opacity: 0.4;
    transform: translate(-50%) translateY(0) translateZ(-200px);
  }
}
@keyframes arco-carousel-card-middle-to-right {
  0% {
    opacity: 0.4;
    transform: translate(-50%) translateY(-100%) translateZ(-200px);
  }
  to {
    opacity: 0;
    transform: translate(-50%) translateY(-100%) translateZ(-400px);
  }
}
@keyframes arco-carousel-card-left-to-middle {
  0% {
    opacity: 1;
    transform: translate(-50%) translateY(-50%) translateZ(0);
  }
  to {
    opacity: 0.4;
    transform: translate(-50%) translateY(-100%) translateZ(-200px);
  }
}
@keyframes arco-carousel-card-middle-to-left {
  0% {
    opacity: 0.4;
    transform: translate(-50%) translateY(0) translateZ(-200px);
  }
  to {
    opacity: 1;
    transform: translate(-50%) translateY(-50%) translateZ(0);
  }
}
@keyframes arco-carousel-card-right-to-middle-reverse {
  0% {
    opacity: 0;
    transform: translate(-50%) translateY(-100%) translateZ(-400px);
  }
  to {
    opacity: 0.4;
    transform: translate(-50%) translateY(-100%) translateZ(-200px);
  }
}
@keyframes arco-carousel-card-middle-to-right-reverse {
  0% {
    opacity: 0.4;
    transform: translate(-50%) translateY(0) translateZ(-200px);
  }
  to {
    opacity: 0;
    transform: translate(-50%) translateY(0) translateZ(-400px);
  }
}
@keyframes arco-carousel-card-left-to-middle-reverse {
  0% {
    opacity: 1;
    transform: translate(-50%) translateY(-50%) translateZ(0);
  }
  to {
    opacity: 0.4;
    transform: translate(-50%) translateY(0) translateZ(-200px);
  }
}
@keyframes arco-carousel-card-middle-to-left-reverse {
  0% {
    opacity: 0.4;
    transform: translate(-50%) translateY(-100%) translateZ(-200px);
  }
  to {
    opacity: 1;
    transform: translate(-50%) translateY(-50%) translateZ(0);
  }
}
.arco-carousel {
  position: relative;
}
.arco-carousel-indicator-position-outer {
  margin-bottom: 30px;
}
.arco-carousel-slide,
.arco-carousel-card,
.arco-carousel-fade {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}
.arco-carousel-slide > *,
.arco-carousel-card > *,
.arco-carousel-fade > * {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.arco-carousel-item-current {
  z-index: 1;
  position: relative;
}
.arco-carousel-slide > *:not(.arco-carousel-item-current) {
  visibility: hidden;
}
.arco-carousel-slide.arco-carousel-horizontal .arco-carousel-item-slide-out {
  display: block;
  animation: arco-carousel-slide-x-out;
}
.arco-carousel-slide.arco-carousel-horizontal .arco-carousel-item-slide-in {
  display: block;
  animation: arco-carousel-slide-x-in;
}
.arco-carousel-slide.arco-carousel-horizontal.arco-carousel-negative
  .arco-carousel-item-slide-out {
  animation: arco-carousel-slide-x-out-reverse;
}
.arco-carousel-slide.arco-carousel-horizontal.arco-carousel-negative
  .arco-carousel-item-slide-in {
  animation: arco-carousel-slide-x-in-reverse;
}
.arco-carousel-slide.arco-carousel-vertical .arco-carousel-item-slide-out {
  display: block;
  animation: arco-carousel-slide-y-out;
}
.arco-carousel-slide.arco-carousel-vertical .arco-carousel-item-slide-in {
  display: block;
  animation: arco-carousel-slide-y-in;
}
.arco-carousel-slide.arco-carousel-vertical.arco-carousel-negative
  .arco-carousel-item-slide-out {
  animation: arco-carousel-slide-y-out-reverse;
}
.arco-carousel-slide.arco-carousel-vertical.arco-carousel-negative
  .arco-carousel-item-slide-in {
  animation: arco-carousel-slide-y-in-reverse;
}
.arco-carousel-card {
  perspective: 800px;
}
.arco-carousel-card.arco-carousel-horizontal > * {
  left: 50%;
  opacity: 0;
  transform: translate(-50%) translateZ(-400px);
  animation: arco-carousel-card-middle-to-bottom;
}
.arco-carousel-rtl .arco-carousel-card.arco-carousel-horizontal > * {
  left: unset;
  right: 50%;
  animation: arco-carousel-card-middle-to-bottom-rtl;
}
.arco-carousel-card.arco-carousel-horizontal .arco-carousel-item-prev {
  opacity: 0.4;
  transform: translate(-100%) translateZ(-200px);
  animation: arco-carousel-card-top-to-middle;
}
.arco-carousel-rtl
  .arco-carousel-card.arco-carousel-horizontal
  .arco-carousel-item-prev {
  transform: translate(100%) translateZ(-200px);
  animation: arco-carousel-card-top-to-middle-rtl;
}
.arco-carousel-card.arco-carousel-horizontal .arco-carousel-item-next {
  opacity: 0.4;
  transform: translate(0) translateZ(-200px);
  animation: arco-carousel-card-bottom-to-middle;
}
.arco-carousel-card.arco-carousel-horizontal .arco-carousel-item-current {
  opacity: 1;
  transform: translate(-50%) translateZ(0);
  animation: arco-carousel-card-middle-to-top;
}
.arco-carousel-rtl
  .arco-carousel-card.arco-carousel-horizontal
  .arco-carousel-item-current {
  transform: translate(50%) translateZ(0);
  animation: arco-carousel-card-middle-to-top-rtl;
}
.arco-carousel-card.arco-carousel-horizontal.arco-carousel-negative > * {
  animation: arco-carousel-card-middle-to-bottom-reverse;
}
.arco-carousel-card.arco-carousel-horizontal.arco-carousel-negative
  .arco-carousel-item-prev {
  animation: arco-carousel-card-bottom-to-middle-reverse;
}
.arco-carousel-rtl
  .arco-carousel-card.arco-carousel-horizontal.arco-carousel-negative
  .arco-carousel-item-prev {
  animation: arco-carousel-card-bottom-to-middle-reverse-rtl;
}
.arco-carousel-card.arco-carousel-horizontal.arco-carousel-negative
  .arco-carousel-item-next {
  animation: arco-carousel-card-top-to-middle-reverse;
}
.arco-carousel-rtl
  .arco-carousel-card.arco-carousel-horizontal.arco-carousel-negative
  .arco-carousel-item-next {
  animation: arco-carousel-card-top-to-middle-reverse-rtl;
}
.arco-carousel-card.arco-carousel-horizontal.arco-carousel-negative
  .arco-carousel-item-current {
  animation: arco-carousel-card-middle-to-top-reverse;
}
.arco-carousel-rtl
  .arco-carousel-card.arco-carousel-horizontal.arco-carousel-negative
  .arco-carousel-item-current {
  animation: arco-carousel-card-middle-to-top-reverse-rtl;
}
.arco-carousel-card.arco-carousel-vertical > * {
  top: 50%;
  left: 50%;
  opacity: 0;
  transform: translate(-50%) translateY(-50%) translateZ(-400px);
  animation: arco-carousel-card-middle-to-right;
  display: flex;
  justify-content: center;
}
.arco-carousel-card.arco-carousel-vertical .arco-carousel-item-prev {
  opacity: 0.4;
  transform: translate(-50%) translateY(-100%) translateZ(-200px);
  animation: arco-carousel-card-left-to-middle;
}
.arco-carousel-card.arco-carousel-vertical .arco-carousel-item-next {
  opacity: 0.4;
  transform: translate(-50%) translateY(0) translateZ(-200px);
  animation: arco-carousel-card-right-to-middle;
}
.arco-carousel-card.arco-carousel-vertical .arco-carousel-item-current {
  opacity: 1;
  transform: translate(-50%) translateY(-50%) translateZ(0);
  animation: arco-carousel-card-middle-to-left;
}
.arco-carousel-card.arco-carousel-negative > * {
  animation: arco-carousel-card-middle-to-right-reverse;
}
.arco-carousel-card.arco-carousel-negative .arco-carousel-item-prev {
  animation: arco-carousel-card-right-to-middle-reverse;
}
.arco-carousel-card.arco-carousel-negative .arco-carousel-item-next {
  animation: arco-carousel-card-left-to-middle-reverse;
}
.arco-carousel-card.arco-carousel-negative .arco-carousel-item-current {
  animation: arco-carousel-card-middle-to-left-reverse;
}
.arco-carousel-fade > * {
  left: 50%;
  transform: translate(-50%);
  opacity: 0;
}
.arco-carousel-fade .arco-carousel-item-current {
  opacity: 1;
}
.arco-carousel-indicator {
  display: flex;
  position: absolute;
  margin: 0;
  padding: 0;
}
.arco-carousel-indicator-wrapper {
  position: absolute;
  z-index: 2;
}
.arco-carousel-indicator-wrapper-top {
  left: 0;
  right: 0;
  top: 0;
  height: 48px;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.15) 0%,
    rgba(0, 0, 0, 0) 87%
  );
}
.arco-carousel-indicator-wrapper-bottom {
  left: 0;
  right: 0;
  bottom: 0;
  height: 48px;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 13%,
    rgba(0, 0, 0, 0.15) 100%
  );
}
.arco-carousel-indicator-wrapper-left {
  left: 0;
  top: 0;
  width: 48px;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.15) 0%,
    rgba(0, 0, 0, 0) 87%
  );
}
.arco-carousel-indicator-wrapper-right {
  right: 0;
  top: 0;
  width: 48px;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0) 13%,
    rgba(0, 0, 0, 0.15) 100%
  );
}
.arco-carousel-indicator-wrapper-outer {
  left: 0;
  right: 0;
  background: none;
}
.arco-carousel-indicator-wrapper-outer-right {
  right: 0;
  top: 0;
  width: 20px;
  height: 100%;
}
.arco-carousel-indicator-bottom {
  bottom: 12px;
  left: 50%;
  transform: translate(-50%);
}
.arco-carousel-indicator-top {
  top: 12px;
  left: 50%;
  transform: translate(-50%);
}
.arco-carousel-indicator-left {
  left: 12px;
  top: 50%;
  transform: translate(-50%, -50%) rotate(90deg);
}
.arco-carousel-indicator-right {
  right: 12px;
  top: 50%;
  transform: translate(50%, -50%) rotate(90deg);
}
.arco-carousel-indicator-outer {
  left: 50%;
  transform: translate(-50%);
  padding: 4px;
  border-radius: 20px;
  background-color: transparent;
}
.arco-carousel-indicator-outer.arco-carousel-indicator-dot {
  bottom: -22px;
}
.arco-carousel-indicator-outer.arco-carousel-indicator-line {
  bottom: -20px;
}
.arco-carousel-indicator-outer.arco-carousel-indicator-slider {
  padding: 0;
  bottom: -16px;
  background-color: rgba(var(--gray-4), 0.5);
}
.arco-carousel-indicator-outer .arco-carousel-indicator-item {
  background-color: rgba(var(--gray-4), 0.5);
}
.arco-carousel-indicator-outer .arco-carousel-indicator-item:hover,
.arco-carousel-indicator-outer .arco-carousel-indicator-item-active {
  background-color: var(--color-fill-4);
}
.arco-carousel-indicator-outer-right {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(90deg);
  padding: 4px;
  border-radius: 20px;
  background-color: transparent;
}
.arco-carousel-indicator-outer-right.arco-carousel-indicator-slider {
  padding: 0;
  background-color: rgba(var(--gray-4), 0.5);
}
.arco-carousel-indicator-outer-right .arco-carousel-indicator-item {
  background-color: rgba(var(--gray-4), 0.5);
}
.arco-carousel-indicator-outer-right .arco-carousel-indicator-item:hover,
.arco-carousel-indicator-outer-right .arco-carousel-indicator-item-active {
  background-color: var(--color-fill-4);
}
.arco-carousel-indicator-item {
  display: inline-block;
  border-radius: var(--border-radius-medium);
  background-color: #ffffff4d;
  cursor: pointer;
}
.arco-carousel-indicator-item:hover,
.arco-carousel-indicator-item-active {
  background-color: var(--color-white);
}
.arco-carousel-indicator-dot .arco-carousel-indicator-item {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}
.arco-carousel-indicator-dot .arco-carousel-indicator-item:not(:last-child) {
  margin-right: 8px;
}
.arco-carousel-indicator-line .arco-carousel-indicator-item {
  width: 12px;
  height: 4px;
}
.arco-carousel-indicator-line .arco-carousel-indicator-item:not(:last-child) {
  margin-right: 8px;
}
.arco-carousel-indicator-slider {
  width: 48px;
  height: 4px;
  border-radius: var(--border-radius-medium);
  background-color: #ffffff4d;
  cursor: pointer;
}
.arco-carousel-indicator-slider .arco-carousel-indicator-item {
  position: absolute;
  top: 0;
  height: 100%;
  transition: left 0.3s;
}
.arco-carousel-arrow > div {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  color: var(--color-white);
  background-color: #ffffff4d;
  cursor: pointer;
  z-index: 2;
  user-select: none;
}
.arco-carousel-arrow > div:focus-visible {
  box-shadow: 0 0 0 2px var(--color-primary-light-3);
}
.arco-carousel-arrow > div > svg {
  color: var(--color-white);
  font-size: 14px;
}
.arco-carousel-arrow > div:hover {
  background-color: #ffffff80;
}
.arco-carousel-arrow-left {
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
}
.arco-carousel-arrow-right {
  top: 50%;
  transform: translateY(-50%);
  right: 12px;
}
.arco-carousel-arrow-top {
  left: 50%;
  transform: translate(-50%);
  top: 12px;
}
.arco-carousel-arrow-bottom {
  left: 50%;
  transform: translate(-50%);
  bottom: 12px;
}
.arco-carousel-arrow-hover div {
  opacity: 0;
  transition: all 0.3s;
}
.arco-carousel:hover .arco-carousel-arrow-hover div {
  opacity: 1;
}
.arco-carousel-rtl {
  direction: rtl;
}
.arco-carousel-rtl
  .arco-carousel-indicator-dot
  .arco-carousel-indicator-item:not(:last-child) {
  margin-left: 8px;
  margin-right: 0;
}
.arco-carousel-rtl
  .arco-carousel-indicator-line
  .arco-carousel-indicator-item:not(:last-child) {
  margin-left: 8px;
  margin-right: 0;
}
body[arco-theme='dark'] .arco-carousel-arrow > div {
  background-color: rgba(var(--gray-1), 0.3);
}
body[arco-theme='dark'] .arco-carousel-arrow > div:hover {
  background-color: rgba(var(--gray-1), 0.5);
}
body[arco-theme='dark'] .arco-carousel-indicator-item,
body[arco-theme='dark'] .arco-carousel-indicator-slider {
  background-color: rgba(var(--gray-1), 0.3);
}
body[arco-theme='dark'] .arco-carousel-indicator-item-active,
body[arco-theme='dark'] .arco-carousel-indicator-item:hover {
  background-color: var(--color-white);
}
body[arco-theme='dark']
  .arco-carousel-indicator-outer.arco-carousel-indicator-slider {
  background-color: rgba(var(--gray-4), 0.5);
}
body[arco-theme='dark']
  .arco-carousel-indicator-outer
  .arco-carousel-indicator-item:hover,
body[arco-theme='dark']
  .arco-carousel-indicator-outer
  .arco-carousel-indicator-item-active {
  background-color: var(--color-fill-4);
}
