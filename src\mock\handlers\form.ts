import { http, HttpResponse } from 'msw';

const formHandlers = [
  // 表单 step 模块
  http.post('/api/form/step', async ({ request }) => {
    const data = await request.json();
    return HttpResponse.json({
      status: 'success',
      data,
    });
  }),

  // 表单 group 模块
  http.post('/api/form/group', async ({ request }) => {
    const data = await request.json();
    return HttpResponse.json({
      status: 'success',
      data,
    });
  }),
];

export default formHandlers;
