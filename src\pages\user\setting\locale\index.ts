const i18n = {
  'en-US': {
    'menu.user': 'Personal Center',
    'menu.user.setting': 'User Setting',
    'userSetting.menu.title.info': 'Personal Information',
    'userSetting.menu.title.account': 'Account Setting',
    'userSetting.menu.title.password': 'Password',
    'userSetting.menu.title.message': 'Message Notification',
    'userSetting.menu.title.result': 'Result',
    'userSetting.menu.title.data': 'Export Data',
    'userSetting.saveSuccess': 'Save Success',
    'userSetting.title.basicInfo': 'Basic Information',
    'userSetting.title.security': 'Security Settings',
    'userSetting.label.avatar': 'Avatar',
    'userSetting.label.name': 'User Name',
    'userSetting.label.accountId': 'Account ID',
    'userSetting.label.verified': 'Whether Verified',
    'userSetting.value.verified': 'verified',
    'userSetting.value.notVerified': 'not verified',
    'userSetting.label.phoneNumber': 'Phone Number',
    'userSetting.label.registrationTime': 'Registration time',
    'userSetting.btn.edit': 'Edit',
    'userSetting.save': 'Save',
    'userSetting.reset': 'Reset',

    'userSetting.info.email': 'Email',
    'userSetting.info.email.placeholder':
      'Please enter your email address, <NAME_EMAIL>',
    'userSetting.info.nickName': 'Nick name',
    'userSetting.info.nickName.placeholder': 'Please enter your nickname',
    'userSetting.info.area': 'Country / Region',
    'userSetting.info.area.placeholder': 'Please select a country/region',
    'userSetting.info.location': 'Your location',
    'userSetting.info.address': 'Specific address',
    'userSetting.info.address.placeholder': 'Please enter your address',
    'userSetting.info.profile': 'Personal profile',
    'userSetting.info.profile.placeholder':
      'Please enter your profile, no more than 200 words.',
    'userSetting.security.password': 'Login Password',
    'userSetting.security.password.tips':
      'Has been set. The password has at least 6 characters, supports numbers, letters and special characters except spaces, and must contain both numbers and uppercase and lowercase letters. ',
    'userSetting.security.question': 'Secure question',
    'userSetting.security.question.placeholder':
      'You have not set a secret security question, which can effectively protect the security of your account.',
    'userSetting.security.phone': 'Secure phone',
    'userSetting.security.phone.tips': 'Bound:',
    'userSetting.security.email': 'Secure email',
    'userSetting.security.email.placeholder':
      'You have not set up an email address yet. The bound email address can be used to retrieve your password, receive notifications, etc.',
    'userSetting.verified.enterprise': 'Enterprise real-name certification',
    'userSetting.verified.records': 'Certification records',
    'userSetting.verified.label.accountType': 'Account Type',

    'userSetting.verified.label.isVerified': 'Authentication status',
    'userSetting.verified.label.verifiedTime': 'Authentication time',
    'userSetting.verified.label.legalPersonName': 'Legal Person name',
    'userSetting.verified.label.certificateType':
      'Type of legal person certificate',
    'userSetting.verified.label.certificationNumber':
      'Legal person certification number',
    'userSetting.verified.label.enterpriseName': 'Enterprise Name',

    'userSetting.verified.label.enterpriseCertificateType':
      'Enterprise certificate type',
    'userSetting.verified.label.organizationCode': 'Organization Code',

    'userSetting.verified.authType': 'Authentication type',
    'userSetting.verified.authContent': 'Authentication content',
    'userSetting.verified.authStatus': 'Current status',
    'userSetting.verified.createdTime': 'Created time',
    'userSetting.verified.operation': 'Operation',
    'userSetting.verified.operation.view': 'View',
    'userSetting.verified.operation.revoke': 'Revoke',
    'userSetting.verified.status.success': 'passed',
    'userSetting.verified.status.waiting': 'under review',
  },

  'zh-CN': {
    'menu.user': '个人中心',
    'menu.user.setting': '用户设置',
    'userSetting.menu.title.info': '个人信息',
    'userSetting.menu.title.account': '账号设置',
    'userSetting.menu.title.password': '密码',
    'userSetting.menu.title.message': '消息通知',
    'userSetting.menu.title.result': '结果页',
    'userSetting.menu.title.data': '导出数据',
    'userSetting.saveSuccess': '保存成功',
    'userSetting.title.basicInfo': '基本信息',
    'userSetting.title.security': '安全设置',
    'userSetting.label.avatar': '头像',
    'userSetting.label.name': '用户名',
    'userSetting.label.accountId': '账号ID',
    'userSetting.label.verified': '实名认证',
    'userSetting.value.verified': '已认证',
    'userSetting.value.notVerified': '未认证',
    'userSetting.label.phoneNumber': '手机号码',
    'userSetting.label.registrationTime': '注册时间',
    'userSetting.btn.edit': '修改',
    'userSetting.btn.set': '设置',
    'userSetting.save': '保存',
    'userSetting.reset': '重置',
    'userSetting.info.email': '邮箱',
    'userSetting.info.email.placeholder': '请输入邮箱地址，如*****************',
    'userSetting.info.nickName': '昵称',
    'userSetting.info.nickName.placeholder': '请输入您的昵称',
    'userSetting.info.area': '国家/地区',
    'userSetting.info.area.placeholder': '请选择国家/地区',
    'userSetting.info.location': '所在区域',
    'userSetting.info.address': '具体地址',
    'userSetting.info.address.placeholder': '请输入您的地址',
    'userSetting.info.profile': '个人简介',
    'userSetting.info.profile.placeholder':
      '请输入您的个人简介，最多不超过200字。',
    'userSetting.security.password': '登陆密码',
    'userSetting.security.password.tips':
      '已设置。密码至少6位字符，支持数字、字母和除空格外的特殊字符，且必须同时包含数字和大小写字母。',
    'userSetting.security.question': '密保问题',
    'userSetting.security.question.placeholder':
      '您暂未设置密保问题，密保问题可以有效的保护账号的安全。',
    'userSetting.security.phone': '安全手机',
    'userSetting.security.phone.tips': '已绑定：',
    'userSetting.security.email': '安全邮箱',
    'userSetting.security.email.placeholder':
      '您暂未设置邮箱，绑定邮箱可以用来找回密码、接收通知等。',

    'userSetting.verified.enterprise': '企业实名认证',
    'userSetting.verified.label.accountType': '账号类型',
    'userSetting.verified.label.isVerified': '认证状态',
    'userSetting.verified.label.verifiedTime': '认证时间',
    'userSetting.verified.label.legalPersonName': '法人姓名',
    'userSetting.verified.label.certificateType': '法人证件类型',
    'userSetting.verified.label.certificationNumber': '法人认证号码',
    'userSetting.verified.label.enterpriseName': '企业名称',
    'userSetting.verified.label.enterpriseCertificateType': '企业证件类型',
    'userSetting.verified.label.organizationCode': '组织机构代码',
    'userSetting.verified.records': '认证记录',

    'userSetting.verified.authType': '认证类型',
    'userSetting.verified.authContent': '认证内容',
    'userSetting.verified.authStatus': '当前状态',
    'userSetting.verified.createdTime': '创建时间',
    'userSetting.verified.operation': '操作',
    'userSetting.verified.operation.view': '查看',
    'userSetting.verified.operation.revoke': '撤回',
    'userSetting.verified.status.success': '已通过',
    'userSetting.verified.status.waiting': '审核中',
  },
};

export default i18n;
