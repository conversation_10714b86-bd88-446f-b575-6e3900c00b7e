import { n as m, j as o, a as t, F as u, c as x } from './index.7dafa16d.js';
import { S as f } from './index.43d26da3.js';
import { S as g } from './index.dc3f6cf8.js';
import {
  C as p,
  L as j,
  l as k,
  I as _,
  c as v,
  T as b,
  b as F,
} from './index.1a52f4db.js';
import { I as S, a as D, b as C } from './index.9498526d.js';
import './b-tween.es.d368a2a1.js';
import './pad.af73d6a9.js';
const N = '_card_3ij6k_1',
  w = '_statistic_3ij6k_17',
  I = '_chart_3ij6k_20',
  L = '_title_3ij6k_26',
  T = '_diff_3ij6k_42';
var r = {
  card: N,
  'card-line': '_card-line_3ij6k_8',
  'card-interval': '_card-interval_3ij6k_11',
  'card-pie': '_card-pie_3ij6k_14',
  statistic: w,
  chart: I,
  title: L,
  'compare-yesterday-text': '_compare-yesterday-text_3ij6k_37',
  diff: T,
  'diff-increment': '_diff-increment_3ij6k_47',
};
const { Title: A, Text: P } = x,
  h = { pure: !0, autoFit: !0, height: 80, padding: [10, 10, 0, 10] };
function B(s) {
  const { chartData: i } = s;
  return t(p, {
    data: i,
    ...h,
    children: t(j, {
      position: 'x*y',
      size: 3,
      shape: 'smooth',
      color: ['name', ['#165DFF', 'rgba(106,161,255,0.3)']],
      style: {
        fields: ['name'],
        callback: (n) => (n === '\u7C7B\u76EE2' ? { lineDash: [8, 10] } : {}),
      },
    }),
  });
}
function E(s) {
  const { chartData: i } = s;
  return (
    k.registerShape('interval', 'border-radius', {
      draw(n, l) {
        const a = n.points;
        let e = [];
        e.push(['M', a[0].x, a[0].y]),
          e.push(['L', a[1].x, a[1].y]),
          e.push(['L', a[2].x, a[2].y]),
          e.push(['L', a[3].x, a[3].y]),
          e.push('Z'),
          (e = this.parsePath(e));
        const c = l.addGroup();
        return (
          c.addShape('rect', {
            attrs: {
              x: e[1][1],
              y: e[1][2],
              width: e[2][1] - e[1][1],
              height: e[0][2] - e[1][2],
              fill: n.color,
              radius: (e[2][1] - e[1][1]) / 2,
            },
          }),
          c
        );
      },
    }),
    t(p, {
      data: i,
      ...h,
      children: t(_, {
        position: 'x*y',
        color: ['x', (n) => (Number(n) % 2 === 0 ? '#2CAB40' : '#86DF6C')],
        shape: 'border-radius',
      }),
    })
  );
}
function G(s) {
  const { chartData: i } = s;
  return o(p, {
    data: i,
    ...h,
    padding: [0, 20, 0, 0],
    children: [
      t(v, { type: 'theta', radius: 0.8, innerRadius: 0.7 }),
      t(_, {
        adjust: 'stack',
        position: 'count',
        shape: 'sliceShape',
        color: ['name', ['#8D4EDA', '#00B2FF', '#165DFF']],
        label: !1,
      }),
      t(b, { visible: !0 }),
      t(F, { position: 'right' }),
      t(C, { type: 'element-single-selected' }),
    ],
  });
}
function H(s) {
  const {
      chartType: i,
      title: n,
      count: l,
      increment: a,
      diff: e,
      chartData: c,
      loading: d,
    } = s,
    y = m(r.card, r[`card-${i}`]);
  return o('div', {
    className: y,
    children: [
      o('div', {
        className: r.statistic,
        children: [
          t(g, {
            title: t(A, { heading: 6, className: r.title, children: n }),
            loading: d,
            value: l,
            groupSeparator: !0,
          }),
          o('div', {
            className: r['compare-yesterday'],
            children: [
              t(P, {
                type: 'secondary',
                className: r['compare-yesterday-text'],
                children: s.compareTime,
              }),
              t('span', {
                className: m(r.diff, { [r['diff-increment']]: a }),
                children: d
                  ? t(f, { text: { rows: 1 }, animation: !0 })
                  : o(u, { children: [e, a ? t(S, {}) : t(D, {})] }),
              }),
            ],
          }),
        ],
      }),
      t('div', {
        className: r.chart,
        children: d
          ? t(f, {
              text: { rows: 3, width: Array(3).fill('100%') },
              animation: !0,
            })
          : o(u, {
              children: [
                i === 'interval' && t(E, { chartData: c }),
                i === 'line' && t(B, { chartData: c }),
                i === 'pie' && t(G, { chartData: c }),
              ],
            }),
      }),
    ],
  });
}
export { H as default };
