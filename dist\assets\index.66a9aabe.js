import { u as s, a as e, m as n, B as r } from './index.7dafa16d.js';
const c = {
    'en-US': {
      'menu.exception': 'Exception page',
      'menu.exception.404': '404',
      'exception.result.404.description': 'Whoops, this page is gone.',
      'exception.result.404.retry': 'Retry',
      'exception.result.404.back': 'Back',
    },
    'zh-CN': {
      'menu.exception': '\u5F02\u5E38\u9875',
      'menu.exception.404': '404',
      'exception.result.404.description':
        '\u62B1\u6B49\uFF0C\u9875\u9762\u4E0D\u89C1\u4E86\uFF5E',
      'exception.result.404.retry': '\u91CD\u8BD5',
      'exception.result.404.back': '\u8FD4\u56DE',
    },
  },
  i = '_wrapper_vup5c_1',
  p = '_result_vup5c_6';
var u = { wrapper: i, result: p };
function a() {
  const t = s(c);
  return e('div', {
    className: u.wrapper,
    children: e(n, {
      className: u.result,
      status: '404',
      subTitle: t['exception.result.404.description'],
      extra: [
        e(
          r,
          {
            style: { marginRight: 16 },
            children: t['exception.result.404.retry'],
          },
          'again'
        ),
        e(
          r,
          { type: 'primary', children: t['exception.result.404.back'] },
          'back'
        ),
      ],
    }),
  });
}
export { a as default };
