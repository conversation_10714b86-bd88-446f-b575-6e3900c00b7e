import {
  u as b,
  r,
  j as n,
  a as t,
  c as o,
  d,
  ar as l,
  B as s,
  h as D,
  l as u,
} from './index.7dafa16d.js';
import { S as w } from './index.43d26da3.js';
import { T as L } from './index.503eee4e.js';
import { D as N } from './index.********.js';
import { l as C } from './index.69444b8c.js';
import { s as c } from './index.module.5c220b22.js';
import './b-tween.es.d368a2a1.js';
function F() {
  const e = b(C),
    [f, p] = r.exports.useState({
      accountType: '',
      isVerified: !0,
      verifiedTime: '',
      legalPersonName: '',
      certificateType: '',
      certificationNumber: '',
      enterpriseName: '',
      enterpriseCertificateType: '',
      organizationCode: '',
    }),
    [g, S] = r.exports.useState(!0),
    [h, m] = r.exports.useState([]),
    [v, x] = r.exports.useState(!0),
    y = async () => {
      const { data: a } = await u
        .get('/api/user/verified/enterprise')
        .finally(() => S(!1));
      p(a);
      const { data: i } = await u
        .get('/api/user/verified/authList')
        .finally(() => x(!1));
      m(i);
    };
  r.exports.useEffect(() => {
    y();
  }, []);
  const T = t(w, { text: { rows: 1 } });
  return n('div', {
    className: c.verified,
    children: [
      t(o.Title, {
        heading: 6,
        children: e['userSetting.verified.enterprise'],
      }),
      t(N, {
        className: c['verified-enterprise'],
        labelStyle: { textAlign: 'right' },
        layout: 'inline-horizontal',
        colon: '\uFF1A',
        column: 3,
        data: Object.entries(f).map(([a, i]) => ({
          label: e[`userSetting.verified.label.${a}`],
          value: g
            ? T
            : typeof i == 'boolean'
            ? i
              ? t(d, {
                  color: 'green',
                  children: e['userSetting.value.verified'],
                })
              : t(d, {
                  color: 'red',
                  children: e['userSetting.value.notVerified'],
                })
            : i,
        })),
      }),
      t(o.Title, { heading: 6, children: e['userSetting.verified.records'] }),
      t(L, {
        columns: [
          { title: e['userSetting.verified.authType'], dataIndex: 'authType' },
          {
            title: e['userSetting.verified.authContent'],
            dataIndex: 'authContent',
          },
          {
            title: e['userSetting.verified.authStatus'],
            dataIndex: 'authStatus',
            render(a) {
              return a
                ? t(l, {
                    status: 'success',
                    text: e['userSetting.verified.status.success'],
                  })
                : t('span', {
                    children: t(l, {
                      status: 'processing',
                      text: e['userSetting.verified.status.waiting'],
                    }),
                  });
            },
          },
          {
            title: e['userSetting.verified.createdTime'],
            dataIndex: 'createdTime',
          },
          {
            title: e['userSetting.verified.operation'],
            headerCellStyle: { paddingLeft: '15px' },
            render: (a, i) =>
              i.authStatus
                ? t(s, {
                    type: 'text',
                    children: e['userSetting.verified.operation.view'],
                  })
                : n(D, {
                    children: [
                      t(s, {
                        type: 'text',
                        children: e['userSetting.verified.operation.view'],
                      }),
                      t(s, {
                        type: 'text',
                        children: e['userSetting.verified.operation.revoke'],
                      }),
                    ],
                  }),
          },
        ],
        data: h,
        loading: v,
      }),
    ],
  });
}
export { F as default };
