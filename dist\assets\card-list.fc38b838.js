import {
  u as g,
  r as d,
  a as t,
  a5 as v,
  j as o,
  n as C,
  S,
  l as T,
  c as D,
} from './index.7dafa16d.js';
import { S as k } from './index.43d26da3.js';
import { C as w } from './index.519f9d90.js';
import { S as L } from './index.dc3f6cf8.js';
import { C as f, L as N, T as y, I } from './index.1a52f4db.js';
import { l as F } from './index.b0c339fc.js';
import { I as b, a as R, b as A } from './index.9498526d.js';
import './b-tween.es.d368a2a1.js';
import './pad.af73d6a9.js';
const G = '_card_jjj49_1',
  P = '_statistic_jjj49_1',
  $ = '_title_jjj49_4',
  q = '_diff_jjj49_16',
  B = '_tooltip_jjj49_24';
var n = {
  card: G,
  statistic: P,
  title: $,
  diff: q,
  'diff-increment': '_diff-increment_jjj49_21',
  tooltip: B,
};
const { Row: E, Col: M } = v,
  { Title: z, Text: H } = D,
  j = { pure: !0, autoFit: !0, height: 80, padding: [0, 10, 0, 10] };
function _(a) {
  const { items: r } = a;
  return t('div', {
    className: n.tooltip,
    children: r.map((s, i) =>
      t(
        'div',
        {
          children: t(H, {
            bold: !0,
            children: Number(s.data.y).toLocaleString(),
          }),
        },
        i
      )
    ),
  });
}
function J(a) {
  const { chartData: r } = a;
  return o(f, {
    data: r,
    ...j,
    children: [
      t(N, {
        position: 'x*y',
        shape: ['name', ['smooth', 'dash']],
        color: ['name', ['#165DFF', 'rgba(106,161,255,0.3)']],
      }),
      t(y, {
        shared: !1,
        showCrosshairs: !0,
        children: (s, i) => t(_, { items: i }),
      }),
    ],
  });
}
function K(a) {
  const { chartData: r } = a;
  return o(f, {
    data: r,
    ...j,
    children: [
      t(I, {
        position: 'x*y',
        color: ['x', (s) => (Number(s) % 2 === 0 ? '#86DF6C' : '#468DFF')],
      }),
      t(y, { shared: !1, children: (s, i) => t(_, { items: i }) }),
      t(A, { type: 'active-region' }),
    ],
  });
}
function O(a) {
  const {
    chartType: r,
    title: s,
    count: i,
    increment: c,
    diff: p,
    chartData: l,
    loading: e,
  } = a;
  return o(w, {
    className: n.card,
    children: [
      t('div', {
        className: n.statistic,
        children: t(L, {
          title: t(z, { heading: 6, className: n.title, children: s }),
          loading: e,
          value: i,
          extra: t('div', {
            className: n['compare-yesterday'],
            children: e
              ? t(k, {
                  text: { rows: 1 },
                  style: { width: '100px' },
                  animation: !0,
                })
              : o('span', {
                  className: C(n.diff, { [n['diff-increment']]: c }),
                  children: [p, c ? t(b, {}) : t(R, {})],
                }),
          }),
          groupSeparator: !0,
        }),
      }),
      t('div', {
        className: n.chart,
        children: o(S, {
          style: { width: '100%' },
          loading: e,
          children: [
            r === 'interval' && t(K, { chartData: l }),
            r === 'line' && t(J, { chartData: l }),
          ],
        }),
      }),
    ],
  });
}
const h = [
  { key: 'userRetentionTrend', type: 'line' },
  { key: 'userRetention', type: 'interval' },
  { key: 'contentConsumptionTrend', type: 'line' },
  { key: 'contentConsumption', type: 'interval' },
];
function at() {
  const a = g(F),
    [r, s] = d.exports.useState(!1),
    [i, c] = d.exports.useState(h.map((e) => ({ ...e, chartType: e.type }))),
    p = async () => {
      const e = h.map(async (u) => {
        const { data: x } = await T.get(
          `/api/multi-dimension/card?type=${u.type}`
        ).catch(() => ({ data: {} }));
        return { ...x, key: u.key, chartType: u.type };
      });
      s(!0);
      const m = await Promise.all(e).finally(() => s(!1));
      c(m);
    };
  d.exports.useEffect(() => {
    p();
  }, []);
  const l = d.exports.useMemo(
    () =>
      i.map((e) => ({ ...e, title: a[`multiDAnalysis.cardList.${e.key}`] })),
    [a, i]
  );
  return t(E, {
    gutter: 16,
    children: l.map((e, m) =>
      t(M, { span: 6, children: t(O, { ...e, loading: r }) }, m)
    ),
  });
}
export { at as default };
