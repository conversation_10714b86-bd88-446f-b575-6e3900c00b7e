import {
  R as w,
  r as l,
  g as v,
  j as b,
  a as c,
  _ as h,
} from './index.7dafa16d.js';
import { u as y } from './index.1a52f4db.js';
function u(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (o) {
        return Object.getOwnPropertyDescriptor(e, o).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function d(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? u(Object(t), !0).forEach(function (r) {
          h(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : u(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function j(e, n) {
  var t = l.exports.useContext(v),
    r = t.prefixCls,
    o = r === void 0 ? 'arco' : r,
    i = e.spin,
    s = e.className,
    a = d(
      d({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(s ? s + ' ' : '')
          .concat(o, '-icon ')
          .concat(o, '-icon-arrow-fall'),
      }
    );
  return (
    i && (a.className = ''.concat(a.className, ' ').concat(o, '-icon-loading')),
    delete a.spin,
    delete a.isIcon,
    b('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...a,
      children: [
        c('path', {
          d: 'M24.008 41.99a.01.01 0 0 1-.016 0l-9.978-11.974A.01.01 0 0 1 14.02 30H33.98a.01.01 0 0 1 .007.016l-9.978 11.975Z',
        }),
        c('path', {
          fill: 'currentColor',
          stroke: 'none',
          d: 'M24 42 14 30h20L24 42Z',
        }),
        c('path', { d: 'M22 6H26V32H22z' }),
        c('path', {
          fill: 'currentColor',
          stroke: 'none',
          d: 'M22 6H26V32H22z',
        }),
      ],
    })
  );
}
var f = w.forwardRef(j);
f.defaultProps = { isIcon: !0 };
f.displayName = 'IconArrowFall';
var I = f;
function O(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (o) {
        return Object.getOwnPropertyDescriptor(e, o).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function m(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? O(Object(t), !0).forEach(function (r) {
          h(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : O(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function g(e, n) {
  var t = l.exports.useContext(v),
    r = t.prefixCls,
    o = r === void 0 ? 'arco' : r,
    i = e.spin,
    s = e.className,
    a = m(
      m({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(s ? s + ' ' : '')
          .concat(o, '-icon ')
          .concat(o, '-icon-arrow-rise'),
      }
    );
  return (
    i && (a.className = ''.concat(a.className, ' ').concat(o, '-icon-loading')),
    delete a.spin,
    delete a.isIcon,
    b('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...a,
      children: [
        c('path', {
          d: 'M23.992 6.01a.01.01 0 0 1 .016 0l9.978 11.974a.01.01 0 0 1-.007.016H14.02a.01.01 0 0 1-.007-.016l9.978-11.975Z',
        }),
        c('path', {
          fill: 'currentColor',
          stroke: 'none',
          d: 'm24 6 10 12H14L24 6Z',
        }),
        c('path', { d: 'M26 42H30V68H26z', transform: 'rotate(-180 26 42)' }),
        c('path', {
          fill: 'currentColor',
          stroke: 'none',
          d: 'M26 42H30V68H26z',
          transform: 'rotate(-180 26 42)',
        }),
      ],
    })
  );
}
var p = w.forwardRef(g);
p.defaultProps = { isIcon: !0 };
p.displayName = 'IconArrowRise';
var P = p;
function N(e) {
  var n = y(),
    t = e.type,
    r = e.config;
  return (
    l.exports.useLayoutEffect(function () {
      return (
        n.interaction(t, r),
        function () {
          n.removeInteraction(t);
        }
      );
    }),
    null
  );
}
export { P as I, I as a, N as b };
