import { http, HttpResponse } from 'msw';

const resultHandlers = [
  // 成功结果页
  http.post('/api/result/success', async ({ request }) => {
    const data = await request.json();
    return HttpResponse.json({
      status: 'success',
      data,
    });
  }),

  // 错误结果页
  http.post('/api/result/error', async ({ request }) => {
    const data = await request.json();
    return HttpResponse.json({
      status: 'error',
      data,
    });
  }),
];

export default resultHandlers;
