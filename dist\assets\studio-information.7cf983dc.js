import {
  u as a,
  j as i,
  a as e,
  c as l,
  a8 as t,
  k as r,
  B as n,
} from './index.7dafa16d.js';
import { C as d } from './index.519f9d90.js';
import { l as u } from './index.662e0e1f.js';
function h() {
  const o = a(u);
  return i(d, {
    children: [
      e(l.Title, {
        style: { marginTop: 0, marginBottom: 16 },
        heading: 6,
        children: o['monitor.title.studioInfo'],
      }),
      i(t, {
        layout: 'vertical',
        children: [
          e(t.Item, {
            label: o['monitor.studioInfo.label.studioTitle'],
            required: !0,
            children: e(r, {
              placeholder: `admin${o['monitor.studioInfo.placeholder.studioTitle']}`,
            }),
          }),
          e(t.Item, {
            label: o['monitor.studioInfo.label.onlineNotification'],
            required: !0,
            children: e(r.<PERSON><PERSON>, {}),
          }),
          e(t.Item, {
            label: o['monitor.studioInfo.label.studioCategory'],
            required: !0,
            children: e(r.Search, {}),
          }),
          e(t.Item, {
            label: o['monitor.studioInfo.label.studioCategory'],
            required: !0,
            children: e(r.Search, {}),
          }),
        ],
      }),
      e(n, { type: 'primary', children: '\u66F4\u65B0' }),
    ],
  });
}
export { h as default };
