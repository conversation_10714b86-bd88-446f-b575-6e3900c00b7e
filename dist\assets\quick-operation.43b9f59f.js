import {
  R as p,
  r as f,
  g as u,
  a as c,
  _ as d,
  j as O,
  u as N,
  c as S,
  h as k,
  B as l,
} from './index.7dafa16d.js';
import { C as D } from './index.519f9d90.js';
import { l as _ } from './index.662e0e1f.js';
function w(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (o) {
        return Object.getOwnPropertyDescriptor(e, o).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function b(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? w(Object(t), !0).forEach(function (r) {
          d(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : w(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function $(e, n) {
  var t = f.exports.useContext(u),
    r = t.prefixCls,
    o = r === void 0 ? 'arco' : r,
    s = e.spin,
    i = e.className,
    a = b(
      b({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(i ? i + ' ' : '')
          .concat(o, '-icon ')
          .concat(o, '-icon-arrow-right'),
      }
    );
  return (
    s && (a.className = ''.concat(a.className, ' ').concat(o, '-icon-loading')),
    delete a.spin,
    delete a.isIcon,
    c('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...a,
      children: c('path', {
        d: 'm27.728 11.27 12.728 12.728-12.728 12.728M5 24h34.295',
      }),
    })
  );
}
var m = p.forwardRef($);
m.defaultProps = { isIcon: !0 };
m.displayName = 'IconArrowRight';
var R = m;
function y(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (o) {
        return Object.getOwnPropertyDescriptor(e, o).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function j(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? y(Object(t), !0).forEach(function (r) {
          d(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : y(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function E(e, n) {
  var t = f.exports.useContext(u),
    r = t.prefixCls,
    o = r === void 0 ? 'arco' : r,
    s = e.spin,
    i = e.className,
    a = j(
      j({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(i ? i + ' ' : '')
          .concat(o, '-icon ')
          .concat(o, '-icon-swap'),
      }
    );
  return (
    s && (a.className = ''.concat(a.className, ' ').concat(o, '-icon-loading')),
    delete a.spin,
    delete a.isIcon,
    c('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...a,
      children: c('path', {
        d: 'M5 17h35.586c.89 0 1.337-1.077.707-1.707L33 7M43 31H7.414c-.89 0-1.337 1.077-.707 1.707L15 41',
      }),
    })
  );
}
var g = p.forwardRef(E);
g.defaultProps = { isIcon: !0 };
g.displayName = 'IconSwap';
var B = g;
function C(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (o) {
        return Object.getOwnPropertyDescriptor(e, o).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function x(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? C(Object(t), !0).forEach(function (r) {
          d(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : C(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function L(e, n) {
  var t = f.exports.useContext(u),
    r = t.prefixCls,
    o = r === void 0 ? 'arco' : r,
    s = e.spin,
    i = e.className,
    a = x(
      x({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(i ? i + ' ' : '')
          .concat(o, '-icon ')
          .concat(o, '-icon-stop'),
      }
    );
  return (
    s && (a.className = ''.concat(a.className, ' ').concat(o, '-icon-loading')),
    delete a.spin,
    delete a.isIcon,
    c('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...a,
      children: c('path', {
        d: 'M36.728 36.728c7.03-7.03 7.03-18.427 0-25.456-7.03-7.03-18.427-7.03-25.456 0m25.456 25.456c-7.03 7.03-18.427 7.03-25.456 0-7.03-7.03-7.03-18.427 0-25.456m25.456 25.456L11.272 11.272',
      }),
    })
  );
}
var h = p.forwardRef(L);
h.defaultProps = { isIcon: !0 };
h.displayName = 'IconStop';
var T = h;
function P(e, n) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    n &&
      (r = r.filter(function (o) {
        return Object.getOwnPropertyDescriptor(e, o).enumerable;
      })),
      t.push.apply(t, r);
  }
  return t;
}
function I(e) {
  for (var n = 1; n < arguments.length; n++) {
    var t = arguments[n] != null ? arguments[n] : {};
    n % 2
      ? P(Object(t), !0).forEach(function (r) {
          d(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : P(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}
function q(e, n) {
  var t = f.exports.useContext(u),
    r = t.prefixCls,
    o = r === void 0 ? 'arco' : r,
    s = e.spin,
    i = e.className,
    a = I(
      I({ 'aria-hidden': !0, focusable: !1, ref: n }, e),
      {},
      {
        className: ''
          .concat(i ? i + ' ' : '')
          .concat(o, '-icon ')
          .concat(o, '-icon-tags'),
      }
    );
  return (
    s && (a.className = ''.concat(a.className, ' ').concat(o, '-icon-loading')),
    delete a.spin,
    delete a.isIcon,
    O('svg', {
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '4',
      viewBox: '0 0 48 48',
      ...a,
      children: [
        c('path', {
          d: 'm37.581 28.123-14.849 14.85a1 1 0 0 1-1.414 0L8.59 30.243m25.982-22.68-10.685-.628a1 1 0 0 0-.766.291L9.297 21.052a1 1 0 0 0 0 1.414L20.61 33.78a1 1 0 0 0 1.415 0l13.824-13.825a1 1 0 0 0 .291-.765l-.628-10.686a1 1 0 0 0-.94-.94Zm-6.874 7.729a1 1 0 1 1 1.414-1.414 1 1 0 0 1-1.414 1.414Z',
        }),
        c('path', {
          fill: 'currentColor',
          stroke: 'none',
          d: 'M27.697 15.292a1 1 0 1 1 1.414-1.414 1 1 0 0 1-1.414 1.414Z',
        }),
      ],
    })
  );
}
var v = p.forwardRef(q);
v.defaultProps = { isIcon: !0 };
v.displayName = 'IconTags';
var M = v;
function Z() {
  const e = N(_);
  return O(D, {
    children: [
      c(S.Title, {
        style: { marginTop: 0, marginBottom: 16 },
        heading: 6,
        children: e['monitor.title.quickOperation'],
      }),
      O(k, {
        direction: 'vertical',
        style: { width: '100%' },
        size: 10,
        children: [
          c(l, {
            long: !0,
            icon: c(M, {}),
            children: e['monitor.quickOperation.changeClarity'],
          }),
          c(l, {
            long: !0,
            icon: c(B, {}),
            children: e['monitor.quickOperation.switchStream'],
          }),
          c(l, {
            long: !0,
            icon: c(T, {}),
            children: e['monitor.quickOperation.removeClarity'],
          }),
          c(l, {
            long: !0,
            icon: c(R, {}),
            children: e['monitor.quickOperation.pushFlowGasket'],
          }),
        ],
      }),
    ],
  });
}
export { Z as default };
