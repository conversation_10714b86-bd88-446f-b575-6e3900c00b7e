import {
  b as u,
  r as m,
  aJ as l,
  a as r,
  F as s,
  R as f,
} from './index.7dafa16d.js';
const P = (e) => {
  const { backup: n, requiredPermissions: i, oneOfPerm: t } = e,
    a = u((c) => c.userInfo);
  return m.exports.useMemo(
    () => l({ requiredPermissions: i, oneOfPerm: t }, a.permissions),
    [t, i, a.permissions]
  )
    ? r(s, { children: o(e.children) })
    : n
    ? r(s, { children: o(n) })
    : null;
};
function o(e) {
  return f.isValidElement(e) ? e : r(s, { children: e });
}
export { P };
