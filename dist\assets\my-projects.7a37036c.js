import { r, a as s, a5 as u, l as f } from './index.7dafa16d.js';
import d from './project.239d454f.js';
import './index.43d26da3.js';
import './index.519f9d90.js';
function h() {
  const [t, n] = r.exports.useState(new Array(6).fill({})),
    [i, o] = r.exports.useState(!0),
    { Row: c, Col: l } = u,
    p = async () => {
      o(!0);
      const { data: a } = await f.get('/api/user/projectList').finally(() => {
        o(!1);
      });
      n(a);
    };
  return (
    r.exports.useEffect(() => {
      p();
    }, []),
    s(c, {
      gutter: 12,
      children: t.map((a, e) =>
        s(
          l,
          {
            span: 8,
            style:
              e > t.length - 4 && e < t.length ? { marginTop: '16px' } : {},
            children: s(d, { ...a, loading: i }),
          },
          e
        )
      ),
    })
  );
}
export { h as default };
